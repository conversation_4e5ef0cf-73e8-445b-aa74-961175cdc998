import { z } from 'zod'

export const StorySchema = z.object({
  id: z.string(),
  title: z.string(),
  chapter: z.string().optional(),
  language: z.string(),
  content: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  audioUrl: z.string().optional(),
  wordTimings: z.array(z.object({
    word: z.string(),
    startTime: z.number(),
    endTime: z.number(),
    confidence: z.number().optional()
  })).optional()
})

export const TranslationResultSchema = z.object({
  translation: z.string(),
  pronunciation: z.string().optional(),
  confidence: z.number().optional(),
  provider: z.string(),
  example: z.object({
    original: z.string(),
    translation: z.string()
  }).optional()
})

export type Story = z.infer<typeof StorySchema>
export type WordTiming = NonNullable<Story['wordTimings']>[0]
export type TranslationResult = z.infer<typeof TranslationResultSchema>

// UI State Types
export interface AudioPlayerState {
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
}

export interface TranslationCardState {
  isVisible: boolean
  word: string
  position: { x: number; y: number }
  translation?: TranslationResult
  isLoading: boolean
}

export interface StoryReaderState {
  currentWordIndex: number
  selectedWord?: string
  translationCard: TranslationCardState
  audioPlayer: AudioPlayerState
}
