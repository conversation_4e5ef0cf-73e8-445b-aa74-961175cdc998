'use client'

import { useState, useEffect, useCallback } from 'react'
import { progressService, UserProgress, ProgressStats, LearningSession } from '../services/progress-service'

export function useProgress(storyId?: string) {
  const [storyProgress, setStoryProgress] = useState<UserProgress | null>(null)
  const [overallStats, setOverallStats] = useState<ProgressStats | null>(null)

  // Load progress data
  useEffect(() => {
    if (storyId) {
      const progress = progressService.getStoryProgress(storyId)
      setStoryProgress(progress)
    }
    
    const stats = progressService.getProgressStats()
    setOverallStats(stats)
  }, [storyId])

  // Start a learning session (pure service call, no state updates)
  const startSession = useCallback((sessionStoryId: string) => {
    return progressService.startSession(sessionStoryId)
  }, [])

  // End the current session (pure service call, no state updates)
  const endSession = useCallback(() => {
    progressService.endSession()
  }, [])

  // Track word encounter
  const trackWordEncounter = useCallback((word: string) => {
    progressService.trackWordEncounter(word)
  }, [])

  // Track translation request
  const trackTranslationRequest = useCallback((word: string) => {
    progressService.trackTranslationRequest(word)
  }, [])

  // Track audio play time
  const trackAudioPlayTime = useCallback((seconds: number) => {
    progressService.trackAudioPlayTime(seconds)
  }, [])

  // Update completion percentage
  const updateCompletionPercentage = useCallback((percentage: number) => {
    progressService.updateCompletionPercentage(percentage)
  }, [])

  // Calculate reading progress based on current word position (without triggering updates)
  const calculateReadingProgress = useCallback((currentWordIndex: number, totalWords: number) => {
    if (totalWords === 0) return 0
    return Math.round((currentWordIndex / totalWords) * 100)
  }, [])

  // Refresh progress data manually
  const refreshProgress = useCallback(() => {
    if (storyId) {
      const progress = progressService.getStoryProgress(storyId)
      setStoryProgress(progress)
    }

    const stats = progressService.getProgressStats()
    setOverallStats(stats)
  }, [storyId])

  return {
    storyProgress,
    overallStats,
    startSession,
    endSession,
    trackWordEncounter,
    trackTranslationRequest,
    trackAudioPlayTime,
    updateCompletionPercentage,
    calculateReadingProgress,
    refreshProgress
  }
}
