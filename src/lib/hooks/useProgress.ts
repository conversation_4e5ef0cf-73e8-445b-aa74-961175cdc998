'use client'

import { useState, useEffect, useCallback } from 'react'
import { progressService, UserProgress, ProgressStats, LearningSession } from '../services/progress-service'

export function useProgress(storyId?: string) {
  const [storyProgress, setStoryProgress] = useState<UserProgress | null>(null)
  const [overallStats, setOverallStats] = useState<ProgressStats | null>(null)
  const [currentSession, setCurrentSession] = useState<LearningSession | null>(null)

  // Load progress data
  useEffect(() => {
    if (storyId) {
      const progress = progressService.getStoryProgress(storyId)
      setStoryProgress(progress)
    }
    
    const stats = progressService.getProgressStats()
    setOverallStats(stats)
  }, [storyId])

  // Start a learning session
  const startSession = useCallback((sessionStoryId: string) => {
    const session = progressService.startSession(sessionStoryId)
    setCurrentSession(session)
    return session
  }, [])

  // End the current session
  const endSession = useCallback(() => {
    progressService.endSession()
    setCurrentSession(null)
    
    // Refresh progress data
    if (storyId) {
      const progress = progressService.getStoryProgress(storyId)
      setStoryProgress(progress)
    }
    
    const stats = progressService.getProgressStats()
    setOverallStats(stats)
  }, [storyId])

  // Track word encounter
  const trackWordEncounter = useCallback((word: string) => {
    progressService.trackWordEncounter(word)
  }, [])

  // Track translation request
  const trackTranslationRequest = useCallback((word: string) => {
    progressService.trackTranslationRequest(word)
  }, [])

  // Track audio play time
  const trackAudioPlayTime = useCallback((seconds: number) => {
    progressService.trackAudioPlayTime(seconds)
  }, [])

  // Update completion percentage
  const updateCompletionPercentage = useCallback((percentage: number) => {
    progressService.updateCompletionPercentage(percentage)
  }, [])

  // Calculate reading progress based on current word position (without triggering updates)
  const calculateReadingProgress = useCallback((currentWordIndex: number, totalWords: number) => {
    if (totalWords === 0) return 0
    return Math.round((currentWordIndex / totalWords) * 100)
  }, [])

  return {
    storyProgress,
    overallStats,
    currentSession,
    startSession,
    endSession,
    trackWordEncounter,
    trackTranslationRequest,
    trackAudioPlayTime,
    updateCompletionPercentage,
    calculateReadingProgress
  }
}
