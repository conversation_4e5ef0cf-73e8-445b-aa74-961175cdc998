'use client'

import { useEffect } from 'react'

interface KeyboardShortcuts {
  onPlayPause?: () => void
  onSeekForward?: () => void
  onSeekBackward?: () => void
  onCloseTranslation?: () => void
}

export function useKeyboardShortcuts({
  onPlayPause,
  onSeekForward,
  onSeekBackward,
  onCloseTranslation
}: KeyboardShortcuts) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts if user is typing in an input
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      switch (event.code) {
        case 'Space':
          event.preventDefault()
          onPlayPause?.()
          break
        case 'ArrowRight':
          if (event.shiftKey) {
            event.preventDefault()
            onSeekForward?.()
          }
          break
        case 'ArrowLeft':
          if (event.shiftKey) {
            event.preventDefault()
            onSeekBackward?.()
          }
          break
        case 'Escape':
          event.preventDefault()
          onCloseTranslation?.()
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onPlayPause, onSeekForward, onSeekBackward, onCloseTranslation])
}
