'use client'

import { useState, useRef, useEffect, useCallback } from 'react'
import { AudioPlayerState, WordTiming } from '../types'

interface UseAudioPlayerProps {
  audioUrl?: string
  wordTimings?: WordTiming[]
  onWordHighlight?: (wordIndex: number) => void
}

export function useAudioPlayer({ 
  audioUrl, 
  wordTimings = [], 
  onWordHighlight 
}: UseAudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const [state, setState] = useState<AudioPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1.0
  })

  // Initialize audio element
  useEffect(() => {
    if (!audioUrl) return

    const audio = new Audio(audioUrl)
    audioRef.current = audio

    const handleLoadedMetadata = () => {
      setState(prev => ({
        ...prev,
        duration: audio.duration || 0
      }))
    }

    const handleTimeUpdate = () => {
      setState(prev => ({
        ...prev,
        currentTime: audio.currentTime
      }))

      // Find current word based on time
      if (wordTimings.length > 0 && onWordHighlight) {
        const currentWordIndex = wordTimings.findIndex(
          timing => audio.currentTime >= timing.startTime && audio.currentTime <= timing.endTime
        )
        if (currentWordIndex !== -1) {
          onWordHighlight(currentWordIndex)
        }
      }
    }

    const handleEnded = () => {
      setState(prev => ({
        ...prev,
        isPlaying: false,
        currentTime: 0
      }))
    }

    const handleError = (e: Event) => {
      console.error('Audio error:', e)
      setState(prev => ({
        ...prev,
        isPlaying: false
      }))
    }

    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('ended', handleEnded)
    audio.addEventListener('error', handleError)

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('ended', handleEnded)
      audio.removeEventListener('error', handleError)
      audio.pause()
    }
  }, [audioUrl, wordTimings, onWordHighlight])

  const play = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.play()
      setState(prev => ({ ...prev, isPlaying: true }))
    }
  }, [])

  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
      setState(prev => ({ ...prev, isPlaying: false }))
    }
  }, [])

  const togglePlayPause = useCallback(() => {
    if (state.isPlaying) {
      pause()
    } else {
      play()
    }
  }, [state.isPlaying, play, pause])

  const seek = useCallback((time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time
      setState(prev => ({ ...prev, currentTime: time }))
    }
  }, [])

  const setVolume = useCallback((volume: number) => {
    if (audioRef.current) {
      audioRef.current.volume = volume
      setState(prev => ({ ...prev, volume }))
    }
  }, [])

  return {
    state,
    play,
    pause,
    togglePlayPause,
    seek,
    setVolume,
    isReady: !!audioRef.current && state.duration > 0
  }
}
