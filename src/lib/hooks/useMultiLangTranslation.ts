'use client'

import { useState, useCallback } from 'react'
import { MultiLangTranslation, LanguageCode, SupportedLanguages } from '../types-multilang'

// Enhanced mock translation service with multiple languages
const multiLangTranslationService = {
  async translateWord(
    word: string, 
    fromLang: LanguageCode, 
    toLang: LanguageCode
  ): Promise<MultiLangTranslation> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 600))
    
    // Multi-language translation database
    const translations: Record<string, Record<string, Partial<MultiLangTranslation>>> = {
      // English words
      'plane': {
        'fr': { translation: 'avion', pronunciation: 'a-vi-ɔ̃', partOfSpeech: 'noun', gender: 'masculine' },
        'es': { translation: 'avión', pronunciation: 'a-ˈβjon', partOfSpeech: 'noun', gender: 'masculine' },
        'de': { translation: 'Flugzeug', pronunciation: 'ˈfluːktsɔʏk', partOfSpeech: 'noun', gender: 'neuter' },
        'it': { translation: 'aereo', pronunciation: 'a-ˈɛ-re-o', partOfSpeech: 'noun', gender: 'masculine' }
      },
      'time': {
        'fr': { translation: 'temps', pronunciation: 'tɑ̃', partOfSpeech: 'noun', gender: 'masculine' },
        'es': { translation: 'tiempo', pronunciation: 'ˈtjem.po', partOfSpeech: 'noun', gender: 'masculine' },
        'de': { translation: 'Zeit', pronunciation: 'tsaɪt', partOfSpeech: 'noun', gender: 'feminine' },
        'it': { translation: 'tempo', pronunciation: 'ˈtem.po', partOfSpeech: 'noun', gender: 'masculine' }
      },
      'world': {
        'fr': { translation: 'monde', pronunciation: 'mɔ̃d', partOfSpeech: 'noun', gender: 'masculine' },
        'es': { translation: 'mundo', pronunciation: 'ˈmun.do', partOfSpeech: 'noun', gender: 'masculine' },
        'de': { translation: 'Welt', pronunciation: 'vɛlt', partOfSpeech: 'noun', gender: 'feminine' },
        'it': { translation: 'mondo', pronunciation: 'ˈmon.do', partOfSpeech: 'noun', gender: 'masculine' }
      },
      // French words (reverse translations)
      'avion': {
        'en': { translation: 'plane', pronunciation: 'pleɪn', partOfSpeech: 'noun' },
        'es': { translation: 'avión', pronunciation: 'a-ˈβjon', partOfSpeech: 'noun', gender: 'masculine' },
        'de': { translation: 'Flugzeug', pronunciation: 'ˈfluːktsɔʏk', partOfSpeech: 'noun', gender: 'neuter' }
      },
      'temps': {
        'en': { translation: 'time', pronunciation: 'taɪm', partOfSpeech: 'noun' },
        'es': { translation: 'tiempo', pronunciation: 'ˈtjem.po', partOfSpeech: 'noun', gender: 'masculine' },
        'de': { translation: 'Zeit', pronunciation: 'tsaɪt', partOfSpeech: 'noun', gender: 'feminine' }
      }
    }
    
    const cleanWord = word.toLowerCase().replace(/[.,!?;:]/, '')
    const wordTranslations = translations[cleanWord]
    const targetTranslation = wordTranslations?.[toLang]
    
    if (targetTranslation) {
      return {
        word,
        sourceLanguage: fromLang,
        targetLanguage: toLang,
        translation: targetTranslation.translation!,
        pronunciation: targetTranslation.pronunciation,
        partOfSpeech: targetTranslation.partOfSpeech,
        gender: targetTranslation.gender,
        provider: 'multi-lang-mock',
        confidence: 0.95,
        example: {
          original: `Example with "${word}" in ${SupportedLanguages[fromLang].name}`,
          translation: `Exemple avec "${targetTranslation.translation}" en ${SupportedLanguages[toLang].name}`
        }
      }
    }
    
    // Fallback for unknown words
    return {
      word,
      sourceLanguage: fromLang,
      targetLanguage: toLang,
      translation: `[${word} → ${SupportedLanguages[toLang].name}]`,
      provider: 'multi-lang-mock',
      confidence: 0.3
    }
  }
}

interface UseMultiLangTranslationState {
  isLoading: boolean
  error: string | null
  lastTranslation: MultiLangTranslation | null
  supportedLanguages: typeof SupportedLanguages
}

export function useMultiLangTranslation() {
  const [state, setState] = useState<UseMultiLangTranslationState>({
    isLoading: false,
    error: null,
    lastTranslation: null,
    supportedLanguages: SupportedLanguages
  })

  const translateWord = useCallback(async (
    word: string, 
    fromLang: LanguageCode = 'en', 
    toLang: LanguageCode = 'fr'
  ): Promise<MultiLangTranslation | null> => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }))

    try {
      const result = await multiLangTranslationService.translateWord(word, fromLang, toLang)
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        lastTranslation: result
      }))
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Translation failed'
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      return null
    }
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  const isLanguageSupported = useCallback((langCode: string): boolean => {
    return langCode in SupportedLanguages
  }, [])

  return {
    ...state,
    translateWord,
    clearError,
    isLanguageSupported
  }
}
