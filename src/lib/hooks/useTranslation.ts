'use client'

import { useState, useCallback } from 'react'
import { TranslationResult } from '../types'

// Mock translation service - in a real app this would call an API
const mockTranslationService = {
  async translateWord(word: string, fromLang: string, toLang: string): Promise<TranslationResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // Enhanced mock translations with more examples
    const mockTranslations: Record<string, TranslationResult> = {
      'plane': {
        translation: 'avion',
        pronunciation: 'a-vi-ɔ̃',
        provider: 'mock-translate',
        example: {
          original: 'I like riding on planes.',
          translation: 'J\'aime voyager en avion.'
        }
      },
      'aircraft': {
        translation: 'aéronef',
        pronunciation: 'a-e-ʁo-nɛf',
        provider: 'mock-translate',
        example: {
          original: 'The aircraft landed safely.',
          translation: 'L\'aéronef a atterri en sécurité.'
        }
      },
      'huge': {
        translation: 'énorme',
        pronunciation: 'e-nɔʁm',
        provider: 'mock-translate',
        example: {
          original: 'That\'s a huge building.',
          translation: 'C\'est un énorme bâtiment.'
        }
      },
      'cloud': {
        translation: 'nuage',
        pronunciation: 'ny-aʒ',
        provider: 'mock-translate',
        example: {
          original: 'The cloud is white.',
          translation: 'Le nuage est blanc.'
        }
      },
      'airport': {
        translation: 'aéroport',
        pronunciation: 'a-e-ʁo-pɔʁ',
        provider: 'mock-translate',
        example: {
          original: 'We arrived at the airport.',
          translation: 'Nous sommes arrivés à l\'aéroport.'
        }
      },
      'seat': {
        translation: 'siège',
        pronunciation: 'sjɛʒ',
        provider: 'mock-translate',
        example: {
          original: 'Please take a seat.',
          translation: 'Veuillez prendre un siège.'
        }
      },
      'time': {
        translation: 'temps',
        pronunciation: 'tɑ̃',
        provider: 'mock-translate',
        example: {
          original: 'What time is it?',
          translation: 'Quelle heure est-il?'
        }
      },
      'world': {
        translation: 'monde',
        pronunciation: 'mɔ̃d',
        provider: 'mock-translate',
        example: {
          original: 'The world is beautiful.',
          translation: 'Le monde est beau.'
        }
      }
    }
    
    const cleanWord = word.toLowerCase().replace(/[.,!?;:]/, '')
    const translation = mockTranslations[cleanWord]
    
    if (translation) {
      return translation
    }
    
    // Fallback for unknown words
    return {
      translation: `[Translation for "${word}" not available]`,
      provider: 'mock-translate',
      confidence: 0.5
    }
  }
}

interface UseTranslationState {
  isLoading: boolean
  error: string | null
  lastTranslation: TranslationResult | null
}

export function useTranslation() {
  const [state, setState] = useState<UseTranslationState>({
    isLoading: false,
    error: null,
    lastTranslation: null
  })

  const translateWord = useCallback(async (
    word: string, 
    fromLang: string = 'en', 
    toLang: string = 'fr'
  ): Promise<TranslationResult | null> => {
    setState(prev => ({
      ...prev,
      isLoading: true,
      error: null
    }))

    try {
      const result = await mockTranslationService.translateWord(word, fromLang, toLang)
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        lastTranslation: result
      }))
      
      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Translation failed'
      
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }))
      
      return null
    }
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  return {
    ...state,
    translateWord,
    clearError
  }
}
