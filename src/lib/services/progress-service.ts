// User Progress Tracking Service
// This service handles user progress, statistics, and learning analytics

export interface UserProgress {
  userId: string
  storyId: string
  wordsLearned: string[]
  completionPercentage: number
  timeSpent: number // in seconds
  lastAccessedAt: string
  startedAt: string
  completedAt?: string
  statistics: {
    totalWords: number
    uniqueWordsEncountered: number
    translationsRequested: number
    audioPlayTime: number
    sessionsCount: number
  }
}

export interface LearningSession {
  sessionId: string
  storyId: string
  startTime: string
  endTime?: string
  wordsEncountered: string[]
  translationsRequested: string[]
  audioPlayTime: number
  completionPercentage: number
}

export interface ProgressStats {
  totalStoriesStarted: number
  totalStoriesCompleted: number
  totalWordsLearned: number
  totalTimeSpent: number
  averageCompletionRate: number
  streakDays: number
  lastActiveDate: string
}

class ProgressService {
  private storageKey = 'polistory-progress'
  private sessionKey = 'polistory-current-session'

  // Get user's overall progress statistics
  getProgressStats(): ProgressStats {
    const allProgress = this.getAllProgress()
    const now = new Date()
    const today = now.toDateString()

    const totalStoriesStarted = allProgress.length
    const totalStoriesCompleted = allProgress.filter(p => p.completedAt).length
    const totalWordsLearned = [...new Set(allProgress.flatMap(p => p.wordsLearned))].length
    const totalTimeSpent = allProgress.reduce((sum, p) => sum + p.timeSpent, 0)
    const averageCompletionRate = totalStoriesStarted > 0 
      ? allProgress.reduce((sum, p) => sum + p.completionPercentage, 0) / totalStoriesStarted 
      : 0

    // Calculate streak (simplified - just check if user was active today)
    const lastActiveDate = allProgress.length > 0 
      ? allProgress.reduce((latest, p) => 
          new Date(p.lastAccessedAt) > new Date(latest) ? p.lastAccessedAt : latest
        , allProgress[0].lastAccessedAt)
      : today

    const streakDays = new Date(lastActiveDate).toDateString() === today ? 1 : 0

    return {
      totalStoriesStarted,
      totalStoriesCompleted,
      totalWordsLearned,
      totalTimeSpent,
      averageCompletionRate,
      streakDays,
      lastActiveDate
    }
  }

  // Get progress for a specific story
  getStoryProgress(storyId: string): UserProgress | null {
    const allProgress = this.getAllProgress()
    return allProgress.find(p => p.storyId === storyId) || null
  }

  // Start a new learning session
  startSession(storyId: string): LearningSession {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const session: LearningSession = {
      sessionId,
      storyId,
      startTime: new Date().toISOString(),
      wordsEncountered: [],
      translationsRequested: [],
      audioPlayTime: 0,
      completionPercentage: 0
    }

    localStorage.setItem(this.sessionKey, JSON.stringify(session))
    return session
  }

  // Update current session
  updateSession(updates: Partial<LearningSession>): void {
    const currentSession = this.getCurrentSession()
    if (!currentSession) return

    const updatedSession = { ...currentSession, ...updates }
    localStorage.setItem(this.sessionKey, JSON.stringify(updatedSession))
  }

  // End current session and update progress
  endSession(): void {
    const session = this.getCurrentSession()
    if (!session) return

    const endTime = new Date().toISOString()
    const sessionDuration = new Date(endTime).getTime() - new Date(session.startTime).getTime()
    const sessionDurationSeconds = Math.floor(sessionDuration / 1000)

    // Update or create progress record
    let progress = this.getStoryProgress(session.storyId)
    
    if (!progress) {
      progress = {
        userId: 'default-user', // In a real app, this would be the actual user ID
        storyId: session.storyId,
        wordsLearned: [],
        completionPercentage: 0,
        timeSpent: 0,
        lastAccessedAt: endTime,
        startedAt: session.startTime,
        statistics: {
          totalWords: 0,
          uniqueWordsEncountered: 0,
          translationsRequested: 0,
          audioPlayTime: 0,
          sessionsCount: 0
        }
      }
    }

    // Update progress with session data
    const newWordsLearned = session.translationsRequested.filter(
      word => !progress!.wordsLearned.includes(word)
    )
    
    progress.wordsLearned = [...progress.wordsLearned, ...newWordsLearned]
    progress.completionPercentage = Math.max(progress.completionPercentage, session.completionPercentage)
    progress.timeSpent += sessionDurationSeconds
    progress.lastAccessedAt = endTime
    progress.statistics.uniqueWordsEncountered = [...new Set([
      ...session.wordsEncountered,
      ...progress.wordsLearned
    ])].length
    progress.statistics.translationsRequested += session.translationsRequested.length
    progress.statistics.audioPlayTime += session.audioPlayTime
    progress.statistics.sessionsCount += 1

    // Mark as completed if 90% or more
    if (progress.completionPercentage >= 90 && !progress.completedAt) {
      progress.completedAt = endTime
    }

    this.saveStoryProgress(progress)
    localStorage.removeItem(this.sessionKey)
  }

  // Track word encounter
  trackWordEncounter(word: string): void {
    const session = this.getCurrentSession()
    if (!session) return

    if (!session.wordsEncountered.includes(word)) {
      session.wordsEncountered.push(word)
      this.updateSession({ wordsEncountered: session.wordsEncountered })
    }
  }

  // Track translation request
  trackTranslationRequest(word: string): void {
    const session = this.getCurrentSession()
    if (!session) return

    if (!session.translationsRequested.includes(word)) {
      session.translationsRequested.push(word)
      this.updateSession({ translationsRequested: session.translationsRequested })
    }
  }

  // Track audio play time
  trackAudioPlayTime(seconds: number): void {
    const session = this.getCurrentSession()
    if (!session) return

    this.updateSession({ audioPlayTime: session.audioPlayTime + seconds })
  }

  // Update completion percentage
  updateCompletionPercentage(percentage: number): void {
    const session = this.getCurrentSession()
    if (!session) return

    this.updateSession({ completionPercentage: Math.max(session.completionPercentage, percentage) })
  }

  // Private helper methods
  private getCurrentSession(): LearningSession | null {
    const sessionData = localStorage.getItem(this.sessionKey)
    return sessionData ? JSON.parse(sessionData) : null
  }

  private getAllProgress(): UserProgress[] {
    const progressData = localStorage.getItem(this.storageKey)
    return progressData ? JSON.parse(progressData) : []
  }

  private saveStoryProgress(progress: UserProgress): void {
    const allProgress = this.getAllProgress()
    const existingIndex = allProgress.findIndex(p => p.storyId === progress.storyId)
    
    if (existingIndex >= 0) {
      allProgress[existingIndex] = progress
    } else {
      allProgress.push(progress)
    }
    
    localStorage.setItem(this.storageKey, JSON.stringify(allProgress))
  }
}

export const progressService = new ProgressService()
