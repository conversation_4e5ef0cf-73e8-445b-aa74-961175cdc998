import { Story, WordTiming } from '../types'
import { ttsService, TTSOptions } from './tts-service'

export interface ProcessStoryOptions {
  voice?: string
  speed?: number
  pitch?: number
  language?: string
  generateAudio?: boolean
}

export interface ProcessedStory extends Story {
  audioUrl: string
  wordTimings: WordTiming[]
  processingMetadata: {
    processedAt: string
    audioGenerated: boolean
    voice?: string
    duration: number
  }
}

class StoryProcessor {
  async processStory(
    story: Omit<Story, 'audioUrl' | 'wordTimings'>, 
    options: ProcessStoryOptions = {}
  ): Promise<ProcessedStory> {
    const {
      voice,
      speed = 1.0,
      pitch = 1.0,
      language = 'fr-FR', // Default to French for language learning
      generateAudio = true
    } = options

    console.log(`Processing story: ${story.title}`)

    let audioUrl = ''
    let wordTimings: WordTiming[] = []
    let duration = 0

    if (generateAudio) {
      try {
        console.log('Generating audio...')
        const audioResult = await ttsService.generateMockAudio(story.content)
        
        audioUrl = audioResult.audioUrl
        wordTimings = audioResult.wordTimings || []
        duration = audioResult.duration

        console.log(`Audio generated: ${duration}s, ${wordTimings.length} word timings`)
      } catch (error) {
        console.error('Audio generation failed:', error)
        // Continue without audio
        audioUrl = ''
        wordTimings = this.createFallbackTimings(story.content)
        duration = this.estimateDuration(story.content)
      }
    } else {
      // Create estimated timings without audio
      wordTimings = this.createFallbackTimings(story.content)
      duration = this.estimateDuration(story.content)
    }

    return {
      ...story,
      audioUrl,
      wordTimings,
      processingMetadata: {
        processedAt: new Date().toISOString(),
        audioGenerated: generateAudio && audioUrl !== '',
        voice,
        duration
      }
    }
  }

  private createFallbackTimings(text: string): WordTiming[] {
    const words = text.split(/\s+/)
    let currentTime = 0
    
    return words.map(word => {
      const cleanWord = word.replace(/[.,!?;:]/, '')
      const duration = Math.max(0.3, cleanWord.length * 0.08)
      
      const timing: WordTiming = {
        word: cleanWord,
        startTime: parseFloat(currentTime.toFixed(2)),
        endTime: parseFloat((currentTime + duration).toFixed(2)),
        confidence: 0.7 // Lower confidence for estimated timings
      }
      
      currentTime += duration + 0.1
      return timing
    })
  }

  private estimateDuration(text: string): number {
    // Rough estimation: average reading speed is about 200 words per minute
    // For language learning, we slow it down to about 120 words per minute
    const words = text.split(/\s+/).length
    const wordsPerMinute = 120
    return (words / wordsPerMinute) * 60
  }

  async reprocessStoryAudio(
    story: Story, 
    options: TTSOptions = {}
  ): Promise<ProcessedStory> {
    console.log(`Reprocessing audio for story: ${story.title}`)
    
    try {
      const audioResult = await ttsService.generateMockAudio(story.content)
      
      return {
        ...story,
        audioUrl: audioResult.audioUrl,
        wordTimings: audioResult.wordTimings || story.wordTimings || [],
        processingMetadata: {
          processedAt: new Date().toISOString(),
          audioGenerated: true,
          voice: options.voice,
          duration: audioResult.duration
        }
      }
    } catch (error) {
      console.error('Audio reprocessing failed:', error)
      throw new Error(`Failed to reprocess audio: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  validateStory(story: Partial<Story>): string[] {
    const errors: string[] = []

    if (!story.title || story.title.trim().length === 0) {
      errors.push('Title is required')
    }

    if (!story.content || story.content.trim().length === 0) {
      errors.push('Content is required')
    }

    if (story.content && story.content.length > 10000) {
      errors.push('Content is too long (maximum 10,000 characters)')
    }

    if (!story.language || story.language.trim().length === 0) {
      errors.push('Language is required')
    }

    if (!story.difficulty || !['beginner', 'intermediate', 'advanced'].includes(story.difficulty)) {
      errors.push('Valid difficulty level is required (beginner, intermediate, or advanced)')
    }

    return errors
  }
}

export const storyProcessor = new StoryProcessor()
