// Text-to-Speech Service
// This service handles audio generation and word alignment

export interface TTSOptions {
  voice?: string
  speed?: number
  pitch?: number
  language?: string
}

export interface AudioGenerationResult {
  audioUrl: string
  duration: number
  wordTimings?: Array<{
    word: string
    startTime: number
    endTime: number
    confidence?: number
  }>
}

class TTSService {
  private baseUrl = '/api/tts'

  async generateAudio(
    text: string, 
    options: TTSOptions = {}
  ): Promise<AudioGenerationResult> {
    const response = await fetch(`${this.baseUrl}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text,
        ...options
      })
    })

    if (!response.ok) {
      throw new Error(`TTS generation failed: ${response.statusText}`)
    }

    return response.json()
  }

  async getAvailableVoices(language?: string): Promise<Array<{
    id: string
    name: string
    language: string
    gender: 'male' | 'female' | 'neutral'
  }>> {
    const params = language ? `?language=${language}` : ''
    const response = await fetch(`${this.baseUrl}/voices${params}`)
    
    if (!response.ok) {
      throw new Error(`Failed to fetch voices: ${response.statusText}`)
    }

    return response.json()
  }

  // For development: create a mock audio file with estimated timings
  async generateMockAudio(text: string): Promise<AudioGenerationResult> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Create estimated word timings (roughly 0.5 seconds per word)
    const words = text.split(/\s+/)
    let currentTime = 0
    const wordTimings = words.map(word => {
      const duration = Math.max(0.3, word.length * 0.1) // Minimum 0.3s, scale with word length
      const timing = {
        word,
        startTime: currentTime,
        endTime: currentTime + duration,
        confidence: 0.8
      }
      currentTime += duration + 0.1 // Small pause between words
      return timing
    })

    // For now, return a mock result with Web Speech API synthesis
    return {
      audioUrl: await this.createWebSpeechAudio(text),
      duration: currentTime,
      wordTimings
    }
  }

  private async createWebSpeechAudio(text: string): Promise<string> {
    return new Promise((resolve, reject) => {
      if (!('speechSynthesis' in window)) {
        reject(new Error('Speech synthesis not supported'))
        return
      }

      const utterance = new SpeechSynthesisUtterance(text)
      utterance.rate = 0.8
      utterance.pitch = 1.0
      utterance.volume = 1.0

      // Try to use a French voice if available
      const voices = speechSynthesis.getVoices()
      const frenchVoice = voices.find(voice => 
        voice.lang.startsWith('fr') || voice.name.toLowerCase().includes('french')
      )
      if (frenchVoice) {
        utterance.voice = frenchVoice
        utterance.lang = 'fr-FR'
      }

      // Create a data URL for the audio (this is a simplified approach)
      // In a real implementation, you'd record the audio and create a blob
      utterance.onend = () => {
        // For now, return a placeholder URL
        // In production, this would be a real audio file URL
        resolve('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
      }

      utterance.onerror = (event) => {
        reject(new Error(`Speech synthesis failed: ${event.error}`))
      }

      speechSynthesis.speak(utterance)
    })
  }
}

export const ttsService = new TTSService()
