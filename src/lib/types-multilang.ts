// Enhanced types for multi-language support
import { z } from 'zod'

// Language configuration
export const SupportedLanguages = {
  'en': { name: 'English', flag: '🇺🇸', rtl: false },
  'fr': { name: 'Français', flag: '🇫🇷', rtl: false },
  'es': { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸', rtl: false },
  'de': { name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪', rtl: false },
  'it': { name: 'Italiano', flag: '🇮🇹', rtl: false },
  'pt': { name: 'Português', flag: '🇵🇹', rtl: false },
  'ar': { name: 'العربية', flag: '🇸🇦', rtl: true },
  'zh': { name: '中文', flag: '🇨🇳', rtl: false },
  'ja': { name: '日本語', flag: '🇯🇵', rtl: false },
  'ko': { name: '한국어', flag: '🇰🇷', rtl: false }
} as const

export type LanguageCode = keyof typeof SupportedLanguages
export type LanguageInfo = typeof SupportedLanguages[LanguageCode]

// Enhanced story schema with language support
export const MultiLangStorySchema = z.object({
  id: z.string(),
  title: z.string(),
  chapter: z.string().optional(),
  sourceLanguage: z.string(), // Language of the story content
  targetLanguage: z.string(), // Language user wants to learn
  content: z.string(),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  audioUrl: z.string().optional(),
  wordTimings: z.array(z.object({
    word: z.string(),
    startTime: z.number(),
    endTime: z.number(),
    confidence: z.number().optional()
  })).optional(),
  // Multi-language metadata
  translations: z.record(z.string()).optional(), // Pre-computed translations
  tags: z.array(z.string()).optional(),
  author: z.string().optional()
})

// Enhanced translation result with language context
export const MultiLangTranslationSchema = z.object({
  word: z.string(),
  sourceLanguage: z.string(),
  targetLanguage: z.string(),
  translation: z.string(),
  pronunciation: z.string().optional(),
  confidence: z.number().optional(),
  provider: z.string(),
  alternatives: z.array(z.string()).optional(), // Alternative translations
  partOfSpeech: z.string().optional(), // noun, verb, adjective, etc.
  gender: z.string().optional(), // for languages with gendered nouns
  example: z.object({
    original: z.string(),
    translation: z.string()
  }).optional(),
  // Language-specific features
  conjugations: z.array(z.object({
    form: z.string(),
    translation: z.string()
  })).optional(),
  culturalNotes: z.string().optional()
})

// User language preferences
export const UserLanguagePreferencesSchema = z.object({
  nativeLanguage: z.string(),
  learningLanguages: z.array(z.string()),
  interfaceLanguage: z.string(),
  translationDirection: z.enum(['to-native', 'to-learning', 'bidirectional'])
})

export type MultiLangStory = z.infer<typeof MultiLangStorySchema>
export type MultiLangTranslation = z.infer<typeof MultiLangTranslationSchema>
export type UserLanguagePreferences = z.infer<typeof UserLanguagePreferencesSchema>

// Language detection utilities
export const LanguageUtils = {
  isRTL: (langCode: string): boolean => {
    return SupportedLanguages[langCode as LanguageCode]?.rtl || false
  },
  
  getLanguageName: (langCode: string): string => {
    return SupportedLanguages[langCode as LanguageCode]?.name || langCode
  },
  
  getLanguageFlag: (langCode: string): string => {
    return SupportedLanguages[langCode as LanguageCode]?.flag || '🌐'
  },
  
  // Common language pairs for learning
  getCommonPairs: () => [
    { source: 'en', target: 'fr', name: 'English → French' },
    { source: 'en', target: 'es', name: 'English → Spanish' },
    { source: 'en', target: 'de', name: 'English → German' },
    { source: 'fr', target: 'en', name: 'French → English' },
    { source: 'es', target: 'en', name: 'Spanish → English' },
    { source: 'zh', target: 'en', name: 'Chinese → English' },
    { source: 'ja', target: 'en', name: 'Japanese → English' }
  ]
}
