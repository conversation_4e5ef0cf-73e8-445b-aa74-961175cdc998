import { NextRequest, NextResponse } from 'next/server'

const AVAILABLE_VOICES = [
  {
    id: 'en-US-neural-aria',
    name: '<PERSON> (English US)',
    language: 'en-US',
    gender: 'female' as const,
    description: 'Clear, natural female voice'
  },
  {
    id: 'en-US-neural-guy',
    name: '<PERSON> (English US)',
    language: 'en-US',
    gender: 'male' as const,
    description: 'Professional male voice'
  },
  {
    id: 'fr-FR-neural-denise',
    name: '<PERSON> (French)',
    language: 'fr-FR',
    gender: 'female' as const,
    description: 'Native French female voice'
  },
  {
    id: 'fr-FR-neural-henri',
    name: '<PERSON> (French)',
    language: 'fr-FR',
    gender: 'male' as const,
    description: 'Native French male voice'
  },
  {
    id: 'es-ES-neural-elvira',
    name: '<PERSON><PERSON><PERSON> (Spanish)',
    language: 'es-ES',
    gender: 'female' as const,
    description: 'Native Spanish female voice'
  },
  {
    id: 'de-DE-neural-katja',
    name: '<PERSON><PERSON> (German)',
    language: 'de-DE',
    gender: 'female' as const,
    description: 'Native German female voice'
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const language = searchParams.get('language')

    let voices = AVAILABLE_VOICES

    // Filter by language if specified
    if (language) {
      voices = voices.filter(voice => 
        voice.language.toLowerCase().startsWith(language.toLowerCase())
      )
    }

    return NextResponse.json(voices)
  } catch (error) {
    console.error('Error fetching voices:', error)
    return NextResponse.json(
      { error: 'Failed to fetch voices' }, 
      { status: 500 }
    )
  }
}
