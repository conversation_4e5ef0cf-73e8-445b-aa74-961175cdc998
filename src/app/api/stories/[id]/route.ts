import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs/promises'
import path from 'path'

// Get single story
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const storiesPath = path.join(process.cwd(), 'src/data/stories.json')
    const storiesData = await fs.readFile(storiesPath, 'utf-8')
    const stories = JSON.parse(storiesData)
    
    const story = stories.find((s: any) => s.id === params.id)
    
    if (!story) {
      return NextResponse.json(
        { error: 'Story not found' }, 
        { status: 404 }
      )
    }
    
    return NextResponse.json(story)
  } catch (error) {
    console.error('Error reading story:', error)
    return NextResponse.json(
      { error: 'Failed to load story' }, 
      { status: 500 }
    )
  }
}

// Delete story
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const storiesPath = path.join(process.cwd(), 'src/data/stories.json')
    const storiesData = await fs.readFile(storiesPath, 'utf-8')
    const stories = JSON.parse(storiesData)
    
    const storyIndex = stories.findIndex((s: any) => s.id === params.id)
    
    if (storyIndex === -1) {
      return NextResponse.json(
        { error: 'Story not found' }, 
        { status: 404 }
      )
    }
    
    // Remove the story
    const deletedStory = stories.splice(storyIndex, 1)[0]
    
    // Save updated stories
    await fs.writeFile(storiesPath, JSON.stringify(stories, null, 2))
    
    return NextResponse.json({ 
      message: 'Story deleted successfully',
      deletedStory 
    })
  } catch (error) {
    console.error('Error deleting story:', error)
    return NextResponse.json(
      { error: 'Failed to delete story' }, 
      { status: 500 }
    )
  }
}
