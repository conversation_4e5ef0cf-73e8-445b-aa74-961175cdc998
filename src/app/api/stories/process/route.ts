import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { storyProcessor } from '@/lib/services/story-processor'

const ProcessStorySchema = z.object({
  title: z.string().min(1),
  content: z.string().min(1),
  language: z.string().min(2),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),
  chapter: z.string().optional(),
  voice: z.string().optional(),
  speed: z.number().min(0.1).max(2.0).optional().default(1.0),
  pitch: z.number().min(0.1).max(2.0).optional().default(1.0),
  generateAudio: z.boolean().optional().default(true)
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const data = ProcessStorySchema.parse(body)

    // Validate the story
    const validationErrors = storyProcessor.validateStory(data)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Story validation failed', details: validationErrors }, 
        { status: 400 }
      )
    }

    // Generate a temporary ID for processing
    const tempId = `temp-${Date.now()}`
    
    // Process the story
    const processedStory = await storyProcessor.processStory(
      { ...data, id: tempId },
      {
        voice: data.voice,
        speed: data.speed,
        pitch: data.pitch,
        language: data.language,
        generateAudio: data.generateAudio
      }
    )

    // Return the processed story data (without saving)
    return NextResponse.json({
      story: processedStory,
      preview: true,
      message: 'Story processed successfully. Use POST /api/stories to save.'
    })

  } catch (error) {
    console.error('Story processing error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors }, 
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Story processing failed', details: error instanceof Error ? error.message : 'Unknown error' }, 
      { status: 500 }
    )
  }
}
