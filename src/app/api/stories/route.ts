import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { StorySchema } from '@/lib/types'
import { storyProcessor } from '@/lib/services/story-processor'
import fs from 'fs/promises'
import path from 'path'

const CreateStorySchema = StorySchema.omit({ 
  id: true, 
  audioUrl: true, 
  wordTimings: true 
}).extend({
  generateAudio: z.boolean().optional().default(true),
  voice: z.string().optional(),
  speed: z.number().min(0.1).max(2.0).optional(),
  pitch: z.number().min(0.1).max(2.0).optional()
})

// Get all stories
export async function GET() {
  try {
    const storiesPath = path.join(process.cwd(), 'src/data/stories.json')
    const storiesData = await fs.readFile(storiesPath, 'utf-8')
    const stories = JSON.parse(storiesData)
    
    return NextResponse.json(stories)
  } catch (error) {
    console.error('Error reading stories:', error)
    return NextResponse.json(
      { error: 'Failed to load stories' }, 
      { status: 500 }
    )
  }
}

// Create new story
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const storyData = CreateStorySchema.parse(body)

    // Generate unique ID
    const id = `story-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    // Validate story content
    const validationErrors = storyProcessor.validateStory(storyData)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors }, 
        { status: 400 }
      )
    }

    // Process the story (generate audio, word timings, etc.)
    const processedStory = await storyProcessor.processStory(
      { ...storyData, id },
      {
        generateAudio: storyData.generateAudio,
        voice: storyData.voice,
        speed: storyData.speed,
        pitch: storyData.pitch,
        language: storyData.language
      }
    )

    // Load existing stories
    const storiesPath = path.join(process.cwd(), 'src/data/stories.json')
    let stories = []
    
    try {
      const storiesData = await fs.readFile(storiesPath, 'utf-8')
      stories = JSON.parse(storiesData)
    } catch (error) {
      // File doesn't exist or is empty, start with empty array
      console.log('Creating new stories file')
    }

    // Add new story
    stories.push(processedStory)

    // Save back to file
    await fs.writeFile(storiesPath, JSON.stringify(stories, null, 2))

    return NextResponse.json(processedStory, { status: 201 })
  } catch (error) {
    console.error('Story creation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid story data', details: error.errors }, 
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to create story' }, 
      { status: 500 }
    )
  }
}
