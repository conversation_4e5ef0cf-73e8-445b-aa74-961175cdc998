'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { AdminScreen } from '@/components/screens/AdminScreen'
import { StoryForm } from '@/components/story/StoryForm'
import { StoryReaderScreen } from '@/components/screens/StoryReaderScreen'
import { Story } from '@/lib/types'

interface StoryFormData {
  title: string
  chapter?: string
  content: string
  language: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  voice?: string
  speed: number
  generateAudio: boolean
}

export default function AdminPage() {
  const [stories, setStories] = useState<Story[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingStory, setEditingStory] = useState<Story | null>(null)
  const [previewStory, setPreviewStory] = useState<Story | null>(null)

  // Load stories on mount
  useEffect(() => {
    loadStories()
  }, [])

  const loadStories = async () => {
    try {
      const response = await fetch('/api/stories')
      if (response.ok) {
        const storiesData = await response.json()
        setStories(storiesData)
      } else {
        console.error('Failed to load stories')
      }
    } catch (error) {
      console.error('Error loading stories:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreateStory = useCallback(() => {
    setEditingStory(null)
    setShowForm(true)
  }, [])

  const handleEditStory = useCallback((story: Story) => {
    setEditingStory(story)
    setShowForm(true)
  }, [])

  const handleDeleteStory = useCallback(async (storyId: string) => {
    if (!confirm('Are you sure you want to delete this story?')) {
      return
    }

    try {
      const response = await fetch(`/api/stories/${storyId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setStories(prev => prev.filter(story => story.id !== storyId))
      } else {
        alert('Failed to delete story')
      }
    } catch (error) {
      console.error('Error deleting story:', error)
      alert('Failed to delete story')
    }
  }, [])

  const handlePreviewStory = useCallback((story: Story) => {
    setPreviewStory(story)
  }, [])

  const handleSaveStory = useCallback(async (data: StoryFormData) => {
    try {
      const response = await fetch('/api/stories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (response.ok) {
        const newStory = await response.json()
        setStories(prev => [...prev, newStory])
        setShowForm(false)
        setEditingStory(null)
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save story')
      }
    } catch (error) {
      console.error('Error saving story:', error)
      throw error
    }
  }, [])

  const handlePreviewFormStory = useCallback(async (data: StoryFormData) => {
    try {
      const response = await fetch('/api/stories/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (response.ok) {
        const result = await response.json()
        setPreviewStory(result.story)
        setShowForm(false)
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to process story')
      }
    } catch (error) {
      console.error('Error processing story:', error)
      throw error
    }
  }, [])

  const handleCloseForm = useCallback(() => {
    setShowForm(false)
    setEditingStory(null)
  }, [])

  const handleClosePreview = useCallback(() => {
    setPreviewStory(null)
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading stories...</p>
        </div>
      </div>
    )
  }

  // Show preview mode
  if (previewStory) {
    return (
      <div className="relative">
        <button
          onClick={handleClosePreview}
          className="absolute top-4 left-4 z-50 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors"
        >
          ← Back to Admin
        </button>
        <StoryReaderScreen story={previewStory} />
      </div>
    )
  }

  return (
    <>
      <AdminScreen
        stories={stories}
        onCreateStory={handleCreateStory}
        onEditStory={handleEditStory}
        onDeleteStory={handleDeleteStory}
        onPreviewStory={handlePreviewStory}
      />

      <StoryForm
        story={editingStory}
        isOpen={showForm}
        onClose={handleCloseForm}
        onSave={handleSaveStory}
        onPreview={handlePreviewFormStory}
      />
    </>
  )
}
