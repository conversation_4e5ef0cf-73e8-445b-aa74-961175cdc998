"use client";

import React, { useState } from "react";
import { StoryReaderScreen } from "@/components/screens/StoryReaderScreen";
import storiesData from "@/data/stories.json";
import { Story } from "@/lib/types";

export default function Home() {
  // Get the first story from our mock data
  const story = storiesData[0] as Story;

  return (
    <div className="relative">
      <div className="absolute top-4 right-4 z-50">
        <a
          href="/admin"
          className="px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors text-sm"
        >
          Admin Panel
        </a>
      </div>
      <StoryReaderScreen story={story} />
    </div>
  );
}
