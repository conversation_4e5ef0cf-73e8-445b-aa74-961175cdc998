'use client'

import React, { useState, useCallback } from 'react'
import { StoryHeader } from '../story/StoryHeader'
import { ReadingArea } from '../story/ReadingArea'
import { AudioPlayer } from '../story/AudioPlayer'
import { PageNavigation } from '../story/PageNavigation'
import { ErrorBoundary } from '../ui/ErrorBoundary'
import { Story, StoryReaderState, TranslationResult } from '@/lib/types'

interface StoryReaderScreenProps {
  story: Story
}

// Mock translation function for now
const mockTranslate = async (word: string): Promise<TranslationResult> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500))
  
  // Mock translations for demo
  const mockTranslations: Record<string, TranslationResult> = {
    'plane': {
      translation: 'avion',
      pronunciation: 'a-vi-ɔ̃',
      provider: 'mock',
      example: {
        original: 'I like riding on planes.',
        translation: 'J\'aime voyager en avion.'
      }
    },
    'aircraft': {
      translation: 'aéronef',
      pronunciation: 'a-e-ʁo-nɛf',
      provider: 'mock'
    }
  }
  
  return mockTranslations[word.toLowerCase().replace(/[.,!?]/, '')] || {
    translation: `[${word}]`,
    provider: 'mock'
  }
}

export function StoryReaderScreen({ story }: StoryReaderScreenProps) {
  const [state, setState] = useState<StoryReaderState>({
    currentWordIndex: -1,
    selectedWord: undefined,
    translationCard: {
      isVisible: false,
      word: '',
      position: { x: 0, y: 0 },
      translation: undefined,
      isLoading: false
    },
    audioPlayer: {
      isPlaying: false,
      currentTime: 2.14, // Mock current time to match the image
      duration: 7.35, // Mock duration to match the image
      volume: 1.0
    }
  })

  const handleWordClick = useCallback(async (word: string, index: number, event: React.MouseEvent) => {
    // Close existing translation card if clicking the same word
    if (state.translationCard.isVisible && state.translationCard.word === word) {
      setState(prev => ({
        ...prev,
        translationCard: { ...prev.translationCard, isVisible: false }
      }))
      return
    }

    // Show loading state
    setState(prev => ({
      ...prev,
      selectedWord: word,
      translationCard: {
        isVisible: true,
        word,
        position: { x: event.clientX, y: event.clientY },
        translation: undefined,
        isLoading: true
      }
    }))

    try {
      const translation = await mockTranslate(word)
      setState(prev => ({
        ...prev,
        translationCard: {
          ...prev.translationCard,
          translation,
          isLoading: false
        }
      }))
    } catch (error) {
      setState(prev => ({
        ...prev,
        translationCard: {
          ...prev.translationCard,
          isLoading: false
        }
      }))
    }
  }, [state.translationCard.isVisible, state.translationCard.word])

  const handleTranslationClose = useCallback(() => {
    setState(prev => ({
      ...prev,
      translationCard: { ...prev.translationCard, isVisible: false }
    }))
  }, [])

  const handlePlayPause = useCallback(() => {
    setState(prev => ({
      ...prev,
      audioPlayer: {
        ...prev.audioPlayer,
        isPlaying: !prev.audioPlayer.isPlaying
      }
    }))
  }, [])

  const handleSeek = useCallback((time: number) => {
    setState(prev => ({
      ...prev,
      audioPlayer: {
        ...prev.audioPlayer,
        currentTime: time
      }
    }))
  }, [])

  const handleVolumeChange = useCallback((volume: number) => {
    setState(prev => ({
      ...prev,
      audioPlayer: {
        ...prev.audioPlayer,
        volume
      }
    }))
  }, [])

  return (
    <ErrorBoundary>
      <div className="flex flex-col h-screen bg-white">
        <StoryHeader
          title={story.title}
          chapter={story.chapter}
          onBookmarkClick={() => console.log('Bookmark clicked')}
          onMenuClick={() => console.log('Menu clicked')}
        />
        
        <ReadingArea
          content={story.content}
          currentWordIndex={state.currentWordIndex}
          translationCard={state.translationCard}
          onWordClick={handleWordClick}
          onTranslationClose={handleTranslationClose}
          onTranslationPlayAudio={() => console.log('Play translation audio')}
        />
        
        <PageNavigation
          currentPage={24}
          totalPages={120}
          onPreviousPage={() => console.log('Previous page')}
          onNextPage={() => console.log('Next page')}
        />
        
        <AudioPlayer
          state={state.audioPlayer}
          onPlayPause={handlePlayPause}
          onSeek={handleSeek}
          onVolumeChange={handleVolumeChange}
        />
      </div>
    </ErrorBoundary>
  )
}
