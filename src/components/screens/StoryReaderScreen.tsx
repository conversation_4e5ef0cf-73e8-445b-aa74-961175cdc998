'use client'

import React, { useState, useCallback } from 'react'
import { StoryHeader } from '../story/StoryHeader'
import { ReadingArea } from '../story/ReadingArea'
import { AudioPlayer } from '../story/AudioPlayer'
import { PageNavigation } from '../story/PageNavigation'
import { ErrorBoundary } from '../ui/ErrorBoundary'
import { KeyboardShortcutsHelp } from '../ui/KeyboardShortcutsHelp'
import { ProgressDisplay } from '../ui/ProgressDisplay'
import { useAudioPlayer } from '@/lib/hooks/useAudioPlayer'
import { useTranslation } from '@/lib/hooks/useTranslation'
import { useKeyboardShortcuts } from '@/lib/hooks/useKeyboardShortcuts'
import { useProgress } from '@/lib/hooks/useProgress'
import { Story, StoryReaderState, TranslationResult } from '@/lib/types'

interface StoryReaderScreenProps {
  story: Story
}



export function StoryReaderScreen({ story }: StoryReaderScreenProps) {
  const [state, setState] = useState<StoryReaderState>({
    currentWordIndex: -1,
    selectedWord: undefined,
    translationCard: {
      isVisible: false,
      word: '',
      position: { x: 0, y: 0 },
      translation: undefined,
      isLoading: false
    },
    audioPlayer: {
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 1.0
    }
  })

  // Use translation hook
  const translation = useTranslation()

  // Use progress tracking
  const progress = useProgress(story.id)

  // Handle word highlighting during audio playback
  const handleWordHighlight = useCallback((wordIndex: number) => {
    setState(prev => ({
      ...prev,
      currentWordIndex: wordIndex
    }))
  }, [])

  // Use audio player hook with real functionality
  const audioPlayer = useAudioPlayer({
    audioUrl: story.audioUrl,
    wordTimings: story.wordTimings,
    onWordHighlight: handleWordHighlight
  })

  // Start progress tracking session on mount
  React.useEffect(() => {
    progress.startSession(story.id)

    return () => {
      progress.endSession()
      // Refresh progress data after session ends
      setTimeout(() => {
        progress.refreshProgress()
      }, 100)
    }
  }, [story.id, progress])

  // Track reading progress when current word changes (debounced)
  React.useEffect(() => {
    if (state.currentWordIndex >= 0 && story.wordTimings) {
      const totalWords = story.wordTimings.length
      if (totalWords > 0) {
        const progressPercentage = Math.round((state.currentWordIndex / totalWords) * 100)

        // Debounce progress updates to avoid excessive calls
        const timeoutId = setTimeout(() => {
          progress.updateCompletionPercentage(progressPercentage)
        }, 100)

        return () => clearTimeout(timeoutId)
      }
    }
  }, [state.currentWordIndex, story.wordTimings, progress])

  // Update state when audio player state changes
  React.useEffect(() => {
    setState(prev => ({
      ...prev,
      audioPlayer: audioPlayer.state
    }))
  }, [audioPlayer.state])

  const handleWordClick = useCallback(async (word: string, index: number, event: React.MouseEvent) => {
    // Close existing translation card if clicking the same word
    if (state.translationCard.isVisible && state.translationCard.word === word) {
      setState(prev => ({
        ...prev,
        translationCard: { ...prev.translationCard, isVisible: false }
      }))
      return
    }

    // Show loading state
    setState(prev => ({
      ...prev,
      selectedWord: word,
      translationCard: {
        isVisible: true,
        word,
        position: { x: event.clientX, y: event.clientY },
        translation: undefined,
        isLoading: true
      }
    }))

    // Track word encounter and translation request
    progress.trackWordEncounter(word)
    progress.trackTranslationRequest(word)

    // Get translation using the hook
    const translationResult = await translation.translateWord(word, 'en', 'fr')

    setState(prev => ({
      ...prev,
      translationCard: {
        ...prev.translationCard,
        translation: translationResult || undefined,
        isLoading: false
      }
    }))
  }, [state.translationCard.isVisible, state.translationCard.word, translation])

  const handleTranslationClose = useCallback(() => {
    setState(prev => ({
      ...prev,
      translationCard: { ...prev.translationCard, isVisible: false }
    }))
  }, [])

  const handlePlayPause = useCallback(() => {
    audioPlayer.togglePlayPause()
  }, [audioPlayer])

  const handleSeek = useCallback((time: number) => {
    audioPlayer.seek(time)
  }, [audioPlayer])

  const handleVolumeChange = useCallback((volume: number) => {
    audioPlayer.setVolume(volume)
  }, [audioPlayer])

  const handleSeekForward = useCallback(() => {
    const newTime = Math.min(state.audioPlayer.currentTime + 10, state.audioPlayer.duration)
    audioPlayer.seek(newTime)
  }, [audioPlayer, state.audioPlayer.currentTime, state.audioPlayer.duration])

  const handleSeekBackward = useCallback(() => {
    const newTime = Math.max(state.audioPlayer.currentTime - 10, 0)
    audioPlayer.seek(newTime)
  }, [audioPlayer, state.audioPlayer.currentTime])

  // Setup keyboard shortcuts
  useKeyboardShortcuts({
    onPlayPause: handlePlayPause,
    onSeekForward: handleSeekForward,
    onSeekBackward: handleSeekBackward,
    onCloseTranslation: handleTranslationClose
  })

  return (
    <ErrorBoundary>
      <div className="flex flex-col h-screen bg-white">
        <StoryHeader
          title={story.title}
          chapter={story.chapter}
          isAudioPlaying={state.audioPlayer.isPlaying}
          onBookmarkClick={() => console.log('Bookmark clicked')}
          onMenuClick={() => console.log('Menu clicked')}
        />
        
        <ReadingArea
          content={story.content}
          currentWordIndex={state.currentWordIndex}
          translationCard={state.translationCard}
          onWordClick={handleWordClick}
          onTranslationClose={handleTranslationClose}
          onTranslationPlayAudio={() => console.log('Play translation audio')}
        />
        
        <PageNavigation
          currentPage={24}
          totalPages={120}
          onPreviousPage={() => console.log('Previous page')}
          onNextPage={() => console.log('Next page')}
        />
        
        <AudioPlayer
          state={state.audioPlayer}
          onPlayPause={handlePlayPause}
          onSeek={handleSeek}
          onVolumeChange={handleVolumeChange}
        />

        <KeyboardShortcutsHelp />

        <ProgressDisplay
          storyProgress={progress.storyProgress}
          overallStats={progress.overallStats}
          className="fixed bottom-4 left-4 max-w-xs z-40"
        />
      </div>
    </ErrorBoundary>
  )
}
