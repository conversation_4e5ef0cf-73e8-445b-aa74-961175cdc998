'use client'

import React, { useState, useCallback } from 'react'
import { Typography } from '../ui/Typography'
import { IconButton } from '../ui/IconButton'
import { ErrorBoundary } from '../ui/ErrorBoundary'
import { StoryForm } from '../story/StoryForm'
import { PlusIcon, PlayIcon, TrashIcon } from '@radix-ui/react-icons'
import { Story } from '@/lib/types'

interface AdminScreenProps {
  stories: Story[]
  onCreateStory: () => void
  onEditStory: (story: Story) => void
  onDeleteStory: (storyId: string) => void
  onPreviewStory: (story: Story) => void
}

export function AdminScreen({ 
  stories, 
  onCreateStory, 
  onEditStory, 
  onDeleteStory, 
  onPreviewStory 
}: AdminScreenProps) {
  const [selectedStory, setSelectedStory] = useState<Story | null>(null)

  const handleStoryClick = useCallback((story: Story) => {
    setSelectedStory(story)
  }, [])

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <Typography.Title className="text-2xl">
                Story Management
              </Typography.Title>
              <Typography.Subtitle className="mt-1">
                Manage your language learning stories
              </Typography.Subtitle>
            </div>
            <IconButton
              icon={<PlusIcon className="w-5 h-5" />}
              onClick={onCreateStory}
              className="bg-blue-600 text-white hover:bg-blue-700"
              aria-label="Create new story"
            />
          </div>
        </header>

        <div className="flex h-[calc(100vh-80px)]">
          {/* Stories List */}
          <div className="w-1/2 border-r border-gray-200 overflow-y-auto">
            <div className="p-6">
              <Typography.Body className="text-lg font-semibold mb-4">
                Stories ({stories.length})
              </Typography.Body>
              
              {stories.length === 0 ? (
                <div className="text-center py-12">
                  <Typography.Body className="text-gray-500 mb-4">
                    No stories yet
                  </Typography.Body>
                  <button
                    onClick={onCreateStory}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Create your first story
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {stories.map((story) => (
                    <div
                      key={story.id}
                      className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                        selectedStory?.id === story.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 bg-white hover:border-gray-300'
                      }`}
                      onClick={() => handleStoryClick(story)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Typography.Body className="font-semibold text-gray-900 mb-1">
                            {story.title}
                          </Typography.Body>
                          {story.chapter && (
                            <Typography.Caption className="text-gray-600 mb-2">
                              {story.chapter}
                            </Typography.Caption>
                          )}
                          <div className="flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(story.difficulty)}`}>
                              {story.difficulty}
                            </span>
                            <Typography.Micro className="text-gray-500">
                              {story.language.toUpperCase()}
                            </Typography.Micro>
                            {story.audioUrl && (
                              <Typography.Micro className="text-green-600">
                                ♪ Audio
                              </Typography.Micro>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-1 ml-4">
                          <IconButton
                            icon={<PlayIcon className="w-4 h-4" />}
                            onClick={(e) => {
                              e.stopPropagation()
                              onPreviewStory(story)
                            }}
                            variant="ghost"
                            size="sm"
                            aria-label="Preview story"
                          />
                          <IconButton
                            icon={<TrashIcon className="w-4 h-4" />}
                            onClick={(e) => {
                              e.stopPropagation()
                              onDeleteStory(story.id)
                            }}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:bg-red-50"
                            aria-label="Delete story"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Story Details */}
          <div className="w-1/2 overflow-y-auto">
            {selectedStory ? (
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <Typography.Title className="text-xl">
                    Story Details
                  </Typography.Title>
                  <button
                    onClick={() => onEditStory(selectedStory)}
                    className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    Edit Story
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <Typography.Caption className="font-semibold text-gray-700 mb-1">
                      Title
                    </Typography.Caption>
                    <Typography.Body>{selectedStory.title}</Typography.Body>
                  </div>

                  {selectedStory.chapter && (
                    <div>
                      <Typography.Caption className="font-semibold text-gray-700 mb-1">
                        Chapter
                      </Typography.Caption>
                      <Typography.Body>{selectedStory.chapter}</Typography.Body>
                    </div>
                  )}

                  <div>
                    <Typography.Caption className="font-semibold text-gray-700 mb-1">
                      Language & Difficulty
                    </Typography.Caption>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(selectedStory.difficulty)}`}>
                        {selectedStory.difficulty}
                      </span>
                      <Typography.Caption>{selectedStory.language.toUpperCase()}</Typography.Caption>
                    </div>
                  </div>

                  <div>
                    <Typography.Caption className="font-semibold text-gray-700 mb-1">
                      Content Preview
                    </Typography.Caption>
                    <div className="bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto">
                      <Typography.Body className="text-sm">
                        {selectedStory.content.substring(0, 300)}
                        {selectedStory.content.length > 300 && '...'}
                      </Typography.Body>
                    </div>
                  </div>

                  <div>
                    <Typography.Caption className="font-semibold text-gray-700 mb-1">
                      Audio & Timing
                    </Typography.Caption>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Typography.Micro>Audio:</Typography.Micro>
                        <Typography.Micro className={selectedStory.audioUrl ? 'text-green-600' : 'text-red-600'}>
                          {selectedStory.audioUrl ? '✓ Available' : '✗ Not generated'}
                        </Typography.Micro>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Typography.Micro>Word Timings:</Typography.Micro>
                        <Typography.Micro className={selectedStory.wordTimings?.length ? 'text-green-600' : 'text-red-600'}>
                          {selectedStory.wordTimings?.length ? `✓ ${selectedStory.wordTimings.length} words` : '✗ Not available'}
                        </Typography.Micro>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <Typography.Body className="text-gray-500">
                  Select a story to view details
                </Typography.Body>
              </div>
            )}
          </div>
        </div>
      </div>
    </ErrorBoundary>
  )
}
