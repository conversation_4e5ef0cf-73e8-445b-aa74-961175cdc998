import React from 'react'
import { BookmarkIcon, HamburgerMenuIcon } from '@radix-ui/react-icons'
import { Typography } from '../ui/Typography'
import { IconButton } from '../ui/IconButton'

interface StoryHeaderProps {
  title: string
  chapter?: string
  onBookmarkClick: () => void
  onMenuClick: () => void
}

export function StoryHeader({ title, chapter, onBookmarkClick, onMenuClick }: StoryHeaderProps) {
  return (
    <header className="flex items-center justify-between p-4 bg-white border-b border-gray-100">
      <div className="flex-1">
        <Typography.Title className="text-xl font-bold text-gray-900">
          {title}
        </Typography.Title>
        {chapter && (
          <Typography.Subtitle className="text-sm text-gray-600 mt-1">
            {chapter}
          </Typography.Subtitle>
        )}
      </div>
      
      <div className="flex items-center space-x-2">
        <IconButton
          icon={<BookmarkIcon className="w-5 h-5" />}
          onClick={onBookmarkClick}
          variant="ghost"
          aria-label="Bookmark story"
        />
        <IconButton
          icon={<HamburgerMenuIcon className="w-5 h-5" />}
          onClick={onMenuClick}
          variant="ghost"
          aria-label="Open menu"
        />
      </div>
    </header>
  )
}
