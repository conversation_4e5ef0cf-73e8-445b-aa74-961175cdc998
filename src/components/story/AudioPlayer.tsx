import React from 'react'
import { PlayIcon, PauseIcon } from '@radix-ui/react-icons'
import { IconButton } from '../ui/IconButton'
import { Slider } from '../ui/Slider'
import { Typography } from '../ui/Typography'
import { AudioPlayerState } from '@/lib/types'

interface AudioPlayerProps {
  state: AudioPlayerState
  onPlayPause: () => void
  onSeek: (time: number) => void
  onVolumeChange: (volume: number) => void
}

function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.floor(seconds % 60)
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

export function AudioPlayer({ state, onPlayPause, onSeek, onVolumeChange }: AudioPlayerProps) {
  const { isPlaying, currentTime, duration, volume } = state

  const handleProgressChange = (value: number[]) => {
    onSeek(value[0])
  }

  const handleVolumeChange = (value: number[]) => {
    onVolumeChange(value[0] / 100)
  }

  return (
    <div className="bg-white border-t border-gray-200 p-4">
      {/* Progress Bar */}
      <div className="mb-4">
        <Slider
          value={[currentTime]}
          onValueChange={handleProgressChange}
          max={duration}
          min={0}
          step={0.1}
          className="w-full"
        />
        <div className="flex justify-between mt-2">
          <Typography.Micro>
            {formatTime(currentTime)}
          </Typography.Micro>
          <Typography.Micro>
            {formatTime(duration)}
          </Typography.Micro>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-center">
        <IconButton
          icon={isPlaying ? <PauseIcon className="w-6 h-6" /> : <PlayIcon className="w-6 h-6" />}
          onClick={onPlayPause}
          size="lg"
          className="bg-gray-900 text-white hover:bg-gray-800"
          aria-label={isPlaying ? 'Pause' : 'Play'}
        />
      </div>
    </div>
  )
}
