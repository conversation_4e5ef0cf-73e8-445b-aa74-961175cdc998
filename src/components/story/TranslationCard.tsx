import React from 'react'
import { Cross2Icon, SpeakerLoudIcon } from '@radix-ui/react-icons'
import { Typography } from '../ui/Typography'
import { IconButton } from '../ui/IconButton'
import { TranslationResult } from '@/lib/types'

interface TranslationCardProps {
  word: string
  translation?: TranslationResult
  isLoading?: boolean
  onPlayAudio: () => void
  onClose: () => void
}

export function TranslationCard({
  word,
  translation,
  isLoading = false,
  onPlayAudio,
  onClose
}: TranslationCardProps) {
  return (
    <div className="bg-gray-50 rounded-xl p-4 mx-5 mb-6 relative shadow-sm border border-gray-200">
      <IconButton
        icon={<Cross2Icon className="w-4 h-4" />}
        onClick={onClose}
        variant="ghost"
        size="sm"
        className="absolute top-2 right-2"
        aria-label="Close translation"
      />
      
      <div className="flex items-center justify-between mb-3">
        <div>
          <Typography.TranslationWord className="text-xl">
            {word}
          </Typography.TranslationWord>
          {translation?.pronunciation && (
            <Typography.Caption className="block mt-1">
              /{translation.pronunciation}/
            </Typography.Caption>
          )}
        </div>
        <IconButton 
          icon={<SpeakerLoudIcon className="w-4 h-4" />} 
          onClick={onPlayAudio}
          variant="ghost"
          size="sm"
          aria-label="Play pronunciation"
        />
      </div>
      
      {isLoading ? (
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          <Typography.Caption className="text-gray-400 mt-2">
            Translating...
          </Typography.Caption>
        </div>
      ) : translation ? (
        <>
          <Typography.TranslationText className="block mb-3">
            {translation.translation}
          </Typography.TranslationText>
          
          {translation.example && (
            <div className="text-sm text-gray-600 border-t border-gray-200 pt-3">
              <Typography.Caption className="italic block mb-1">
                {translation.example.original}
              </Typography.Caption>
              <Typography.Caption className="block">
                {translation.example.translation}
              </Typography.Caption>
            </div>
          )}
        </>
      ) : (
        <Typography.Caption className="text-gray-500">
          Translation not available
        </Typography.Caption>
      )}
    </div>
  )
}
