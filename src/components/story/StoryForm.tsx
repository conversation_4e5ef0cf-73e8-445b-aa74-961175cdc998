'use client'

import React, { useState, useCallback } from 'react'
import { Typography } from '../ui/Typography'
import { IconButton } from '../ui/IconButton'
import { Cross2Icon, PlayIcon } from '@radix-ui/react-icons'
import { Story } from '@/lib/types'

interface StoryFormData {
  title: string
  chapter?: string
  content: string
  language: string
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  voice?: string
  speed: number
  generateAudio: boolean
}

interface StoryFormProps {
  story?: Story
  isOpen: boolean
  onClose: () => void
  onSave: (data: StoryFormData) => Promise<void>
  onPreview?: (data: StoryFormData) => Promise<void>
}

const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'fr', name: 'French' },
  { code: 'es', name: 'Spanish' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' }
]

const DIFFICULTIES = [
  { value: 'beginner', label: 'Beginner', description: 'Simple vocabulary and grammar' },
  { value: 'intermediate', label: 'Intermediate', description: 'Moderate complexity' },
  { value: 'advanced', label: 'Advanced', description: 'Complex vocabulary and structures' }
] as const

export function StoryForm({ story, isOpen, onClose, onSave, onPreview }: StoryFormProps) {
  const [formData, setFormData] = useState<StoryFormData>({
    title: story?.title || '',
    chapter: story?.chapter || '',
    content: story?.content || '',
    language: story?.language || 'fr',
    difficulty: story?.difficulty || 'beginner',
    voice: '',
    speed: 1.0,
    generateAudio: true
  })

  const [isLoading, setIsLoading] = useState(false)
  const [isPreviewing, setIsPreviewing] = useState(false)

  const handleInputChange = useCallback((field: keyof StoryFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }, [])

  const handleSave = useCallback(async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert('Please fill in title and content')
      return
    }

    setIsLoading(true)
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Save failed:', error)
      alert('Failed to save story')
    } finally {
      setIsLoading(false)
    }
  }, [formData, onSave, onClose])

  const handlePreview = useCallback(async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert('Please fill in title and content')
      return
    }

    if (!onPreview) return

    setIsPreviewing(true)
    try {
      await onPreview(formData)
    } catch (error) {
      console.error('Preview failed:', error)
      alert('Failed to preview story')
    } finally {
      setIsPreviewing(false)
    }
  }, [formData, onPreview])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <Typography.Title className="text-xl">
            {story ? 'Edit Story' : 'Create New Story'}
          </Typography.Title>
          <IconButton
            icon={<Cross2Icon className="w-5 h-5" />}
            onClick={onClose}
            variant="ghost"
            aria-label="Close form"
          />
        </div>

        {/* Form */}
        <div className="p-6 space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter story title"
            />
          </div>

          {/* Chapter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Chapter (optional)
            </label>
            <input
              type="text"
              value={formData.chapter}
              onChange={(e) => handleInputChange('chapter', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="e.g., Chapter 1, Part A"
            />
          </div>

          {/* Language and Difficulty */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Language *
              </label>
              <select
                value={formData.language}
                onChange={(e) => handleInputChange('language', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {LANGUAGES.map(lang => (
                  <option key={lang.code} value={lang.code}>
                    {lang.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Difficulty *
              </label>
              <select
                value={formData.difficulty}
                onChange={(e) => handleInputChange('difficulty', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {DIFFICULTIES.map(diff => (
                  <option key={diff.value} value={diff.value}>
                    {diff.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Story Content *
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              rows={8}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
              placeholder="Enter the story content..."
            />
            <Typography.Micro className="text-gray-500 mt-1">
              {formData.content.length} characters
            </Typography.Micro>
          </div>

          {/* Audio Settings */}
          <div className="border-t border-gray-200 pt-6">
            <Typography.Body className="font-semibold mb-4">
              Audio Settings
            </Typography.Body>
            
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="generateAudio"
                  checked={formData.generateAudio}
                  onChange={(e) => handleInputChange('generateAudio', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="generateAudio" className="ml-2 text-sm text-gray-700">
                  Generate audio automatically
                </label>
              </div>

              {formData.generateAudio && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Speech Speed
                  </label>
                  <input
                    type="range"
                    min="0.5"
                    max="2.0"
                    step="0.1"
                    value={formData.speed}
                    onChange={(e) => handleInputChange('speed', parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>Slow (0.5x)</span>
                    <span>Normal ({formData.speed}x)</span>
                    <span>Fast (2.0x)</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          
          <div className="flex items-center space-x-3">
            {onPreview && (
              <button
                onClick={handlePreview}
                disabled={isPreviewing}
                className="px-4 py-2 text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors disabled:opacity-50 flex items-center space-x-2"
              >
                <PlayIcon className="w-4 h-4" />
                <span>{isPreviewing ? 'Processing...' : 'Preview'}</span>
              </button>
            )}
            
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {isLoading ? 'Saving...' : 'Save Story'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
