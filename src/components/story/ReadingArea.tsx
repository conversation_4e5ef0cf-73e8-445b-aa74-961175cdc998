import React from 'react'
import { WordHighlighter } from './WordHighlighter'
import { TranslationCard } from './TranslationCard'
import { TranslationCardState } from '@/lib/types'

interface ReadingAreaProps {
  content: string
  currentWordIndex?: number
  translationCard: TranslationCardState
  onWordClick: (word: string, index: number, event: React.MouseEvent) => void
  onTranslationClose: () => void
  onTranslationPlayAudio: () => void
}

export function ReadingArea({
  content,
  currentWordIndex,
  translationCard,
  onWordClick,
  onTranslationClose,
  onTranslationPlayAudio
}: ReadingAreaProps) {
  return (
    <div className="flex-1 overflow-y-auto">
      <div className="max-w-sm mx-auto p-6">
        <div className="mb-6">
          <WordHighlighter
            text={content}
            currentWordIndex={currentWordIndex}
            onWordClick={onWordClick}
          />
        </div>
        
        {translationCard.isVisible && (
          <TranslationCard
            word={translationCard.word}
            translation={translationCard.translation}
            isLoading={translationCard.isLoading}
            onPlayAudio={onTranslationPlayAudio}
            onClose={onTranslationClose}
          />
        )}
      </div>
    </div>
  )
}
