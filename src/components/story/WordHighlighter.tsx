import React from 'react'

interface WordHighlighterProps {
  text: string
  currentWordIndex?: number
  onWordClick: (word: string, index: number, event: React.MouseEvent) => void
}

export function WordHighlighter({ text, currentWordIndex, onWordClick }: WordHighlighterProps) {
  const words = text.split(/(\s+)/)
  let wordIndex = 0

  return (
    <div className="text-lg leading-relaxed text-gray-800 select-none">
      {words.map((segment, segmentIndex) => {
        // Skip whitespace segments
        if (/^\s+$/.test(segment)) {
          return <span key={segmentIndex}>{segment}</span>
        }

        const isCurrentWord = wordIndex === currentWordIndex
        const currentIndex = wordIndex
        wordIndex++

        return (
          <span
            key={segmentIndex}
            className={`cursor-pointer transition-all duration-300 hover:bg-blue-100 px-1 py-0.5 rounded ${
              isCurrentWord
                ? 'bg-blue-300 text-blue-900 font-semibold shadow-sm scale-105'
                : 'hover:scale-102'
            }`}
            onClick={(event) => onWordClick(segment, currentIndex, event)}
          >
            {segment}
          </span>
        )
      })}
    </div>
  )
}
