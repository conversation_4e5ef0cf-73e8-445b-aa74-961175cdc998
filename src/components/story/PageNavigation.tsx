import React from 'react'
import { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons'
import { IconButton } from '../ui/IconButton'
import { Typography } from '../ui/Typography'

interface PageNavigationProps {
  currentPage: number
  totalPages: number
  onPreviousPage: () => void
  onNextPage: () => void
}

export function PageNavigation({ 
  currentPage, 
  totalPages, 
  onPreviousPage, 
  onNextPage 
}: PageNavigationProps) {
  return (
    <div className="flex items-center justify-between p-4 bg-white border-t border-gray-100">
      <IconButton
        icon={<ChevronLeftIcon className="w-5 h-5" />}
        onClick={onPreviousPage}
        variant="ghost"
        disabled={currentPage <= 1}
        aria-label="Previous page"
      />
      
      <Typography.Caption className="text-gray-500">
        Page {currentPage} of {totalPages}
      </Typography.Caption>
      
      <IconButton
        icon={<ChevronRightIcon className="w-5 h-5" />}
        onClick={onNextPage}
        variant="ghost"
        disabled={currentPage >= totalPages}
        aria-label="Next page"
      />
    </div>
  )
}
