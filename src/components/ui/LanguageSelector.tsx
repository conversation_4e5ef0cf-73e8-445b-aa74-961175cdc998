import React, { useState } from 'react'
import { ChevronDownIcon, CheckIcon } from '@radix-ui/react-icons'
import { Typography } from './Typography'
import { SupportedLanguages, LanguageCode, LanguageUtils } from '@/lib/types-multilang'

interface LanguageSelectorProps {
  selectedLanguage: LanguageCode
  onLanguageChange: (language: LanguageCode) => void
  label?: string
  className?: string
  availableLanguages?: LanguageCode[]
}

export function LanguageSelector({
  selectedLanguage,
  onLanguageChange,
  label,
  className = '',
  availableLanguages = Object.keys(SupportedLanguages) as LanguageCode[]
}: LanguageSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)

  const selectedLang = SupportedLanguages[selectedLanguage]

  return (
    <div className={`relative ${className}`}>
      {label && (
        <Typography.Caption className="block mb-1 text-gray-600">
          {label}
        </Typography.Caption>
      )}
      
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center space-x-2">
          <span className="text-lg">{selectedLang.flag}</span>
          <Typography.Caption className="text-gray-700">
            {selectedLang.name}
          </Typography.Caption>
        </div>
        <ChevronDownIcon 
          className={`w-4 h-4 text-gray-400 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-md shadow-lg z-20 max-h-60 overflow-y-auto">
            {availableLanguages.map((langCode) => {
              const lang = SupportedLanguages[langCode]
              const isSelected = langCode === selectedLanguage
              
              return (
                <button
                  key={langCode}
                  onClick={() => {
                    onLanguageChange(langCode)
                    setIsOpen(false)
                  }}
                  className={`flex items-center justify-between w-full px-3 py-2 hover:bg-gray-50 ${
                    isSelected ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{lang.flag}</span>
                    <Typography.Caption>
                      {lang.name}
                    </Typography.Caption>
                  </div>
                  {isSelected && (
                    <CheckIcon className="w-4 h-4 text-blue-600" />
                  )}
                </button>
              )
            })}
          </div>
        </>
      )}
    </div>
  )
}

// Language pair selector for translation direction
interface LanguagePairSelectorProps {
  sourceLanguage: LanguageCode
  targetLanguage: LanguageCode
  onSourceChange: (language: LanguageCode) => void
  onTargetChange: (language: LanguageCode) => void
  onSwap?: () => void
  className?: string
}

export function LanguagePairSelector({
  sourceLanguage,
  targetLanguage,
  onSourceChange,
  onTargetChange,
  onSwap,
  className = ''
}: LanguagePairSelectorProps) {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <LanguageSelector
        selectedLanguage={sourceLanguage}
        onLanguageChange={onSourceChange}
        label="From"
        className="flex-1"
      />
      
      {onSwap && (
        <button
          onClick={onSwap}
          className="p-2 mt-5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
          aria-label="Swap languages"
        >
          ⇄
        </button>
      )}
      
      <LanguageSelector
        selectedLanguage={targetLanguage}
        onLanguageChange={onTargetChange}
        label="To"
        className="flex-1"
      />
    </div>
  )
}
