import React from 'react'

interface TypographyProps {
  children: React.ReactNode
  className?: string
}

export const Typography = {
  Title: ({ children, className = '' }: TypographyProps) => (
    <h1 className={`text-2xl font-bold text-gray-900 ${className}`}>
      {children}
    </h1>
  ),
  
  Subtitle: ({ children, className = '' }: TypographyProps) => (
    <h2 className={`text-base text-gray-600 ${className}`}>
      {children}
    </h2>
  ),
  
  Body: ({ children, className = '' }: TypographyProps) => (
    <p className={`text-lg text-gray-800 leading-relaxed ${className}`}>
      {children}
    </p>
  ),
  
  Caption: ({ children, className = '' }: TypographyProps) => (
    <span className={`text-sm text-gray-500 ${className}`}>
      {children}
    </span>
  ),

  TranslationWord: ({ children, className = '' }: TypographyProps) => (
    <span className={`text-xl font-bold text-gray-900 ${className}`}>
      {children}
    </span>
  ),

  TranslationText: ({ children, className = '' }: TypographyProps) => (
    <span className={`text-base text-gray-700 ${className}`}>
      {children}
    </span>
  ),

  Micro: ({ children, className = '' }: TypographyProps) => (
    <span className={`text-xs text-gray-400 ${className}`}>
      {children}
    </span>
  )
}
