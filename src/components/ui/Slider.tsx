import React from 'react'
import * as RadixSlider from '@radix-ui/react-slider'

interface SliderProps {
  value: number[]
  onValueChange: (value: number[]) => void
  max?: number
  min?: number
  step?: number
  className?: string
  disabled?: boolean
}

export function Slider({
  value,
  onValueChange,
  max = 100,
  min = 0,
  step = 1,
  className = '',
  disabled = false,
}: SliderProps) {
  return (
    <RadixSlider.Root
      className={`relative flex items-center select-none touch-none w-full h-5 ${className}`}
      value={value}
      onValueChange={onValueChange}
      max={max}
      min={min}
      step={step}
      disabled={disabled}
    >
      <RadixSlider.Track className="bg-gray-200 relative grow rounded-full h-1">
        <RadixSlider.Range className="absolute bg-gray-900 rounded-full h-full" />
      </RadixSlider.Track>
      <RadixSlider.Thumb
        className="block w-4 h-4 bg-white border-2 border-gray-900 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
        aria-label="Volume"
      />
    </RadixSlider.Root>
  )
}
