import React from 'react'
import { Typography } from './Typography'
import { UserProgress, ProgressStats } from '@/lib/services/progress-service'

interface ProgressDisplayProps {
  storyProgress?: UserProgress | null
  overallStats?: ProgressStats | null
  className?: string
}

export function ProgressDisplay({ storyProgress, overallStats, className = '' }: ProgressDisplayProps) {
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const formatPercentage = (percentage: number): string => {
    return `${Math.round(percentage)}%`
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <Typography.Body className="font-semibold mb-3 text-gray-800">
        Learning Progress
      </Typography.Body>

      {/* Current Story Progress */}
      {storyProgress && (
        <div className="mb-4 pb-4 border-b border-gray-100">
          <Typography.Caption className="text-gray-600 mb-2">
            Current Story
          </Typography.Caption>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Typography.Micro className="text-gray-500">
                Completion
              </Typography.Micro>
              <Typography.Micro className="font-medium text-blue-600">
                {formatPercentage(storyProgress.completionPercentage)}
              </Typography.Micro>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${storyProgress.completionPercentage}%` }}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4 mt-3">
              <div>
                <Typography.Micro className="text-gray-500">
                  Words Learned
                </Typography.Micro>
                <Typography.Caption className="font-medium text-green-600">
                  {storyProgress.wordsLearned.length}
                </Typography.Caption>
              </div>
              
              <div>
                <Typography.Micro className="text-gray-500">
                  Time Spent
                </Typography.Micro>
                <Typography.Caption className="font-medium text-purple-600">
                  {formatTime(storyProgress.timeSpent)}
                </Typography.Caption>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Overall Statistics */}
      {overallStats && (
        <div>
          <Typography.Caption className="text-gray-600 mb-2">
            Overall Stats
          </Typography.Caption>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-2 bg-blue-50 rounded-lg">
              <Typography.Body className="font-bold text-blue-600">
                {overallStats.totalStoriesStarted}
              </Typography.Body>
              <Typography.Micro className="text-blue-600">
                Stories Started
              </Typography.Micro>
            </div>
            
            <div className="text-center p-2 bg-green-50 rounded-lg">
              <Typography.Body className="font-bold text-green-600">
                {overallStats.totalStoriesCompleted}
              </Typography.Body>
              <Typography.Micro className="text-green-600">
                Completed
              </Typography.Micro>
            </div>
            
            <div className="text-center p-2 bg-purple-50 rounded-lg">
              <Typography.Body className="font-bold text-purple-600">
                {overallStats.totalWordsLearned}
              </Typography.Body>
              <Typography.Micro className="text-purple-600">
                Words Learned
              </Typography.Micro>
            </div>
            
            <div className="text-center p-2 bg-orange-50 rounded-lg">
              <Typography.Body className="font-bold text-orange-600">
                {formatTime(overallStats.totalTimeSpent)}
              </Typography.Body>
              <Typography.Micro className="text-orange-600">
                Total Time
              </Typography.Micro>
            </div>
          </div>
          
          {overallStats.streakDays > 0 && (
            <div className="mt-3 text-center p-2 bg-yellow-50 rounded-lg">
              <Typography.Caption className="font-medium text-yellow-700">
                🔥 {overallStats.streakDays} day streak!
              </Typography.Caption>
            </div>
          )}
        </div>
      )}

      {!storyProgress && !overallStats && (
        <div className="text-center py-4">
          <Typography.Caption className="text-gray-500">
            Start reading to track your progress
          </Typography.Caption>
        </div>
      )}
    </div>
  )
}
