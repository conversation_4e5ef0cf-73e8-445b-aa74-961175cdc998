import React, { useState } from 'react'
import { QuestionMarkCircledIcon, Cross2Icon } from '@radix-ui/react-icons'
import { IconButton } from './IconButton'
import { Typography } from './Typography'

export function KeyboardShortcutsHelp() {
  const [isVisible, setIsVisible] = useState(false)

  const shortcuts = [
    { key: 'Space', description: 'Play/Pause audio' },
    { key: 'Shift + →', description: 'Skip forward 10s' },
    { key: 'Shift + ←', description: 'Skip backward 10s' },
    { key: 'Esc', description: 'Close translation' },
  ]

  if (!isVisible) {
    return (
      <IconButton
        icon={<QuestionMarkCircledIcon className="w-4 h-4" />}
        onClick={() => setIsVisible(true)}
        variant="ghost"
        size="sm"
        aria-label="Show keyboard shortcuts"
        className="fixed bottom-20 right-4 bg-white shadow-lg border border-gray-200"
      />
    )
  }

  return (
    <div className="fixed bottom-20 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-xs z-50">
      <div className="flex items-center justify-between mb-3">
        <Typography.Caption className="font-semibold text-gray-700">
          Keyboard Shortcuts
        </Typography.Caption>
        <IconButton
          icon={<Cross2Icon className="w-3 h-3" />}
          onClick={() => setIsVisible(false)}
          variant="ghost"
          size="sm"
          aria-label="Close shortcuts help"
        />
      </div>
      
      <div className="space-y-2">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="flex items-center justify-between">
            <Typography.Micro className="font-mono bg-gray-100 px-2 py-1 rounded text-gray-700">
              {shortcut.key}
            </Typography.Micro>
            <Typography.Micro className="text-gray-600 ml-3">
              {shortcut.description}
            </Typography.Micro>
          </div>
        ))}
      </div>
    </div>
  )
}
