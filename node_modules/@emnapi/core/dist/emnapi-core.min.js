!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((e="undefined"!=typeof globalThis?globalThis:e||self).emnapiCore={})}(this,function(e){var r="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0;function t(e){if(e&&"object"!=typeof e)throw new TypeError("imports must be an object or undefined");return!0}function n(e,a){if(!e)throw new TypeError("Invalid wasm source");t(a),a=null!=a?a:{};try{var o="object"==typeof e&&null!==e&&"then"in e?e.then:void 0;if("function"==typeof o)return o.call(e,function(e){return n(e,a)})}catch(e){}if(e instanceof ArrayBuffer||ArrayBuffer.isView(e))return r.instantiate(e,a);if(e instanceof r.Module)return r.instantiate(e,a).then(function(r){return{instance:r,module:e}});if("undefined"!=typeof Response&&e instanceof Response)return e.arrayBuffer().then(function(e){return r.instantiate(e,a)});var s="string"==typeof e;if(s||"undefined"!=typeof URL&&e instanceof URL){if(s&&"undefined"!=typeof wx&&"undefined"!=typeof __wxConfig)return r.instantiate(e,a);if("function"!=typeof fetch)throw new TypeError("wasm source can not be a string or URL in this environment");if("function"!=typeof r.instantiateStreaming)return n(fetch(e),a);try{return r.instantiateStreaming(fetch(e),a).catch(function(){return n(fetch(e),a)})}catch(r){return n(fetch(e),a)}}throw new TypeError("Invalid wasm source")}var a="undefined"!=typeof WebAssembly?WebAssembly:"undefined"!=typeof WXWebAssembly?WXWebAssembly:void 0,o="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node;function s(e){return"function"==typeof(null==e?void 0:e.postMessage)?e.postMessage:"function"==typeof postMessage?postMessage:void 0}function i(e){return"function"==typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(e)}function u(e){try{return e instanceof a.RuntimeError}catch(e){return!1}}function f(e,r){return{__emnapi__:{type:e,payload:r}}}function c(e){if(e){if(!i(e.buffer))throw new Error("Multithread features require shared wasm memory. Try to compile with `-matomics -mbulk-memory` and use `--import-memory --shared-memory` during linking, then create WebAssembly.Memory with `shared: true` option")}else if("undefined"==typeof SharedArrayBuffer)throw new Error("Current environment does not support SharedArrayBuffer, threads are not available!")}var l=0,d=function(){function e(e){var r;if(this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.wasmModule=null,this.wasmMemory=null,this.messageEvents=new WeakMap,!e)throw new TypeError("ThreadManager(): options is not provided");this._childThread="childThread"in e&&Boolean(e.childThread),this._childThread?(this._onCreateWorker=void 0,this._reuseWorker=!1,this._beforeLoad=void 0):(this._onCreateWorker=e.onCreateWorker,this._reuseWorker=function(e){var r;if("boolean"==typeof e)return!!e&&{size:0,strict:!1};if("number"==typeof e){if(!(e>=0))throw new RangeError("reuseWorker: size must be a non-negative integer");return{size:e,strict:!1}}if(!e)return!1;var t=null!==(r=Number(e.size))&&void 0!==r?r:0,n=Boolean(e.strict);if(!(t>0)&&n)throw new RangeError("reuseWorker: size must be set to positive integer if strict is set to true");return{size:t,strict:n}}(e.reuseWorker),this._beforeLoad=e.beforeLoad),this.printErr=null!==(r=e.printErr)&&void 0!==r?r:console.error.bind(console)}return Object.defineProperty(e.prototype,"nextWorkerID",{get:function(){return l},enumerable:!1,configurable:!0}),e.prototype.init=function(){this._childThread||this.initMainThread()},e.prototype.initMainThread=function(){this.preparePool()},e.prototype.preparePool=function(){if(this._reuseWorker&&this._reuseWorker.size)for(var e=this._reuseWorker.size;e--;){var r=this.allocateUnusedWorker();o&&(r.once("message",function(){}),r.unref())}},e.prototype.shouldPreloadWorkers=function(){return!this._childThread&&this._reuseWorker&&this._reuseWorker.size>0},e.prototype.loadWasmModuleToAllWorkers=function(){for(var e=this,r=Array(this.unusedWorkers.length),t=function(e){var t=n.unusedWorkers[e];o&&t.ref(),r[e]=n.loadWasmModuleToWorker(t).then(function(e){return o&&t.unref(),e},function(e){throw o&&t.unref(),e})},n=this,a=0;a<this.unusedWorkers.length;++a)t(a);return Promise.all(r).catch(function(r){throw e.terminateAllThreads(),r})},e.prototype.preloadWorkers=function(){return this.shouldPreloadWorkers()?this.loadWasmModuleToAllWorkers():Promise.resolve([])},e.prototype.setup=function(e,r){this.wasmModule=e,this.wasmMemory=r},e.prototype.markId=function(e){if(e.__emnapi_tid)return e.__emnapi_tid;var r=l+43;return l=(l+1)%536870869,this.pthreads[r]=e,e.__emnapi_tid=r,r},e.prototype.returnWorkerToPool=function(e){var r=e.__emnapi_tid;void 0!==r&&delete this.pthreads[r],this.unusedWorkers.push(e),this.runningWorkers.splice(this.runningWorkers.indexOf(e),1),delete e.__emnapi_tid,o&&e.unref()},e.prototype.loadWasmModuleToWorker=function(e,r){var t=this;if(e.whenLoaded)return e.whenLoaded;var n=this.printErr,a=this._beforeLoad,s=this;return e.whenLoaded=new Promise(function(i,u){e.onmessage=function(r){!function(r){if(r.__emnapi__){var n=r.__emnapi__.type,a=r.__emnapi__.payload;"loaded"===n?(e.loaded=!0,o&&!e.__emnapi_tid&&e.unref(),i(e)):"cleanup-thread"===n&&a.tid in t.pthreads&&t.cleanThread(e,a.tid)}}(r.data),t.fireMessageEvent(e,r)},e.onerror=function(r){var t="worker sent an error!";if(void 0!==e.__emnapi_tid&&(t="worker (tid = "+e.__emnapi_tid+") sent an error!"),"message"in r){if(n(t+" "+r.message),-1!==r.message.indexOf("RuntimeError")||-1!==r.message.indexOf("unreachable"))try{s.terminateAllThreads()}catch(e){}}else n(t);throw u(r),r},o&&(e.on("message",function(r){var t,n;null===(n=(t=e).onmessage)||void 0===n||n.call(t,{data:r})}),e.on("error",function(r){var t,n;null===(n=(t=e).onerror)||void 0===n||n.call(t,r)}),e.on("detachedExit",function(){})),"function"==typeof a&&a(e);try{e.postMessage(f("load",{wasmModule:t.wasmModule,wasmMemory:t.wasmMemory,sab:r}))}catch(e){throw c(t.wasmMemory),e}}),e.whenLoaded},e.prototype.allocateUnusedWorker=function(){var e=this._onCreateWorker;if("function"!=typeof e)throw new TypeError("`options.onCreateWorker` is not provided");var r=e({type:"thread",name:"emnapi-pthread"});return this.unusedWorkers.push(r),r},e.prototype.getNewWorker=function(e){if(this._reuseWorker){if(0===this.unusedWorkers.length){if(this._reuseWorker.strict)if(!o)return void(0,this.printErr)("Tried to spawn a new thread, but the thread pool is exhausted.\nThis might result in a deadlock unless some threads eventually exit or the code explicitly breaks out to the event loop.");var r=this.allocateUnusedWorker();this.loadWasmModuleToWorker(r,e)}return this.unusedWorkers.pop()}var t=this.allocateUnusedWorker();return this.loadWasmModuleToWorker(t,e),this.unusedWorkers.pop()},e.prototype.cleanThread=function(e,r,t){if(!t&&this._reuseWorker)this.returnWorkerToPool(e);else{delete this.pthreads[r];var n=this.runningWorkers.indexOf(e);-1!==n&&this.runningWorkers.splice(n,1),this.terminateWorker(e),delete e.__emnapi_tid}},e.prototype.terminateWorker=function(e){var r,t=this,n=e.__emnapi_tid;e.terminate(),null===(r=this.messageEvents.get(e))||void 0===r||r.clear(),this.messageEvents.delete(e),e.onmessage=function(e){e.data.__emnapi__&&(0,t.printErr)('received "'+e.data.__emnapi__.type+'" command from terminated worker: '+n)}},e.prototype.terminateAllThreads=function(){for(var e=0;e<this.runningWorkers.length;++e)this.terminateWorker(this.runningWorkers[e]);for(e=0;e<this.unusedWorkers.length;++e)this.terminateWorker(this.unusedWorkers[e]);this.unusedWorkers=[],this.runningWorkers=[],this.pthreads=Object.create(null),this.preparePool()},e.prototype.addMessageEventListener=function(e,r){var t=this.messageEvents.get(e);return t||(t=new Set,this.messageEvents.set(e,t)),t.add(r),function(){null==t||t.delete(r)}},e.prototype.fireMessageEvent=function(e,r){var t=this.messageEvents.get(e);if(t){var n=this.printErr;t.forEach(function(e){try{e(r)}catch(e){n(e.stack)}})}},e}(),p=Symbol("kIsProxy");function v(e,r){if(e[p])return e;var t=e.exports,n=function(e){for(var r=["apply","construct","defineProperty","deleteProperty","get","getOwnPropertyDescriptor","getPrototypeOf","has","isExtensible","ownKeys","preventExtensions","set","setPrototypeOf"],t={},n=function(n){var a=r[n];t[a]=function(){var r=Array.prototype.slice.call(arguments,1);return r.unshift(e),Reflect[a].apply(Reflect,r)}},a=0;a<r.length;a++)n(a);return t}(t),a=function(){},o=function(){return 0};n.get=function(e,n,s){var i;return"memory"===n?null!==(i="function"==typeof r?r():r)&&void 0!==i?i:Reflect.get(t,n,s):"_initialize"===n?n in t?a:void 0:"_start"===n?n in t?o:void 0:Reflect.get(t,n,s)},n.has=function(e,r){return"memory"===r||Reflect.has(t,r)};var s=new Proxy(Object.create(null),n);return new Proxy(e,{get:function(e,r,t){return"exports"===r?s:r===p||Reflect.get(e,r,t)}})}var h=new WeakMap,y=function(){function e(e){var r=this;if(!e)throw new TypeError("WASIThreads(): options is not provided");if(!e.wasi)throw new TypeError("WASIThreads(): options.wasi is not provided");h.set(this,new WeakSet);var t=e.wasi;!function(e,r){var t=h.get(e);if(t.has(r))return;var n=e,a=r.wasiImport;if(a){var o=a.proc_exit;a.proc_exit=function(e){return n.terminateAllThreads(),o.call(this,e)}}if(!n.childThread){var s=r.start;"function"==typeof s&&(r.start=function(e){try{return s.call(this,e)}catch(e){throw u(e)&&n.terminateAllThreads(),e}})}t.add(r)}(this,t),this.wasi=t,this.childThread="childThread"in e&&Boolean(e.childThread),this.PThread=void 0,"threadManager"in e?"function"==typeof e.threadManager?this.PThread=e.threadManager():this.PThread=e.threadManager:this.childThread||(this.PThread=new d(e),this.PThread.init());var n=!1;"waitThreadStart"in e&&(n="number"==typeof e.waitThreadStart?e.waitThreadStart:Boolean(e.waitThreadStart));var i=s(e);if(this.childThread&&"function"!=typeof i)throw new TypeError("options.postMessage is not a function");this.postMessage=i;var l=Boolean(e.wasm64),p=function(e){if(e.data.__emnapi__){var t=e.data.__emnapi__.type,n=e.data.__emnapi__.payload;"spawn-thread"===t?v(n.startArg,n.errorOrTid):"terminate-all-threads"===t&&r.terminateAllThreads()}},v=function(e,t){var s,u=void 0!==t;try{c(r.wasmMemory)}catch(e){if(null===(s=r.PThread)||void 0===s||s.printErr(e.stack),u){var d=new Int32Array(r.wasmMemory.buffer,t,2);return Atomics.store(d,0,1),Atomics.store(d,1,6),Atomics.notify(d,1),1}return-6}if(!u){var v=r.wasmInstance.exports.malloc;if(!(t=l?Number(v(BigInt(8))):v(8)))return-48}var h=r.wasmInstance.exports.free,y=l?function(e){h(BigInt(e))}:h,g=new Int32Array(r.wasmMemory.buffer,t,2);if(Atomics.store(g,0,0),Atomics.store(g,1,0),r.childThread){i(f("spawn-thread",{startArg:e,errorOrTid:t})),Atomics.wait(g,1,0);var _=Atomics.load(g,0),E=Atomics.load(g,1);return u?_:(y(t),_?-E:E)}var w,m,L,b=n||0===n;b&&(w=new Int32Array(new SharedArrayBuffer(8208)),Atomics.store(w,0,0));var S=r.PThread;try{if(!(m=S.getNewWorker(w)))throw new Error("failed to get new worker");if(S.addMessageEventListener(m,p),L=S.markId(m),o&&m.ref(),m.postMessage(f("start",{tid:L,arg:e,sab:w})),b){if("number"==typeof n){if("timed-out"===Atomics.wait(w,0,0,n)){try{S.cleanThread(m,L,!0)}catch(e){}throw new Error("Spawning thread timed out. Please check if the worker is created successfully and if message is handled properly in the worker.")}}else Atomics.wait(w,0,0);if(Atomics.load(w,0)>1){try{S.cleanThread(m,L,!0)}catch(e){}throw function(e){var r,t,n=new Int32Array(e);if(Atomics.load(n,0)<=1)return null;var o=Atomics.load(n,1),s=Atomics.load(n,2),i=Atomics.load(n,3),u=new Uint8Array(e),f=u.slice(16,16+o),c=u.slice(16+o,16+o+s),l=u.slice(16+o+s,16+o+s+i),d=(new TextDecoder).decode(f),p=(new TextDecoder).decode(c),v=(new TextDecoder).decode(l),h=new(null!==(r=globalThis[d])&&void 0!==r?r:"RuntimeError"===d&&null!==(t=a.RuntimeError)&&void 0!==t?t:Error)(p);return Object.defineProperty(h,"stack",{value:v,writable:!0,enumerable:!1,configurable:!0}),h}(w.buffer)}}}catch(e){return Atomics.store(g,0,1),Atomics.store(g,1,6),Atomics.notify(g,1),null==S||S.printErr(e.stack),u?1:(y(t),-6)}return Atomics.store(g,0,0),Atomics.store(g,1,L),Atomics.notify(g,1),S.runningWorkers.push(m),b||m.whenLoaded.catch(function(e){throw delete m.whenLoaded,S.cleanThread(m,L,!0),e}),u?0:(y(t),L)};this.threadSpawn=v}return e.prototype.getImportObject=function(){return{wasi:{"thread-spawn":this.threadSpawn}}},e.prototype.setup=function(e,r,t){null!=t||(t=e.exports.memory),this.wasmInstance=e,this.wasmMemory=t,this.PThread&&this.PThread.setup(r,t)},e.prototype.preloadWorkers=function(){return this.PThread?this.PThread.preloadWorkers():Promise.resolve([])},e.prototype.initialize=function(e,r,t){var n=e.exports;null!=t||(t=n.memory),this.childThread&&(e=v(e,t)),this.setup(e,r,t);var a=this.wasi;if("_start"in n&&"function"==typeof n._start)if(this.childThread){a.start(e);try{a[g(a,"kStarted")]=!1}catch(e){}}else!function(e,r){var t=g(e,["kInstance","kSetMemory"]),n=t[0],a=t[1];e[n]=r,e[a](r.exports.memory)}(a,e);else a.initialize(e);return e},e.prototype.start=function(e,r,t){var n=e.exports;return null!=t||(t=n.memory),this.childThread&&(e=v(e,t)),this.setup(e,r,t),{exitCode:this.wasi.start(e),instance:e}},e.prototype.terminateAllThreads=function(){var e;this.childThread?this.postMessage(f("terminate-all-threads",{})):null===(e=this.PThread)||void 0===e||e.terminateAllThreads()},e}();function g(e,r){var t=Object.getOwnPropertySymbols(e),n=function(e){return function(r){return r.description?r.description===e:r.toString()==="Symbol(".concat(e,")")}};return Array.isArray(r)?r.map(function(e){return t.filter(n(e))[0]}):t.filter(n(r))[0]}var _=function(){function e(e){var r=s(e);if("function"!=typeof r)throw new TypeError("options.postMessage is not a function");this.postMessage=r,this.onLoad=null==e?void 0:e.onLoad,this.instance=void 0,this.messagesBeforeLoad=[]}return e.prototype.instantiate=function(e){if("function"==typeof this.onLoad)return this.onLoad(e);throw new Error("ThreadMessageHandler.prototype.instantiate is not implemented")},e.prototype.handle=function(e){var r,t=this;if(null===(r=null==e?void 0:e.data)||void 0===r?void 0:r.__emnapi__){var n=e.data.__emnapi__.type,a=e.data.__emnapi__.payload;"load"===n?this._load(a):"start"===n&&this.handleAfterLoad(e,function(){t._start(a)})}},e.prototype._load=function(e){var r=this;if(void 0===this.instance){var t;try{t=this.instantiate(e)}catch(r){return void this._loaded(r,null,e)}var n=t&&"then"in t?t.then:void 0;"function"==typeof n?n.call(t,function(t){r._loaded(null,t,e)},function(t){r._loaded(t,null,e)}):this._loaded(null,t,e)}},e.prototype._start=function(e){var r=this.instance.exports.wasi_thread_start;if("function"!=typeof r){var t=new TypeError("wasi_thread_start is not exported");throw E(e.sab,2,t),t}var n=this.postMessage,a=e.tid,o=e.arg;E(e.sab,1),r(a,o),n(f("cleanup-thread",{tid:a}))},e.prototype._loaded=function(e,r,t){if(e)throw E(t.sab,2,e),e;if(null==r){var n=new TypeError("onLoad should return an object");throw E(t.sab,2,n),n}var a=r.instance;if(!a){var o=new TypeError('onLoad should return an object which includes "instance"');throw E(t.sab,2,o),o}this.instance=a,(0,this.postMessage)(f("loaded",{}));var s=this.messagesBeforeLoad;this.messagesBeforeLoad=[];for(var i=0;i<s.length;i++){var u=s[i];this.handle({data:u})}},e.prototype.handleAfterLoad=function(e,r){void 0!==this.instance?r.call(this,e):this.messagesBeforeLoad.push(e.data)},e}();function E(e,r,t){e&&(!function(e,r,t){var n=new Int32Array(e);if(Atomics.store(n,0,r),r>1&&t){var a=t.name,o=t.message,s=t.stack,i=(new TextEncoder).encode(a),u=(new TextEncoder).encode(o),f=(new TextEncoder).encode(s);Atomics.store(n,1,i.length),Atomics.store(n,2,u.length),Atomics.store(n,3,f.length);var c=new Uint8Array(e);c.set(i,16),c.set(u,16+i.length),c.set(f,16+i.length+u.length)}}(e.buffer,r,t),Atomics.notify(e,0))}function w(e){var t=function(){var t,n,a,o,s,i="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node,u=Boolean(e.childThread),f="number"==typeof e.waitThreadStart?e.waitThreadStart:Boolean(e.waitThreadStart);function c(e){if("function"==typeof r.RuntimeError)throw new r.RuntimeError(e);throw Error(e)}var l,p,v,h={imports:{env:{},napi:{},emnapi:{}},exports:{},emnapi:{},loaded:!1,filename:"",childThread:u,initWorker:void 0,executeAsyncWork:void 0,waitThreadStart:f,PThread:void 0,init:function(e){if(h.loaded)return h.exports;if(!e)throw new TypeError("Invalid napi init options");var i=e.instance;if(!(null==i?void 0:i.exports))throw new TypeError("Invalid wasm instance");t=i;var u=i.exports,f=e.module,d=e.memory||u.memory,v=e.table||u.__indirect_function_table;if(!(f instanceof r.Module))throw new TypeError("Invalid wasm module");if(!(d instanceof r.Memory))throw new TypeError("Invalid wasm memory");if(!(v instanceof r.Table))throw new TypeError("Invalid wasm table");if(n=d,a=v,"function"!=typeof u.malloc)throw new TypeError("malloc is not exported");if("function"!=typeof u.free)throw new TypeError("free is not exported");if(o=u.malloc,s=u.free,!h.childThread){var y=8,g=i.exports.node_api_module_get_api_version_v1;"function"==typeof g&&(y=g());var _=h.envObject||(h.envObject=l.createEnv(h.filename,y,function(e){return a.get(e)},function(e){return a.get(e)},c,p)),E=l.openScope(_);try{_.callIntoModule(function(e){var r=h.exports,t=E.add(r),n=(0,i.exports.napi_register_wasm_v1)(e.id,t.id);h.exports=n?l.handleStore.get(n).value:r})}finally{l.closeScope(_,E)}return h.loaded=!0,delete h.envObject,h.exports}}},y=void 0;if(u){l=null==e?void 0:e.context;var g="function"==typeof e.postMessage?e.postMessage:"function"==typeof postMessage?postMessage:void 0;if("function"!=typeof g)throw new TypeError("No postMessage found");h.postMessage=g}else{var _=e.context;if("object"!=typeof _||null===_)throw new TypeError("Invalid `options.context`. Use `import { getDefaultContext } from '@emnapi/runtime'`");l=_}if("string"==typeof e.filename&&(h.filename=e.filename),"function"==typeof e.onCreateWorker&&(y=e.onCreateWorker),"function"==typeof e.print?e.print:console.log.bind(console),v="function"==typeof e.printErr?e.printErr:console.warn.bind(console),"nodeBinding"in e){var E=e.nodeBinding;if("object"!=typeof E||null===E)throw new TypeError("Invalid `options.nodeBinding`. Use @emnapi/node-binding package");p=E}var w=0;if("asyncWorkPoolSize"in e){if("number"!=typeof e.asyncWorkPoolSize)throw new TypeError("options.asyncWorkPoolSize must be a integer");(w=e.asyncWorkPoolSize|0)>1024?w=1024:w<-1024&&(w=-1024)}var m=!u&&w<=0;function L(){return Math.abs(w)}function b(e){if(!e)return!1;if(e._emnapiSendListener)return!0;var r=function(e){var r=(i?e:e.data).__emnapi__;if(r&&"async-send"===r.type)if(u){(0,h.postMessage)({__emnapi__:r})}else{var t=r.payload.callback;a.get(t)(r.payload.data)}};return e._emnapiSendListener={handler:r,dispose:function(){i?e.off("message",r):e.removeEventListener("message",r,!1),delete e._emnapiSendListener}},i?e.on("message",r):e.addEventListener("message",r,!1),!0}h.imports.env._emnapi_async_work_pool_size=L,h.emnapi.addSendListener=b;var S=new d(u?{printErr:v,childThread:!0}:{printErr:v,beforeLoad:function(e){b(e)},reuseWorker:e.reuseWorker,onCreateWorker:y});function C(e,r){l.feature.setImmediate(function(){a.get(e)(r)})}function A(e,r){Promise.resolve().then(function(){a.get(e)(r)})}function I(e,r){var t,a=[r>>>0,(t=r,+Math.abs(t)>=1?t>0?(0|Math.min(+Math.floor(t/4294967296),4294967295))>>>0:~~+Math.ceil((t-+(~~t>>>0))/4294967296)>>>0:0)],o=new DataView(n.buffer);o.setInt32(e,a[0],!0),o.setInt32(e+4,a[1],!0)}h.PThread=S;var k,T=Object.freeze({__proto__:null,$emnapiSetValueI64:I,_emnapi_call_finalizer:function(e,r,t,n,a){l.envStore.get(r).callFinalizerInternal(e,t,n,a)},_emnapi_callback_into_module:function(e,r,t,n,o){var s=l.envStore.get(r),i=l.openScope(s);try{s.callbackIntoModule(Boolean(e),function(){a.get(t)(r,n)})}catch(e){throw l.closeScope(s,i),o&&l.closeScope(s),e}l.closeScope(s,i)},_emnapi_ctx_decrease_waiting_request_counter:function(){l.decreaseWaitingRequestCounter()},_emnapi_ctx_increase_waiting_request_counter:function(){l.increaseWaitingRequestCounter()},_emnapi_get_node_version:function(e,r,t){var a="object"==typeof process&&null!==process&&"object"==typeof process.versions&&null!==process.versions&&"string"==typeof process.versions.node?process.versions.node.split(".").map(function(e){return Number(e)}):[0,0,0],o=new DataView(n.buffer);o.setUint32(e,a[0],!0),o.setUint32(r,a[1],!0),o.setUint32(t,a[2],!0)},_emnapi_next_tick:A,_emnapi_runtime_keepalive_pop:function(){},_emnapi_runtime_keepalive_push:function(){},_emnapi_set_immediate:C,napi_clear_last_error:function(e){return l.envStore.get(e).clearLastError()},napi_set_last_error:function(e,r,t,n){return l.envStore.get(e).setLastError(r,t,n)}});function V(e){var r=new DataView(n.buffer).getInt32(e+20,!0);return S.pthreads[r]}var B=new Promise(function(e){k=function(){B.ready=!0,e()}});B.ready=!1;var D=Object.freeze({__proto__:null,_emnapi_after_uvthreadpool_ready:function(e,r,t){B.ready?a.get(e)(r,t):B.then(function(){a.get(e)(r,t)})},_emnapi_async_send_js:function(e,r,t){if(u)(0,h.postMessage)({__emnapi__:{type:"async-send",payload:{callback:r,data:t}}});else switch(e){case 0:C(r,t);break;case 1:A(r,t)}},_emnapi_emit_async_thread_ready:function(){u&&(0,h.postMessage)({__emnapi__:{type:"async-thread-ready",payload:{}}})},_emnapi_is_main_browser_thread:function(){return"undefined"==typeof window||"undefined"==typeof document||i?0:1},_emnapi_tell_js_uvthreadpool:function(e,r){for(var t=[],a=new DataView(n.buffer),o=function(r){var n=V(a.getInt32(e+4*r,!0));t.push(new Promise(function(e){var r=function(t){var a=(i?t:t.data).__emnapi__;a&&"async-thread-ready"===a.type&&(e(),n&&"function"==typeof n.unref&&n.unref(),i?n.off("message",r):n.removeEventListener("message",r))};i?n.on("message",r):n.addEventListener("message",r)}))},s=0;s<r;s++)o(s);Promise.all(t).then(k)},_emnapi_worker_unref:function(e){if(!u){var r=V(e);r&&"function"==typeof r.unref&&r.unref()}}});var x=Object.freeze({__proto__:null,napi_adjust_external_memory:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(!t)return a.setLastError(1);var o=Number(r);if(o<0)return a.setLastError(1);var s=n.buffer.byteLength,i=s+o;return i+=(65536-i%65536)%65536,-1===n.grow(i-s+65535>>16)?a.setLastError(9):(l.feature.supportBigInt?new DataView(n.buffer).setBigInt64(t,BigInt(n.buffer.byteLength),!0):I(t,n.buffer.byteLength),a.clearLastError())}}),M={idGen:{},values:[void 0],queued:new Set,pending:[],init:function(){var e={nextId:1,list:[],generate:function(){var r;return e.list.length?r=e.list.shift():(r=e.nextId,e.nextId++),r},reuse:function(r){e.list.push(r)}};M.idGen=e,M.values=[void 0],M.queued=new Set,M.pending=[]},create:function(e,r,t,n,a,o){var s=0,i=0;if(p){var u=p.node.emitAsyncInit(r,t,-1);s=u.asyncId,i=u.triggerAsyncId}var f=M.idGen.generate();return M.values[f]={env:e,id:f,resource:r,asyncId:s,triggerAsyncId:i,status:0,execute:n,complete:a,data:o},f},callComplete:function(e,r){var t=e.complete,n=e.env,o=e.data,s=function(){if(t){var e=l.envStore.get(n),s=l.openScope(e);try{e.callbackIntoModule(!0,function(){a.get(t)(n,r,o)})}finally{l.closeScope(e,s)}}};p?p.node.makeCallback(e.resource,s,[],{asyncId:e.asyncId,triggerAsyncId:e.triggerAsyncId}):s()},queue:function(e){var r=M.values[e];if(r&&0===r.status){if(r.status=1,M.queued.size>=(Math.abs(w)||4))return void M.pending.push(e);M.queued.add(e);var t=r.env,n=r.data,o=r.execute;r.status=2,l.feature.setImmediate(function(){if(a.get(o)(t,n),M.queued.delete(e),r.status=3,l.feature.setImmediate(function(){M.callComplete(r,0)}),M.pending.length>0){var s=M.pending.shift();M.values[s].status=0,M.queue(s)}})}},cancel:function(e){var r=M.pending.indexOf(e);if(-1!==r){var t=M.values[e];return t&&1===t.status?(t.status=4,M.pending.splice(r,1),l.feature.setImmediate(function(){M.callComplete(t,11)}),0):9}return 9},remove:function(e){var r=M.values[e];r&&(p&&p.node.emitAsyncDestroy({asyncId:r.asyncId,triggerAsyncId:r.triggerAsyncId}),M.values[e]=void 0,M.idGen.reuse(e))}};function W(e,r,t,a){if(p){var o=l.handleStore.get(e).value,s=l.handleStore.get(r).value,i=p.node.emitAsyncInit(o,s,t),u=i.asyncId,f=i.triggerAsyncId;if(a){var c=new DataView(n.buffer);c.setFloat64(a,u,!0),c.setFloat64(a+8,f,!0)}}}function R(e,r){p&&p.node.emitAsyncDestroy({asyncId:e,triggerAsyncId:r})}var j=Object.freeze({__proto__:null,_emnapi_async_destroy_js:function(e){if(!p)return 9;var r=new DataView(n.buffer),t=r.getInt32(e,!0),a=r.getInt32(e+4,!0),o=BigInt(t>>>0)|BigInt(a)<<BigInt(32),s=p.napi.asyncDestroy(o);return 0!==s.status?s.status:0},_emnapi_async_init_js:function(e,r,t){if(!p)return 9;var a;e&&(a=Object(l.handleStore.get(e).value));var o=l.handleStore.get(r).value,s=p.napi.asyncInit(a,o);if(0!==s.status)return s.status;var i=s.value;i>=BigInt(-1)*(BigInt(1)<<BigInt(63))&&i<BigInt(1)<<BigInt(63)||(i&=(BigInt(1)<<BigInt(64))-BigInt(1))>=BigInt(1)<<BigInt(63)&&(i-=BigInt(1)<<BigInt(64));var u=Number(i&BigInt(4294967295)),f=Number(i>>BigInt(32)),c=new DataView(n.buffer);return c.setInt32(t,u,!0),c.setInt32(t+4,f,!0),0},_emnapi_env_check_gc_access:function(e){l.envStore.get(e).checkGCAccess()},_emnapi_node_emit_async_destroy:R,_emnapi_node_emit_async_init:W,_emnapi_node_make_callback:function(e,r,t,a,o,s,i,u){var f,c=0;if(p){var d=l.handleStore.get(r).value,v=l.handleStore.get(t).value;o>>>=0;for(var h=Array(o),y=new DataView(n.buffer);c<o;c++){var g=y.getInt32(a+4*c,!0);h[c]=l.handleStore.get(g).value}var _=p.node.makeCallback(d,v,h,{asyncId:s,triggerAsyncId:i});if(u)f=l.envStore.get(e).ensureHandleId(_),y.setInt32(u,f,!0)}},napi_close_callback_scope:function(e,r){throw new Error("napi_close_callback_scope has not been implemented yet")},napi_make_callback:function(e,r,t,a,o,s,i){var u,f=0;if(!e)return 1;var c=l.envStore.get(e);if(c.checkGCAccess(),!c.tryCatch.isEmpty())return c.setLastError(10);if(!c.canCallIntoJs())return c.setLastError(c.moduleApiVersion>=10?23:10);c.clearLastError();try{if(!p)return c.setLastError(9);if(!t)return c.setLastError(1);if(o>0&&!s)return c.setLastError(1);var d=Object(l.handleStore.get(t).value),v=l.handleStore.get(a).value;if("function"!=typeof v)return c.setLastError(1);var h=new DataView(n.buffer),y=h.getInt32(r,!0),g=h.getInt32(r+4,!0),_=BigInt(y>>>0)|BigInt(g)<<BigInt(32);o>>>=0;for(var E=Array(o);f<o;f++){var w=h.getInt32(s+4*f,!0);E[f]=l.handleStore.get(w).value}var m=p.napi.makeCallback(_,d,v,E);if(m.error)throw m.error;return 0!==m.status?c.setLastError(m.status):(i&&(u=c.ensureHandleId(m.value),h.setInt32(i,u,!0)),c.getReturnStatus())}catch(e){return c.tryCatch.setError(e),c.setLastError(10)}},napi_open_callback_scope:function(e,r,t,n){throw new Error("napi_open_callback_scope has not been implemented yet")}}),U={offset:{resource:0,async_id:8,trigger_async_id:16,queue_size:24,queue:28,thread_count:32,is_closing:36,dispatch_state:40,context:44,max_queue_size:48,ref:52,env:56,finalize_data:60,finalize_cb:64,call_js_cb:68,handles_closing:72,async_ref:76,mutex:80,cond:84,end:88},init:function(){if(void 0!==S){S.unusedWorkers.forEach(U.addListener),S.runningWorkers.forEach(U.addListener);var e=S.getNewWorker;S.getNewWorker=function(){var r=e.apply(this,arguments);return U.addListener(r),r}}},addListener:function(e){if(!e)return!1;if(e._emnapiTSFNListener)return!0;var r=function(e){var r=(i?e:e.data).__emnapi__;if(r){var t=r.type,n=r.payload;"tsfn-send"===t&&U.dispatch(n.tsfn)}};return e._emnapiTSFNListener={handler:r,dispose:function(){i?e.off("message",r):e.removeEventListener("message",r,!1),delete e._emnapiTSFNListener}},i?e.on("message",r):e.addEventListener("message",r,!1),!0},initQueue:function(e){var r=o(8);return!!r&&(new Uint8Array(n.buffer,r,8).fill(0),U.storeSizeTypeValue(e+U.offset.queue,r,!1),!0)},destroyQueue:function(e){var r=U.loadSizeTypeValue(e+U.offset.queue,!1);r&&s(r)},pushQueue:function(e,r){var t=U.loadSizeTypeValue(e+U.offset.queue,!1),n=U.loadSizeTypeValue(t,!1),a=U.loadSizeTypeValue(t+4,!1),s=o(8);if(!s)throw new Error("OOM");U.storeSizeTypeValue(s,r,!1),U.storeSizeTypeValue(s+4,0,!1),0===n&&0===a?(U.storeSizeTypeValue(t,s,!1),U.storeSizeTypeValue(t+4,s,!1)):(U.storeSizeTypeValue(a+4,s,!1),U.storeSizeTypeValue(t+4,s,!1)),U.addQueueSize(e)},shiftQueue:function(e){var r=U.loadSizeTypeValue(e+U.offset.queue,!1),t=U.loadSizeTypeValue(r,!1);if(0===t)return 0;var n=t,a=U.loadSizeTypeValue(t+4,!1);U.storeSizeTypeValue(r,a,!1),0===a&&U.storeSizeTypeValue(r+4,0,!1),U.storeSizeTypeValue(n+4,0,!1);var o=U.loadSizeTypeValue(n,!1);return s(n),U.subQueueSize(e),o},push:function(e,r,t){var n=U.getMutex(e),a=U.getCond(e),o=function(){var r=U.getQueueSize(e),t=U.getMaxQueueSize(e),n=U.getIsClosing(e);return r>=t&&t>0&&!n},s="undefined"!=typeof window&&"undefined"!=typeof document&&!i;return n.execute(function(){for(;o();){if(0===t)return 15;if(s)return 21;a.wait()}return U.getIsClosing(e)?0===U.getThreadCount(e)?1:(U.subThreadCount(e),16):(U.pushQueue(e,r),U.send(e),0)})},getMutex:function(e){var r=e+U.offset.mutex,t={lock:function(){var e="undefined"!=typeof window&&"undefined"!=typeof document&&!i,t=new Int32Array(n.buffer,r,1);if(e)for(;;){if(0===Atomics.compareExchange(t,0,0,1))return}else for(;;){if(0===Atomics.compareExchange(t,0,0,1))return;Atomics.wait(t,0,1)}},unlock:function(){var e=new Int32Array(n.buffer,r,1);if(1!==Atomics.compareExchange(e,0,1,0))throw new Error("Tried to unlock while not holding the mutex");Atomics.notify(e,0,1)},execute:function(e){t.lock();try{return e()}finally{t.unlock()}}};return t},getCond:function(e){var r=e+U.offset.cond,t=U.getMutex(e);return{wait:function(){var e=new Int32Array(n.buffer,r,1),a=Atomics.load(e,0);t.unlock(),Atomics.wait(e,0,a),t.lock()},signal:function(){var e=new Int32Array(n.buffer,r,1);Atomics.add(e,0,1),Atomics.notify(e,0,1)}}},getQueueSize:function(e){return U.loadSizeTypeValue(e+U.offset.queue_size,!0)},addQueueSize:function(e){var r,t,a=U.offset.queue_size;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.add(r,t,1)},subQueueSize:function(e){var r,t,a=U.offset.queue_size;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.sub(r,t,1)},getThreadCount:function(e){return U.loadSizeTypeValue(e+U.offset.thread_count,!0)},addThreadCount:function(e){var r,t,a=U.offset.thread_count;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.add(r,t,1)},subThreadCount:function(e){var r,t,a=U.offset.thread_count;r=new Uint32Array(n.buffer),t=e+a>>2,Atomics.sub(r,t,1)},getIsClosing:function(e){return Atomics.load(new Int32Array(n.buffer),e+U.offset.is_closing>>2)},setIsClosing:function(e,r){Atomics.store(new Int32Array(n.buffer),e+U.offset.is_closing>>2,r)},getHandlesClosing:function(e){return Atomics.load(new Int32Array(n.buffer),e+U.offset.handles_closing>>2)},setHandlesClosing:function(e,r){Atomics.store(new Int32Array(n.buffer),e+U.offset.handles_closing>>2,r)},getDispatchState:function(e){return Atomics.load(new Uint32Array(n.buffer),e+U.offset.dispatch_state>>2)},getContext:function(e){return U.loadSizeTypeValue(e+U.offset.context,!1)},getMaxQueueSize:function(e){return U.loadSizeTypeValue(e+U.offset.max_queue_size,!0)},getEnv:function(e){return U.loadSizeTypeValue(e+U.offset.env,!1)},getCallJSCb:function(e){return U.loadSizeTypeValue(e+U.offset.call_js_cb,!1)},getRef:function(e){return U.loadSizeTypeValue(e+U.offset.ref,!1)},getResource:function(e){return U.loadSizeTypeValue(e+U.offset.resource,!1)},getFinalizeCb:function(e){return U.loadSizeTypeValue(e+U.offset.finalize_cb,!1)},getFinalizeData:function(e){return U.loadSizeTypeValue(e+U.offset.finalize_data,!1)},loadSizeTypeValue:function(e,r){var t;return r?(t=new Uint32Array(n.buffer),Atomics.load(t,e>>2)):(t=new Int32Array(n.buffer),Atomics.load(t,e>>2))},storeSizeTypeValue:function(e,r,t){var a;return t?(a=new Uint32Array(n.buffer),void Atomics.store(a,e>>2,r)):(a=new Int32Array(n.buffer),void Atomics.store(a,e>>2,r>>>0))},destroy:function(e){U.destroyQueue(e);var r=U.getEnv(e),t=l.envStore.get(r),a=U.getRef(e);a&&l.refStore.get(a).dispose(),l.removeCleanupHook(t,U.cleanup,e),t.unref();var o=e+U.offset.async_ref>>2,i=new Int32Array(n.buffer);Atomics.load(i,o)&&(Atomics.store(i,o,0),l.decreaseWaitingRequestCounter());var u=U.getResource(e);if(l.refStore.get(u).dispose(),p){var f=new DataView(n.buffer);R(f.getFloat64(e+U.offset.async_id,!0),f.getFloat64(e+U.offset.trigger_async_id,!0))}s(e)},emptyQueueAndDelete:function(e){for(var r,t=U.getCallJSCb(e),n=U.getContext(e);U.getQueueSize(e)>0;)r=U.shiftQueue(e),t&&a.get(t)(0,0,n,r);U.destroy(e)},finalize:function(e){var r=U.getEnv(e),t=l.envStore.get(r);l.openScope(t);var a=U.getFinalizeCb(e),o=U.getFinalizeData(e),s=U.getContext(e),i=function(){t.callFinalizerInternal(0,a,o,s)};try{if(a)if(p){var u=U.getResource(e),f=l.refStore.get(u).get(),c=l.handleStore.get(f).value,d=new DataView(n.buffer),v=d.getFloat64(e+U.offset.async_id,!0),h=d.getFloat64(e+U.offset.trigger_async_id,!0);p.node.makeCallback(c,i,[],{asyncId:v,triggerAsyncId:h})}else i();U.emptyQueueAndDelete(e)}finally{l.closeScope(t)}},cleanup:function(e){U.closeHandlesAndMaybeDelete(e,1)},closeHandlesAndMaybeDelete:function(e,r){var t=U.getEnv(e),n=l.envStore.get(t);l.openScope(n);try{if(r&&U.getMutex(e).execute(function(){U.setIsClosing(e,1),U.getMaxQueueSize(e)>0&&U.getCond(e).signal()}),U.getHandlesClosing(e))return;U.setHandlesClosing(e,1),l.feature.setImmediate(function(){U.finalize(e)})}finally{l.closeScope(n)}},dispatchOne:function(e){var r=0,t=!1,o=!1,s=U.getMutex(e),i=U.getCond(e);if(s.execute(function(){if(U.getIsClosing(e))U.closeHandlesAndMaybeDelete(e,0);else{var n=U.getQueueSize(e);if(n>0){r=U.shiftQueue(e),t=!0;var a=U.getMaxQueueSize(e);n===a&&a>0&&i.signal(),n--}0===n?0===U.getThreadCount(e)&&(U.setIsClosing(e,1),U.getMaxQueueSize(e)>0&&i.signal(),U.closeHandlesAndMaybeDelete(e,0)):o=!0}}),t){var u=U.getEnv(e),f=l.envStore.get(u);l.openScope(f);var c=function(){f.callbackIntoModule(!1,function(){var t=U.getCallJSCb(e),n=U.getRef(e),o=n?l.refStore.get(n).get():0;if(t){var s=U.getContext(e);a.get(t)(u,o,s,r)}else{var i=o?l.handleStore.get(o).value:null;"function"==typeof i&&i()}})};try{if(p){var d=U.getResource(e),v=l.refStore.get(d).get(),h=l.handleStore.get(v).value,y=new DataView(n.buffer);p.node.makeCallback(h,c,[],{asyncId:y.getFloat64(e+U.offset.async_id,!0),triggerAsyncId:y.getFloat64(e+U.offset.trigger_async_id,!0)})}else c()}finally{l.closeScope(f)}}return o},dispatch:function(e){for(var r=!0,t=1e3,a=new Uint32Array(n.buffer),o=e+U.offset.dispatch_state>>2;r&&0!==--t;)Atomics.store(a,o,1),r=U.dispatchOne(e),1!==Atomics.exchange(a,o,0)&&(r=!0);r&&U.send(e)},send:function(e){1&~Atomics.or(new Uint32Array(n.buffer),e+U.offset.dispatch_state>>2,2)&&(void 0!==u&&u?postMessage({__emnapi__:{type:"tsfn-send",payload:{tsfn:e}}}):l.feature.setImmediate(function(){U.dispatch(e)}))}};var O={unusedWorkers:[],runningWorkers:[],workQueue:[],workerReady:null,offset:{resource:0,async_id:8,trigger_async_id:16,env:24,data:28,execute:32,complete:36,end:40},init:function(){O.unusedWorkers=[],O.runningWorkers=[],O.workQueue=[],O.workerReady=null},addListener:function(e){if(!e)return!1;if(e._emnapiAWMTListener)return!0;var r=function(r){var t=(i?r:r.data).__emnapi__;if(t){var n=t.type,a=t.payload;"async-work-complete"===n?(l.decreaseWaitingRequestCounter(),O.runningWorkers.splice(O.runningWorkers.indexOf(e),1),O.unusedWorkers.push(e),O.checkIdleWorker(),O.callComplete(a.work,0)):"async-work-queue"===n?O.scheduleWork(a.work):"async-work-cancel"===n&&O.cancelWork(a.work)}};return e._emnapiAWMTListener={handler:r,dispose:function(){i?e.off("message",r):e.removeEventListener("message",r,!1),delete e._emnapiAWMTListener}},i?e.on("message",r):e.addEventListener("message",r,!1),!0},initWorkers:function(e){if(u)return O.workerReady||(O.workerReady=Promise.resolve());if(O.workerReady)return O.workerReady;if("function"!=typeof y)throw new TypeError("`options.onCreateWorker` is not a function");var r=[],n=[];if(!("emnapi_async_worker_create"in t.exports))throw new TypeError("`emnapi_async_worker_create` is not exported, please try to add `--export=emnapi_async_worker_create` to linker flags");for(var a=0;a<e;++a)n.push(t.exports.emnapi_async_worker_create());try{var o=function(e){var t=y({type:"async-work",name:"emnapi-async-worker"}),a=S.loadWasmModuleToWorker(t);O.addListener(t),r.push(a.then(function(){"function"==typeof t.unref&&t.unref()})),O.unusedWorkers.push(t);var o=n[e];t.threadBlockBase=o,t.postMessage({__emnapi__:{type:"async-worker-init",payload:{arg:o}}})};for(a=0;a<e;++a)o(a)}catch(r){for(a=0;a<e;++a){var i=n[a];s(i)}throw r}return O.workerReady=Promise.all(r),O.workerReady},checkIdleWorker:function(){if(O.unusedWorkers.length>0&&O.workQueue.length>0){var e=O.unusedWorkers.shift(),r=O.workQueue.shift();O.runningWorkers.push(e),e.postMessage({__emnapi__:{type:"async-work-execute",payload:{work:r}}})}},getResource:function(e){return U.loadSizeTypeValue(e+O.offset.resource,!1)},getExecute:function(e){return U.loadSizeTypeValue(e+O.offset.execute,!1)},getComplete:function(e){return U.loadSizeTypeValue(e+O.offset.complete,!1)},getEnv:function(e){return U.loadSizeTypeValue(e+O.offset.env,!1)},getData:function(e){return U.loadSizeTypeValue(e+O.offset.data,!1)},scheduleWork:function(e){var r;if(u)(0,h.postMessage)({__emnapi__:{type:"async-work-queue",payload:{work:e}}});else if(l.increaseWaitingRequestCounter(),O.workQueue.push(e),null===(r=O.workerReady)||void 0===r?void 0:r.ready)O.checkIdleWorker();else{var t=function(e){throw l.decreaseWaitingRequestCounter(),e};try{O.initWorkers(L()).then(function(){O.workerReady.ready=!0,O.checkIdleWorker()},t)}catch(e){t(e)}}},cancelWork:function(e){if(u)return(0,h.postMessage)({__emnapi__:{type:"async-work-cancel",payload:{work:e}}}),0;var r=O.workQueue.indexOf(e);return-1!==r?(O.workQueue.splice(r,1),l.feature.setImmediate(function(){l.decreaseWaitingRequestCounter(),O.checkIdleWorker(),O.callComplete(e,11)}),0):9},callComplete:function(e,r){var t=O.getComplete(e),o=O.getEnv(e),s=O.getData(e),i=l.envStore.get(o),u=l.openScope(i),f=function(){t&&i.callbackIntoModule(!0,function(){a.get(t)(o,r,s)})};try{if(p){var c=O.getResource(e),d=l.refStore.get(c).get(),v=l.handleStore.get(d).value,h=new DataView(n.buffer),y=h.getFloat64(e+O.offset.async_id,!0),g=h.getFloat64(e+O.offset.trigger_async_id,!0);p.node.makeCallback(v,f,[],{asyncId:y,triggerAsyncId:g})}else f()}finally{l.closeScope(i,u)}}},z=m?function(e,r,t,a,o,s,i){if(!e)return 1;var u,f=l.envStore.get(e);if(f.checkGCAccess(),!a)return f.setLastError(1);if(!i)return f.setLastError(1);if(u=r?Object(l.handleStore.get(r).value):{},!t)return f.setLastError(1);var c=String(l.handleStore.get(t).value),d=M.create(e,u,c,a,o,s);return new DataView(n.buffer).setInt32(i,d,!0),f.clearLastError()}:function(e,r,t,a,s,i,u){if(!e)return 1;var f,c=l.envStore.get(e);if(c.checkGCAccess(),!a)return c.setLastError(1);if(!u)return c.setLastError(1);if(f=r?Object(l.handleStore.get(r).value):{},!t)return c.setLastError(1);var d=O.offset.end,p=o(d);if(!p)return c.setLastError(9);new Uint8Array(n.buffer).subarray(p,p+d).fill(0);var v=c.ensureHandleId(f),h=l.createReference(c,v,1,1).id,y=new DataView(n.buffer);return y.setInt32(p,h,!0),W(v,t,-1,p+O.offset.async_id),y.setInt32(p+O.offset.env,e,!0),y.setInt32(p+O.offset.execute,a,!0),y.setInt32(p+O.offset.complete,s,!0),y.setInt32(p+O.offset.data,i,!0),y.setInt32(u,p,!0),c.clearLastError()},G=m?function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(M.remove(r),t.clearLastError()):t.setLastError(1)}:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=O.getResource(r);if(l.refStore.get(a).dispose(),p){var o=new DataView(n.buffer);R(o.getFloat64(r+O.offset.async_id,!0),o.getFloat64(r+O.offset.trigger_async_id,!0))}return s(r),t.clearLastError()},F=m?function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(M.queue(r),t.clearLastError()):t.setLastError(1)}:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(O.scheduleWork(r),t.clearLastError()):t.setLastError(1)},P=m?function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(!r)return t.setLastError(1);var n=M.cancel(r);return 0===n?t.clearLastError():t.setLastError(n)}:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(!r)return t.setLastError(1);var n=O.cancelWork(r);return 0===n?t.clearLastError():t.setLastError(n)};h.initWorker=function(e){if(!h.childThread)throw new Error("startThread is only available in child threads");if("function"!=typeof t.exports.emnapi_async_worker_init)throw new TypeError("`emnapi_async_worker_init` is not exported, please try to add `--export=emnapi_async_worker_init` to linker flags");t.exports.emnapi_async_worker_init(e)},h.executeAsyncWork=function(e){if(u){var r=O.getExecute(e),t=O.getEnv(e),n=O.getData(e);a.get(r)(t,n),(0,h.postMessage)({__emnapi__:{type:"async-work-complete",payload:{work:e}}})}};var N=Object.freeze({__proto__:null,napi_cancel_async_work:P,napi_create_async_work:z,napi_delete_async_work:G,napi_queue_async_work:F}),J={registry:"function"==typeof FinalizationRegistry?new FinalizationRegistry(function(e){s(e)}):void 0,table:new WeakMap,wasmMemoryViewTable:new WeakMap,init:function(){J.registry="function"==typeof FinalizationRegistry?new FinalizationRegistry(function(e){s(e)}):void 0,J.table=new WeakMap,J.wasmMemoryViewTable=new WeakMap},isDetachedArrayBuffer:function(e){if(0===e.byteLength)try{new Uint8Array(e)}catch(e){return!0}return!1},getArrayBufferPointer:function(e,r){var t,a={address:0,ownership:0,runtimeAllocated:0};if(e===n.buffer)return a;var s=J.isDetachedArrayBuffer(e);if(J.table.has(e)){var i=J.table.get(e);return s?(i.address=0,i):(r&&0===i.ownership&&1===i.runtimeAllocated&&new Uint8Array(n.buffer).set(new Uint8Array(e),i.address),i)}if(s||0===e.byteLength)return a;if(!r)return a;var u=o(e.byteLength);if(!u)throw new Error("Out of memory");return new Uint8Array(n.buffer).set(new Uint8Array(e),u),a.address=u,a.ownership=J.registry?0:1,a.runtimeAllocated=1,J.table.set(e,a),null===(t=J.registry)||void 0===t||t.register(e,u),a},getOrUpdateMemoryView:function(e){if(e.buffer===n.buffer)return J.wasmMemoryViewTable.has(e)||J.wasmMemoryViewTable.set(e,{Ctor:e.constructor,address:e.byteOffset,length:e instanceof DataView?e.byteLength:e.length,ownership:1,runtimeAllocated:0}),e;if((J.isDetachedArrayBuffer(e.buffer)||"function"==typeof SharedArrayBuffer&&e.buffer instanceof SharedArrayBuffer)&&J.wasmMemoryViewTable.has(e)){var r=J.wasmMemoryViewTable.get(e),t=r.Ctor,a=void 0,o=l.feature.Buffer;return a="function"==typeof o&&t===o?o.from(n.buffer,r.address,r.length):new t(n.buffer,r.address,r.length),J.wasmMemoryViewTable.set(a,r),a}return e},getViewPointer:function(e,r){if((e=J.getOrUpdateMemoryView(e)).buffer===n.buffer){if(J.wasmMemoryViewTable.has(e)){var t=J.wasmMemoryViewTable.get(e);return{address:t.address,ownership:t.ownership,runtimeAllocated:t.runtimeAllocated,view:e}}return{address:e.byteOffset,ownership:1,runtimeAllocated:0,view:e}}var a=J.getArrayBufferPointer(e.buffer,r),o=a.address,s=a.ownership,i=a.runtimeAllocated;return{address:0===o?0:o+e.byteOffset,ownership:s,runtimeAllocated:i,view:e}}},q={utf8Decoder:void 0,utf16Decoder:void 0,init:function(){var e,r={decode:function(e){for(var r=0,t=Math.min(4096,e.length+1),n=new Uint16Array(t),a=[],o=0;;){var s=r<e.length;if(!s||o>=t-1){var i=n.subarray(0,o);if(a.push(String.fromCharCode.apply(null,i)),!s)return a.join("");e=e.subarray(r),r=0,o=0}var u=e[r++];if(128&u){if(192==(224&u)){var f=63&e[r++];n[o++]=(31&u)<<6|f}else if(224==(240&u)){f=63&e[r++];var c=63&e[r++];n[o++]=(31&u)<<12|f<<6|c}else if(240==(248&u)){var l=(7&u)<<18|(f=63&e[r++])<<12|(c=63&e[r++])<<6|63&e[r++];l>65535&&(l-=65536,n[o++]=l>>>10&1023|55296,l=56320|1023&l),n[o++]=l}}else n[o++]=u}}};e="function"==typeof TextDecoder?new TextDecoder:r,q.utf8Decoder=e;var t,n={decode:function(e){var r=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2);if(r.length<=4096)return String.fromCharCode.apply(null,r);for(var t=[],n=0,a=0;n<r.length;n+=a)a=Math.min(4096,r.length-n),t.push(String.fromCharCode.apply(null,r.subarray(n,n+a)));return t.join("")}};t="function"==typeof TextDecoder?new TextDecoder("utf-16le"):n,q.utf16Decoder=t},lengthBytesUTF8:function(e){for(var r,t=0,n=0;n<e.length;++n)(r=e.charCodeAt(n))<=127?t++:r<=2047?t+=2:r>=55296&&r<=57343?(t+=4,++n):t+=3;return t},UTF8ToString:function(e,r){if(!e||!r)return"";e>>>=0;var t=new Uint8Array(n.buffer),a=e;if(-1===r)for(;t[a];)++a;else a=e+(r>>>0);if((r=a-e)<=16){for(var o=e,s="";o<a;){var i=t[o++];if(128&i){var u=63&t[o++];if(192!=(224&i)){var f=63&t[o++];if((i=224==(240&i)?(15&i)<<12|u<<6|f:(7&i)<<18|u<<12|f<<6|63&t[o++])<65536)s+=String.fromCharCode(i);else{var c=i-65536;s+=String.fromCharCode(55296|c>>10,56320|1023&c)}}else s+=String.fromCharCode((31&i)<<6|u)}else s+=String.fromCharCode(i)}return s}return q.utf8Decoder.decode("function"==typeof SharedArrayBuffer&&t.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(t.buffer)?t.slice(e,a):t.subarray(e,a))},stringToUTF8:function(e,r,t){var a=new Uint8Array(n.buffer),o=r;if(!(t>0))return 0;for(var s=o>>>=0,i=o+t-1,u=0;u<e.length;++u){var f=e.charCodeAt(u);if(f>=55296&&f<=57343)f=65536+((1023&f)<<10)|1023&e.charCodeAt(++u);if(f<=127){if(o>=i)break;a[o++]=f}else if(f<=2047){if(o+1>=i)break;a[o++]=192|f>>6,a[o++]=128|63&f}else if(f<=65535){if(o+2>=i)break;a[o++]=224|f>>12,a[o++]=128|f>>6&63,a[o++]=128|63&f}else{if(o+3>=i)break;a[o++]=240|f>>18,a[o++]=128|f>>12&63,a[o++]=128|f>>6&63,a[o++]=128|63&f}}return a[o]=0,o-s},UTF16ToString:function(e,r){if(!e||!r)return"";var t=e>>>=0;if(-1===r){for(var a=t>>1,o=new Uint16Array(n.buffer);o[a];)++a;t=a<<1}else t=e+2*(r>>>0);if((r=t-e)<=32)return String.fromCharCode.apply(null,new Uint16Array(n.buffer,e,r/2));var s=new Uint8Array(n.buffer);return q.utf16Decoder.decode("function"==typeof SharedArrayBuffer&&s.buffer instanceof SharedArrayBuffer||"[object SharedArrayBuffer]"===Object.prototype.toString.call(s.buffer)?s.slice(e,t):s.subarray(e,t))},stringToUTF16:function(e,r,t){if(void 0===t&&(t=2147483647),t<2)return 0;for(var a=r,o=(t-=2)<2*e.length?t/2:e.length,s=new DataView(n.buffer),i=0;i<o;++i){var u=e.charCodeAt(i);s.setInt16(r,u,!0),r+=2}return s.setInt16(r,0,!0),r-a},newString:function(e,r,t,a,o){if(!e)return 1;var s=l.envStore.get(e);s.checkGCAccess();var i=-1===t,u=t>>>0;if(0!==t&&!r)return s.setLastError(1);if(!a)return s.setLastError(1);if(!(i||u<=2147483647))return s.setLastError(1);var f=o(r,i,u),c=l.addToCurrentScope(f).id;return new DataView(n.buffer).setInt32(a,c,!0),s.clearLastError()},newExternalString:function(e,r,t,a,o,s,i,u,f){if(!e)return 1;var c=l.envStore.get(e);c.checkGCAccess();var d=-1===t,p=t>>>0;if(0!==t&&!r)return c.setLastError(1);if(!s)return c.setLastError(1);if(!(d||p<=2147483647))return c.setLastError(1);var v=u(e,r,t,s);if(0===v){if(i)new DataView(n.buffer).setInt8(i,1,!0);a&&c.callFinalizer(a,r,o)}return v}};function H(e,r,t,a,o,s,i){if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!r)return u.setLastError(1);var f=l.handleStore.get(r);if(!f.isTypedArray())return u.setLastError(1);var c,d=f.value,p=new DataView(n.buffer);if(t){var v=void 0;if(d instanceof Int8Array)v=0;else if(d instanceof Uint8Array)v=1;else if(d instanceof Uint8ClampedArray)v=2;else if(d instanceof Int16Array)v=3;else if(d instanceof Uint16Array)v=4;else if(d instanceof Int32Array)v=5;else if(d instanceof Uint32Array)v=6;else if(d instanceof Float32Array)v=7;else if(d instanceof Float64Array)v=8;else if(d instanceof BigInt64Array)v=9;else{if(!(d instanceof BigUint64Array))return u.setLastError(9);v=10}p.setInt32(t,v,!0)}if(a&&p.setUint32(a,d.length,!0),o||s){if(c=d.buffer,o){var h=J.getViewPointer(d,!0).address;p.setInt32(o,h,!0)}if(s){var y=u.ensureHandleId(c);p.setInt32(s,y,!0)}}return i&&p.setUint32(i,d.byteOffset,!0),u.clearLastError()}function Q(e,r,t,a,o,s){if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!r)return i.setLastError(1);var u=l.handleStore.get(r);if(!u.isDataView())return i.setLastError(1);var f,c=u.value,d=new DataView(n.buffer);if(t&&d.setUint32(t,c.byteLength,!0),a||o){if(f=c.buffer,a){var p=J.getViewPointer(c,!0).address;d.setInt32(a,p,!0)}if(o){var v=i.ensureHandleId(f);d.setInt32(o,v,!0)}}return s&&d.setUint32(s,c.byteOffset,!0),i.clearLastError()}var $=Object.freeze({__proto__:null,napi_get_array_length:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if(!o.isArray())return a.setLastError(8);var s=o.value.length>>>0;return new DataView(n.buffer).setUint32(t,s,!0),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_get_arraybuffer_info:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!r)return o.setLastError(1);var s=l.handleStore.get(r);if(!s.isArrayBuffer())return o.setLastError(1);var i=new DataView(n.buffer);if(t){var u=J.getArrayBufferPointer(s.value,!0).address;i.setInt32(t,u,!0)}return a&&i.setUint32(a,s.value.byteLength,!0),o.clearLastError()},napi_get_buffer_info:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isBuffer(l.feature.Buffer)?o.isDataView()?Q(e,r,n,t,0,0):H(e,r,0,n,t,0,0):a.setLastError(1)},napi_get_dataview_info:Q,napi_get_date_value:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var s=l.handleStore.get(r);return s.isDate()?(a=s.value.valueOf(),new DataView(n.buffer).setFloat64(t,a,!0),o.getReturnStatus()):o.setLastError(1)}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_get_prototype:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if(null==o.value)throw new TypeError("Cannot convert undefined or null to object");var s=void 0;try{s=o.isObject()||o.isFunction()?o.value:Object(o.value)}catch(e){return a.setLastError(2)}var i=a.ensureHandleId(Object.getPrototypeOf(s));return new DataView(n.buffer).setInt32(t,i,!0),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_get_typedarray_info:H,napi_get_value_bigint_int64:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!l.feature.supportBigInt)return o.setLastError(9);if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.handleStore.get(r).value;if("bigint"!=typeof s)return o.setLastError(6);var i=new DataView(n.buffer);s>=BigInt(-1)*(BigInt(1)<<BigInt(63))&&s<BigInt(1)<<BigInt(63)?i.setInt8(a,1,!0):(i.setInt8(a,0,!0),(s&=(BigInt(1)<<BigInt(64))-BigInt(1))>=BigInt(1)<<BigInt(63)&&(s-=BigInt(1)<<BigInt(64)));var u=Number(s&BigInt(4294967295)),f=Number(s>>BigInt(32));return i.setInt32(t,u,!0),i.setInt32(t+4,f,!0),o.clearLastError()},napi_get_value_bigint_uint64:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!l.feature.supportBigInt)return o.setLastError(9);if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.handleStore.get(r).value;if("bigint"!=typeof s)return o.setLastError(6);var i=new DataView(n.buffer);s>=BigInt(0)&&s<BigInt(1)<<BigInt(64)?i.setInt8(a,1,!0):(i.setInt8(a,0,!0),s&=(BigInt(1)<<BigInt(64))-BigInt(1));var u=Number(s&BigInt(4294967295)),f=Number(s>>BigInt(32));return i.setUint32(t,u,!0),i.setUint32(t+4,f,!0),o.clearLastError()},napi_get_value_bigint_words:function(e,r,t,a,o){if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!l.feature.supportBigInt)return s.setLastError(9);if(!r)return s.setLastError(1);if(!a)return s.setLastError(1);var i=l.handleStore.get(r);if(!i.isBigInt())return s.setLastError(17);for(var u=i.value<BigInt(0),f=new DataView(n.buffer),c=f.getUint32(a,!0),d=0,p=u?i.value*BigInt(-1):i.value;p!==BigInt(0);)d++,p>>=BigInt(64);if(p=u?i.value*BigInt(-1):i.value,t||o){if(!t)return s.setLastError(1);if(!o)return s.setLastError(1);for(var v=[];p!==BigInt(0);){var h=p&(BigInt(1)<<BigInt(64))-BigInt(1);v.push(h),p>>=BigInt(64)}for(var y=Math.min(c,v.length),g=0;g<y;g++){var _=Number(v[g]&BigInt(4294967295)),E=Number(v[g]>>BigInt(32));f.setUint32(o+8*g,_,!0),f.setUint32(o+(8*g+4),E,!0)}f.setInt32(t,u?1:0,!0),f.setUint32(a,y,!0)}else c=d,f.setUint32(a,c,!0);return s.clearLastError()},napi_get_value_bool:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("boolean"!=typeof o.value)return a.setLastError(7);var s=o.value?1:0;return new DataView(n.buffer).setInt8(t,s,!0),a.clearLastError()},napi_get_value_double:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var s=o.value;return new DataView(n.buffer).setFloat64(t,s,!0),a.clearLastError()},napi_get_value_external:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if(!o.isExternal())return a.setLastError(1);var s=o.data();return new DataView(n.buffer).setInt32(t,s,!0),a.clearLastError()},napi_get_value_int32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var s=new Int32Array([o.value])[0];return new DataView(n.buffer).setInt32(t,s,!0),a.clearLastError()},napi_get_value_int64:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var s=o.value,i=new DataView(n.buffer);return s===Number.POSITIVE_INFINITY||s===Number.NEGATIVE_INFINITY||isNaN(s)?(i.setInt32(t,0,!0),i.setInt32(t+4,0,!0)):s<-0x8000000000000000?(i.setInt32(t,0,!0),i.setInt32(t+4,2147483648,!0)):s>=0x8000000000000000?(i.setUint32(t,4294967295,!0),i.setUint32(t+4,2147483647,!0)):I(t,Math.trunc(s)),a.clearLastError()},napi_get_value_string_latin1:function(e,r,t,a,o){if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!r)return s.setLastError(1);a>>>=0;var i=l.handleStore.get(r);if("string"!=typeof i.value)return s.setLastError(3);var u=new DataView(n.buffer);if(t)if(0!==a){for(var f=0,c=void 0,d=0;d<a-1;++d)c=255&i.value.charCodeAt(d),u.setUint8(t+d,c,!0),f++;u.setUint8(t+f,0,!0),o&&u.setUint32(o,f,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return s.setLastError(1);u.setUint32(o,i.value.length,!0)}return s.clearLastError()},napi_get_value_string_utf16:function(e,r,t,a,o){if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!r)return s.setLastError(1);a>>>=0;var i=l.handleStore.get(r);if("string"!=typeof i.value)return s.setLastError(3);var u=new DataView(n.buffer);if(t)if(0!==a){var f=q.stringToUTF16(i.value,t,2*a);o&&u.setUint32(o,f/2,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return s.setLastError(1);u.setUint32(o,i.value.length,!0)}return s.clearLastError()},napi_get_value_string_utf8:function(e,r,t,a,o){if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!r)return s.setLastError(1);a>>>=0;var i=l.handleStore.get(r);if("string"!=typeof i.value)return s.setLastError(3);var u=new DataView(n.buffer);if(t)if(0!==a){var f=q.stringToUTF8(i.value,t,a);o&&u.setUint32(o,f,!0)}else o&&u.setUint32(o,0,!0);else{if(!o)return s.setLastError(1);var c=q.lengthBytesUTF8(i.value);u.setUint32(o,c,!0)}return s.clearLastError()},napi_get_value_uint32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r);if("number"!=typeof o.value)return a.setLastError(6);var s=new Uint32Array([o.value])[0];return new DataView(n.buffer).setUint32(t,s,!0),a.clearLastError()}});function Y(e,r,t,a){return q.newString(e,r,t,a,function(e,r,t){var a="",o=0,s=new DataView(n.buffer);if(r)for(;;){if(!(i=s.getUint8(e,!0)))break;a+=String.fromCharCode(i),e++}else for(;o<t;){var i;if(!(i=s.getUint8(e,!0)))break;a+=String.fromCharCode(i),o++,e++}return a})}function X(e,r,t,n){return q.newString(e,r,t,n,function(e){return q.UTF16ToString(e,t)})}function Z(e,r,t,n){return q.newString(e,r,t,n,function(e){return q.UTF8ToString(e,t)})}var K=Object.freeze({__proto__:null,napi_create_bigint_int64:function(e,r,t,a){if(!e)return 1;var o,s=l.envStore.get(e);if(s.checkGCAccess(),!l.feature.supportBigInt)return s.setLastError(9);if(!t)return s.setLastError(1);o=r;var i=l.addToCurrentScope(o).id;return new DataView(n.buffer).setInt32(t,i,!0),s.clearLastError()},napi_create_bigint_uint64:function(e,r,t,a){if(!e)return 1;var o,s=l.envStore.get(e);if(s.checkGCAccess(),!l.feature.supportBigInt)return s.setLastError(9);if(!t)return s.setLastError(1);o=r&(BigInt(1)<<BigInt(64))-BigInt(1);var i=l.addToCurrentScope(o).id;return new DataView(n.buffer).setInt32(t,i,!0),s.clearLastError()},napi_create_bigint_words:function(e,r,t,a,o){var s,i;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!l.feature.supportBigInt)return u.setLastError(9);if(!o)return u.setLastError(1);if((t>>>=0)>2147483647)return u.setLastError(1);if(t>16384)throw new RangeError("Maximum BigInt size exceeded");var f=BigInt(0),c=new DataView(n.buffer);for(i=0;i<t;i++){var d=c.getUint32(a+8*i,!0),p=c.getUint32(a+(8*i+4),!0);f+=(BigInt(d)|BigInt(p)<<BigInt(32))<<BigInt(64*i)}return f*=BigInt(r)%BigInt(2)===BigInt(0)?BigInt(1):BigInt(-1),s=l.addToCurrentScope(f).id,c.setInt32(o,s,!0),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}},napi_create_double:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=l.addToCurrentScope(r).id;return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()},napi_create_int32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=l.addToCurrentScope(r).id;return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()},napi_create_int64:function(e,r,t,a){if(!e)return 1;var o,s=l.envStore.get(e);if(s.checkGCAccess(),!t)return s.setLastError(1);o=Number(r);var i=l.addToCurrentScope(o).id;return new DataView(n.buffer).setInt32(t,i,!0),s.clearLastError()},napi_create_string_latin1:Y,napi_create_string_utf16:X,napi_create_string_utf8:Z,napi_create_uint32:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=l.addToCurrentScope(r>>>0).id;return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()},node_api_create_external_string_latin1:function(e,r,t,n,a,o,s){return q.newExternalString(e,r,t,n,a,o,s,Y,void 0)},node_api_create_external_string_utf16:function(e,r,t,n,a,o,s){return q.newExternalString(e,r,t,n,a,o,s,X,void 0)},node_api_create_property_key_latin1:function(e,r,t,n){return Y(e,r,t,n)},node_api_create_property_key_utf16:function(e,r,t,n){return X(e,r,t,n)},node_api_create_property_key_utf8:function(e,r,t,n){return Z(e,r,t,n)}});function ee(e,r,t,n,o){var s,i=r&&t?q.UTF8ToString(r,t):"",u=a.get(n),f=function(e){return u(e.id,e.ctx.scopeStore.currentScope.id)},c=function(e,r){return function(){var t=e.ctx.openScope(e),n=t.callbackInfo;n.data=o,n.args=arguments,n.thiz=this,n.fn=s;try{var a=e.callIntoModule(r);return a?e.ctx.handleStore.get(a).value:void 0}finally{n.data=0,n.args=void 0,n.thiz=void 0,n.fn=void 0,e.ctx.closeScope(e,t)}}};if(""===i)return{status:0,f:s=c(e,f)};if(!/^[_$a-zA-Z][_$a-zA-Z0-9]*$/.test(i))return{status:1,f:void 0};if(l.feature.supportNewFunction){var d=c(e,f);try{s=new Function("_","return function "+i+'(){"use strict";return _.apply(this,arguments);};')(d)}catch(r){s=c(e,f),l.feature.canSetFunctionName&&Object.defineProperty(s,"name",{value:i})}}else s=c(e,f),l.feature.canSetFunctionName&&Object.defineProperty(s,"name",{value:i});return{status:0,f:s}}function re(e,r,t,n,a,o,s,i,u){if(a||o){var f=void 0,c=void 0;a&&(f=ee(e,0,0,a,u).f),o&&(c=ee(e,0,0,o,u).f);var d={configurable:!!(4&i),enumerable:!!(2&i),get:f,set:c};Object.defineProperty(r,t,d)}else if(n){d={configurable:!!(4&i),enumerable:!!(2&i),writable:!!(1&i),value:ee(e,0,0,n,u).f};Object.defineProperty(r,t,d)}else{d={configurable:!!(4&i),enumerable:!!(2&i),writable:!!(1&i),value:l.handleStore.get(s).value};Object.defineProperty(r,t,d)}}function te(e){var r=l.handleStore.get(e);return r.isObject()||r.isFunction()?(void 0!==J&&ArrayBuffer.isView(r.value)&&J.wasmMemoryViewTable.has(r.value)&&(r=l.addToCurrentScope(J.wasmMemoryViewTable.get(r.value))),{status:0,handle:r}):{status:1}}function ne(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!r)return s.setLastError(1);if(0===a&&!t)return s.setLastError(1);var i=l.handleStore.get(r);if(!i.isObject()&&!i.isFunction())return s.setLastError(1);var u=s.getObjectBinding(i.value),f=u.wrapped,c=l.refStore.get(f);if(!c)return s.setLastError(1);if(t)o=c.data(),new DataView(n.buffer).setInt32(t,o,!0);return 1===a&&(u.wrapped=0,1===c.ownership()?c.resetFinalizer():c.dispose()),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}}function ae(e,r,t,a,o,s){if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!l.feature.supportFinalizer)return i.setLastError(9);if(!r)return i.setLastError(1);if(!a)return i.setLastError(1);var u=te(r);if(0!==u.status)return i.setLastError(u.status);var f=u.handle,c=s?1:0,d=l.createReferenceWithFinalizer(i,f.id,0,c,a,t,o);if(s){var p=d.id;new DataView(n.buffer).setInt32(s,p,!0)}return i.clearLastError()}var oe=Object.freeze({__proto__:null,napi_add_finalizer:ae,napi_check_object_type_tag:function(e,r,t,a){var o=!0;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!r)return s.setLastError(s.tryCatch.hasCaught()?10:1);var i=l.handleStore.get(r);if(!i.isObject()&&!i.isFunction())return s.setLastError(s.tryCatch.hasCaught()?10:2);if(!t)return s.setLastError(s.tryCatch.hasCaught()?10:1);if(!a)return s.setLastError(s.tryCatch.hasCaught()?10:1);var u=s.getObjectBinding(i.value);if(null!==u.tag){var f=u.tag,c=new Uint32Array(n.buffer,t,4);o=f[0]===c[0]&&f[1]===c[1]&&f[2]===c[2]&&f[3]===c[3]}else o=!1;return new DataView(n.buffer).setInt8(a,o?1:0,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_define_class:function(e,r,t,a,o,s,i,u){var f,c,d;if(!e)return 1;var p=l.envStore.get(e);if(p.checkGCAccess(),!p.tryCatch.isEmpty())return p.setLastError(10);if(!p.canCallIntoJs())return p.setLastError(p.moduleApiVersion>=10?23:10);p.clearLastError();try{if(!u)return p.setLastError(1);if(!a)return p.setLastError(1);if((s>>>=0)>0&&!i)return p.setLastError(1);if(t<-1||t>2147483647||!r)return p.setLastError(1);var v=ee(p,r,t,a,o);if(0!==v.status)return p.setLastError(v.status);for(var h=v.f,y=void 0,g=new DataView(n.buffer),_=0;_<s;_++){f=i+32*_;var E=g.getInt32(f,!0),w=g.getInt32(f+4,!0),m=g.getInt32(f+8,!0),L=g.getInt32(f+12,!0),b=g.getInt32(f+16,!0),S=g.getInt32(f+20,!0);d=g.getInt32(f+24,!0);var C=g.getInt32(f+28,!0);if(E)y=q.UTF8ToString(E,-1);else{if(!w)return p.setLastError(4);if("string"!=typeof(y=l.handleStore.get(w).value)&&"symbol"!=typeof y)return p.setLastError(4)}1024&d?re(p,h,y,m,L,b,S,d,C):re(p,h.prototype,y,m,L,b,S,d,C)}return c=l.addToCurrentScope(h).id,g.setInt32(u,c,!0),p.getReturnStatus()}catch(e){return p.tryCatch.setError(e),p.setLastError(10)}},napi_remove_wrap:function(e,r,t){return ne(e,r,t,1)},napi_type_tag_object:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(a.tryCatch.hasCaught()?10:1);var o=l.handleStore.get(r);if(!o.isObject()&&!o.isFunction())return a.setLastError(a.tryCatch.hasCaught()?10:2);if(!t)return a.setLastError(a.tryCatch.hasCaught()?10:1);var s=a.getObjectBinding(o.value);if(null!==s.tag)return a.setLastError(a.tryCatch.hasCaught()?10:1);var i=new Uint8Array(16);return i.set(new Uint8Array(n.buffer,t,16)),s.tag=new Uint32Array(i.buffer),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_unwrap:function(e,r,t){return ne(e,r,t,0)},napi_wrap:function(e,r,t,a,o,s){return function(e,r,t,a,o,s){var i;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!l.feature.supportFinalizer){if(a)throw l.createNotSupportWeakRefError("napi_wrap",'Parameter "finalize_cb" must be 0(NULL)');if(s)throw l.createNotSupportWeakRefError("napi_wrap",'Parameter "result" must be 0(NULL)')}if(!r)return u.setLastError(1);var f=te(r);if(0!==f.status)return u.setLastError(f.status);var c=f.handle;if(0!==u.getObjectBinding(c.value).wrapped)return u.setLastError(1);var d=void 0;if(s){if(!a)return u.setLastError(1);i=(d=l.createReferenceWithFinalizer(u,c.id,0,1,a,t,o)).id,new DataView(n.buffer).setInt32(s,i,!0)}else d=a?l.createReferenceWithFinalizer(u,c.id,0,0,a,t,o):l.createReferenceWithData(u,c.id,0,0,t);return u.getObjectBinding(c.value).wrapped=d.id,u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}}(e,r,t,a,o,s)},node_api_post_finalizer:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);return a.enqueueFinalizer(l.createTrackedFinalizer(a,r,t,n)),a.clearLastError()}});function se(e,r,t,a,o,s,i){var u;if(!e)return 1;var f=l.envStore.get(e);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!i)return f.setLastError(1);if(a>>>=0,t||(a=0),a>2147483647)throw new RangeError("Cannot create a memory view larger than 2147483647 bytes");if(t+a>n.buffer.byteLength)throw new RangeError("Memory out of range");if(!l.feature.supportFinalizer&&o)throw l.createNotSupportWeakRefError("emnapi_create_memory_view",'Parameter "finalize_cb" must be 0(NULL)');var c=void 0;switch(r){case 0:c={Ctor:Int8Array,address:t,length:a,ownership:1,runtimeAllocated:0};break;case 1:c={Ctor:Uint8Array,address:t,length:a,ownership:1,runtimeAllocated:0};break;case 2:c={Ctor:Uint8ClampedArray,address:t,length:a,ownership:1,runtimeAllocated:0};break;case 3:c={Ctor:Int16Array,address:t,length:a>>1,ownership:1,runtimeAllocated:0};break;case 4:c={Ctor:Uint16Array,address:t,length:a>>1,ownership:1,runtimeAllocated:0};break;case 5:c={Ctor:Int32Array,address:t,length:a>>2,ownership:1,runtimeAllocated:0};break;case 6:c={Ctor:Uint32Array,address:t,length:a>>2,ownership:1,runtimeAllocated:0};break;case 7:c={Ctor:Float32Array,address:t,length:a>>2,ownership:1,runtimeAllocated:0};break;case 8:c={Ctor:Float64Array,address:t,length:a>>3,ownership:1,runtimeAllocated:0};break;case 9:c={Ctor:BigInt64Array,address:t,length:a>>3,ownership:1,runtimeAllocated:0};break;case 10:c={Ctor:BigUint64Array,address:t,length:a>>3,ownership:1,runtimeAllocated:0};break;case-1:c={Ctor:DataView,address:t,length:a,ownership:1,runtimeAllocated:0};break;case-2:if(!l.feature.Buffer)throw l.createNotSupportBufferError("emnapi_create_memory_view","");c={Ctor:l.feature.Buffer,address:t,length:a,ownership:1,runtimeAllocated:0};break;default:return f.setLastError(1)}var d=c.Ctor,p=-2===r?l.feature.Buffer.from(n.buffer,c.address,c.length):new d(n.buffer,c.address,c.length),v=l.addToCurrentScope(p);if(J.wasmMemoryViewTable.set(p,c),o){var h=ae(e,v.id,t,o,s,0);if(10===h){var y=f.tryCatch.extractException();throw f.clearLastError(),y}if(0!==h)return f.setLastError(h)}return u=v.id,new DataView(n.buffer).setInt32(i,u,!0),f.getReturnStatus()}catch(y){return f.tryCatch.setError(y),f.setLastError(10)}}function ie(e,r,t,a){var o;if(t=null!=t?t:0,t>>>=0,r instanceof ArrayBuffer){if(!(i=J.getArrayBufferPointer(r,!1).address))throw new Error("Unknown ArrayBuffer address");if("number"==typeof a&&-1!==a||(a=r.byteLength-t),0===(a>>>=0))return r;o=new Uint8Array(r,t,a);var s=new Uint8Array(n.buffer);return e?s.set(o,i):o.set(s.subarray(i,i+a)),r}if(ArrayBuffer.isView(r)){var i,u=J.getViewPointer(r,!1),f=u.view;if(!(i=u.address))throw new Error("Unknown ArrayBuffer address");if("number"==typeof a&&-1!==a||(a=f.byteLength-t),0===(a>>>=0))return f;o=new Uint8Array(f.buffer,f.byteOffset+t,a);s=new Uint8Array(n.buffer);return e?s.set(o,i):o.set(s.subarray(i,i+a)),f}throw new TypeError("emnapiSyncMemory expect ArrayBuffer or ArrayBufferView as first parameter")}function ue(e){var r,t=e instanceof ArrayBuffer,n=e instanceof DataView,a=ArrayBuffer.isView(e)&&!n;if(!t&&!a&&!n)throw new TypeError("emnapiGetMemoryAddress expect ArrayBuffer or ArrayBufferView as first parameter");return{address:(r=t?J.getArrayBufferPointer(e,!1):J.getViewPointer(e,!1)).address,ownership:r.ownership,runtimeAllocated:r.runtimeAllocated}}var fe=Object.freeze({__proto__:null,$emnapiGetMemoryAddress:ue,$emnapiSyncMemory:ie,emnapi_create_memory_view:se,emnapi_get_memory_address:function(e,r,t,a,o){var s,i,u,f;if(!e)return 1;var c=l.envStore.get(e);if(c.checkGCAccess(),!c.tryCatch.isEmpty())return c.setLastError(10);if(!c.canCallIntoJs())return c.setLastError(c.moduleApiVersion>=10?23:10);c.clearLastError();try{if(!r)return c.setLastError(1);if(!t&&!a&&!o)return c.setLastError(1);s=(f=ue(c.ctx.handleStore.get(r).value)).address;var d=new DataView(n.buffer);return t&&d.setInt32(t,s,!0),a&&(u=f.ownership,d.setInt32(a,u,!0)),o&&(i=f.runtimeAllocated,d.setInt8(o,i,!0)),c.getReturnStatus()}catch(e){return c.tryCatch.setError(e),c.setLastError(10)}},emnapi_get_runtime_version:function(e,r){if(!e)return 1;var t,a=l.envStore.get(e);if(!r)return a.setLastError(1);try{t=l.getRuntimeVersions().version}catch(e){return a.setLastError(9)}var o=t.split(".").map(function(e){return Number(e)}),s=new DataView(n.buffer);return s.setUint32(r,o[0],!0),s.setUint32(r+4,o[1],!0),s.setUint32(r+8,o[2],!0),a.clearLastError()},emnapi_is_node_binding_available:function(){return p?1:0},emnapi_is_support_bigint:function(){return l.feature.supportBigInt?1:0},emnapi_is_support_weakref:function(){return l.feature.supportFinalizer?1:0},emnapi_sync_memory:function(e,r,t,a,o){var s;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);var u=new DataView(n.buffer),f=u.getInt32(t,!0),c=i.ctx.handleStore.get(f);if(!c.isArrayBuffer()&&!c.isTypedArray()&&!c.isDataView())return i.setLastError(1);var d=ie(Boolean(r),c.value,a,o);return c.value!==d&&(s=i.ensureHandleId(d),u.setInt32(t,s,!0)),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}}});function ce(e,r){e>>>=0;var t=new ArrayBuffer(e);if(r){var a=J.getArrayBufferPointer(t,!0).address;new DataView(n.buffer).setInt32(r,a,!0)}return t}var le=Object.freeze({__proto__:null,napi_create_array:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.addToCurrentScope([]).id;return new DataView(n.buffer).setInt32(r,a,!0),t.clearLastError()},napi_create_array_with_length:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);r>>>=0;var o=l.addToCurrentScope(new Array(r)).id;return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()},napi_create_arraybuffer:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!a)return s.setLastError(1);var i=ce(r,t);return o=l.addToCurrentScope(i).id,new DataView(n.buffer).setInt32(a,o,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_create_buffer:function(e,r,t,a){var s,i,u;if(!e)return 1;var f=l.envStore.get(e);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!a)return f.setLastError(1);var c=l.feature.Buffer;if(!c)throw l.createNotSupportBufferError("napi_create_buffer","");var d=void 0;r>>>=0;var p=new DataView(n.buffer);if(t&&0!==r){if(!(u=o(r)))throw new Error("Out of memory");new Uint8Array(n.buffer).subarray(u,u+r).fill(0);var v=c.from(n.buffer,u,r),h={Ctor:c,address:u,length:r,ownership:J.registry?0:1,runtimeAllocated:1};J.wasmMemoryViewTable.set(v,h),null===(s=J.registry)||void 0===s||s.register(h,u),i=l.addToCurrentScope(v).id,p.setInt32(a,i,!0),p.setInt32(t,u,!0)}else d=c.alloc(r),i=l.addToCurrentScope(d).id,p.setInt32(a,i,!0);return f.getReturnStatus()}catch(e){return f.tryCatch.setError(e),f.setLastError(10)}},napi_create_buffer_copy:function(e,r,t,a,o){var s;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!o)return i.setLastError(1);var u=l.feature.Buffer;if(!u)throw l.createNotSupportBufferError("napi_create_buffer_copy","");var f=ce(r,a),c=u.from(f);return c.set(new Uint8Array(n.buffer).subarray(t,t+r)),s=l.addToCurrentScope(c).id,new DataView(n.buffer).setInt32(o,s,!0),i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_create_dataview:function(e,r,t,a,o){var s;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!t)return i.setLastError(1);if(!o)return i.setLastError(1);r>>>=0,a>>>=0;var u=l.handleStore.get(t);if(!u.isArrayBuffer())return i.setLastError(1);var f=u.value;if(r+a>f.byteLength){var c=new RangeError("byte_offset + byte_length should be less than or equal to the size in bytes of the array passed in");throw c.code="ERR_NAPI_INVALID_DATAVIEW_ARGS",c}var d=new DataView(f,a,r);return f===n.buffer&&(J.wasmMemoryViewTable.has(d)||J.wasmMemoryViewTable.set(d,{Ctor:DataView,address:a,length:r,ownership:1,runtimeAllocated:0})),s=l.addToCurrentScope(d).id,new DataView(n.buffer).setInt32(o,s,!0),i.getReturnStatus()}catch(c){return i.tryCatch.setError(c),i.setLastError(10)}},napi_create_date:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{return t?(a=l.addToCurrentScope(new Date(r)).id,new DataView(n.buffer).setInt32(t,a,!0),o.getReturnStatus()):o.setLastError(1)}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_create_external:function(e,r,t,a,o){var s;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!o)return i.setLastError(1);if(!l.feature.supportFinalizer&&t)throw l.createNotSupportWeakRefError("napi_create_external",'Parameter "finalize_cb" must be 0(NULL)');var u=l.getCurrentScope().addExternal(r);return t&&l.createReferenceWithFinalizer(i,u.id,0,0,t,r,a),s=u.id,new DataView(n.buffer).setInt32(o,s,!0),i.clearLastError()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_create_external_arraybuffer:function(e,r,t,a,o,s){var i;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!s)return u.setLastError(1);if(t>>>=0,r||(t=0),r+t>n.buffer.byteLength)throw new RangeError("Memory out of range");if(!l.feature.supportFinalizer&&a)throw l.createNotSupportWeakRefError("napi_create_external_arraybuffer",'Parameter "finalize_cb" must be 0(NULL)');var f=new ArrayBuffer(t);if(0===t)try{(new(0,l.feature.MessageChannel)).port1.postMessage(f,[f])}catch(e){}else new Uint8Array(f).set(new Uint8Array(n.buffer).subarray(r,r+t)),J.table.set(f,{address:r,ownership:1,runtimeAllocated:0});var c=l.addToCurrentScope(f);if(a){var d=ae(e,c.id,r,a,o,0);if(10===d){var p=u.tryCatch.extractException();throw u.clearLastError(),p}if(0!==d)return u.setLastError(d)}return i=c.id,new DataView(n.buffer).setInt32(s,i,!0),u.getReturnStatus()}catch(p){return u.tryCatch.setError(p),u.setLastError(10)}},napi_create_external_buffer:function(e,r,t,n,a,o){return se(e,-2,t,r,n,a,o)},napi_create_object:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.addToCurrentScope({}).id;return new DataView(n.buffer).setInt32(r,a,!0),t.clearLastError()},napi_create_symbol:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=new DataView(n.buffer);if(r){var s=l.handleStore.get(r).value;if("string"!=typeof s)return a.setLastError(3);var i=l.addToCurrentScope(Symbol(s)).id;o.setInt32(t,i,!0)}else{var u=l.addToCurrentScope(Symbol()).id;o.setInt32(t,u,!0)}return a.clearLastError()},napi_create_typedarray:function(e,r,t,a,o,s){var i;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!a)return u.setLastError(1);if(!s)return u.setLastError(1);var f=l.handleStore.get(a);if(!f.isArrayBuffer())return u.setLastError(1);var c=f.value,d=function(e,r,t,a,o,u){var f,c;if((o>>>=0,u>>>=0,t>1)&&o%t!==0)return(c=new RangeError("start offset of ".concat(null!==(f=r.name)&&void 0!==f?f:""," should be a multiple of ").concat(t))).code="ERR_NAPI_INVALID_TYPEDARRAY_ALIGNMENT",e.tryCatch.setError(c),e.setLastError(9);if(u*t+o>a.byteLength)return(c=new RangeError("Invalid typed array length")).code="ERR_NAPI_INVALID_TYPEDARRAY_LENGTH",e.tryCatch.setError(c),e.setLastError(9);var d=new r(a,o,u);return a===n.buffer&&(J.wasmMemoryViewTable.has(d)||J.wasmMemoryViewTable.set(d,{Ctor:r,address:o,length:u,ownership:1,runtimeAllocated:0})),i=l.addToCurrentScope(d).id,new DataView(n.buffer).setInt32(s,i,!0),e.getReturnStatus()};switch(r){case 0:return d(u,Int8Array,1,c,o,t);case 1:return d(u,Uint8Array,1,c,o,t);case 2:return d(u,Uint8ClampedArray,1,c,o,t);case 3:return d(u,Int16Array,2,c,o,t);case 4:return d(u,Uint16Array,2,c,o,t);case 5:return d(u,Int32Array,4,c,o,t);case 6:return d(u,Uint32Array,4,c,o,t);case 7:return d(u,Float32Array,4,c,o,t);case 8:return d(u,Float64Array,8,c,o,t);case 9:return d(u,BigInt64Array,8,c,o,t);case 10:return d(u,BigUint64Array,8,c,o,t);default:return u.setLastError(1)}}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}},node_api_create_buffer_from_arraybuffer:function(e,r,t,a,o){var s;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if(!r)return i.setLastError(1);if(!o)return i.setLastError(1);t>>>=0,a>>>=0;var u=l.handleStore.get(r);if(!u.isArrayBuffer())return i.setLastError(1);var f=u.value;if(a+t>f.byteLength){var c=new RangeError("The byte offset + length is out of range");throw c.code="ERR_OUT_OF_RANGE",c}var d=l.feature.Buffer;if(!d)throw l.createNotSupportBufferError("node_api_create_buffer_from_arraybuffer","");var p=d.from(f,t,a);return f===n.buffer&&(J.wasmMemoryViewTable.has(p)||J.wasmMemoryViewTable.set(p,{Ctor:d,address:t,length:a,ownership:1,runtimeAllocated:0})),s=l.addToCurrentScope(p).id,new DataView(n.buffer).setInt32(o,s,!0),i.getReturnStatus()}catch(c){return i.tryCatch.setError(c),i.setLastError(10)}},node_api_symbol_for:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!a)return o.setLastError(1);var s=-1===t,i=t>>>0;if(0!==t&&!r)return o.setLastError(1);if(!(s||i<=2147483647))return o.setLastError(1);var u=q.UTF8ToString(r,t),f=l.addToCurrentScope(Symbol.for(u)).id;return new DataView(n.buffer).setInt32(a,f,!0),o.clearLastError()}});var de=Object.freeze({__proto__:null,napi_get_boolean:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!t)return a.setLastError(1);var o=0===r?3:4;return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()},napi_get_global:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(new DataView(n.buffer).setInt32(r,5,!0),t.clearLastError()):t.setLastError(1)},napi_get_null:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(new DataView(n.buffer).setInt32(r,2,!0),t.clearLastError()):t.setLastError(1)},napi_get_undefined:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?(new DataView(n.buffer).setInt32(r,1,!0),t.clearLastError()):t.setLastError(1)}});var pe=Object.freeze({__proto__:null,napi_get_instance_data:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(!r)return t.setLastError(1);var a=t.getInstanceData();return new DataView(n.buffer).setInt32(r,a,!0),t.clearLastError()},napi_set_instance_data:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);return a.setInstanceData(r,t,n),a.clearLastError()}});var ve=Object.freeze({__proto__:null,_emnapi_get_last_error_info:function(e,r,t,a){var o=l.envStore.get(e).lastError,s=o.errorCode,i=o.engineErrorCode>>>0,u=o.engineReserved,f=new DataView(n.buffer);f.setInt32(r,s,!0),f.setUint32(t,i,!0),f.setInt32(a,u,!0)},napi_create_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new Error(s);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=l.addToCurrentScope(i).id;return new DataView(n.buffer).setInt32(a,f,!0),o.clearLastError()},napi_create_range_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new RangeError(s);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=l.addToCurrentScope(i).id;return new DataView(n.buffer).setInt32(a,f,!0),o.clearLastError()},napi_create_type_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new TypeError(s);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=l.addToCurrentScope(i).id;return new DataView(n.buffer).setInt32(a,f,!0),o.clearLastError()},napi_fatal_error:function(e,r,t,n){var a=q.UTF8ToString(e,r),o=q.UTF8ToString(t,n);p?p.napi.fatalError(a,o):c("FATAL ERROR: "+a+" "+o)},napi_fatal_exception:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!r)return t.setLastError(1);var n=t.ctx.handleStore.get(r);try{t.triggerFatalException(n.value)}catch(e){return t.setLastError(9)}return t.clearLastError()}catch(r){return t.tryCatch.setError(r),t.setLastError(10)}},napi_get_and_clear_last_exception:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=new DataView(n.buffer);if(!t.tryCatch.hasCaught())return a.setInt32(r,1,!0),t.clearLastError();var o=t.tryCatch.exception(),s=t.ensureHandleId(o);return a.setInt32(r,s,!0),t.tryCatch.reset(),t.clearLastError()},napi_is_exception_pending:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=t.tryCatch.hasCaught();return new DataView(n.buffer).setInt8(r,a?1:0,!0),t.clearLastError()},napi_throw:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{return r?(t.tryCatch.setError(l.handleStore.get(r).value),t.clearLastError()):t.setLastError(1)}catch(e){return t.tryCatch.setError(e),t.setLastError(10)}},napi_throw_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new Error(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},napi_throw_range_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new RangeError(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},napi_throw_type_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new TypeError(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},node_api_create_syntax_error:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!t)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.handleStore.get(t).value;if("string"!=typeof s)return o.setLastError(3);var i=new SyntaxError(s);if(r){var u=l.handleStore.get(r).value;if("string"!=typeof u)return o.setLastError(3);i.code=u}var f=l.addToCurrentScope(i).id;return new DataView(n.buffer).setInt32(a,f,!0),o.clearLastError()},node_api_throw_syntax_error:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{if(!t)return n.setLastError(1);var a=new SyntaxError(q.UTF8ToString(t,-1));return r&&(a.code=q.UTF8ToString(r,-1)),n.tryCatch.setError(a),n.clearLastError()}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}}});var he=Object.freeze({__proto__:null,napi_call_function:function(e,r,t,a,o,s){var i,u=0;if(!e)return 1;var f=l.envStore.get(e);if(f.checkGCAccess(),!f.tryCatch.isEmpty())return f.setLastError(10);if(!f.canCallIntoJs())return f.setLastError(f.moduleApiVersion>=10?23:10);f.clearLastError();try{if(!r)return f.setLastError(1);if((a>>>=0)>0&&!o)return f.setLastError(1);var c=l.handleStore.get(r).value;if(!t)return f.setLastError(1);var d=l.handleStore.get(t).value;if("function"!=typeof d)return f.setLastError(1);for(var p=[],v=new DataView(n.buffer);u<a;u++){var h=v.getInt32(o+4*u,!0);p.push(l.handleStore.get(h).value)}var y=d.apply(c,p);return s&&(i=f.ensureHandleId(y),v.setInt32(s,i,!0)),f.clearLastError()}catch(e){return f.tryCatch.setError(e),f.setLastError(10)}},napi_create_function:function(e,r,t,a,o,s){var i;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!s)return u.setLastError(1);if(!a)return u.setLastError(1);var f=ee(u,r,t,a,o);if(0!==f.status)return u.setLastError(f.status);var c=f.f;return i=l.addToCurrentScope(c).id,new DataView(n.buffer).setInt32(s,i,!0),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}},napi_get_cb_info:function(e,r,t,a,o,s){if(!e)return 1;var i=l.envStore.get(e);if(!r)return i.setLastError(1);var u=l.scopeStore.get(r).callbackInfo,f=new DataView(n.buffer);if(a){if(!t)return i.setLastError(1);for(var c=f.getUint32(t,!0),d=u.args.length,p=c<d?c:d,v=0;v<p;v++){var h=i.ensureHandleId(u.args[v]);f.setInt32(a+4*v,h,!0)}if(v<c)for(;v<c;v++)f.setInt32(a+4*v,1,!0)}if(t&&f.setUint32(t,u.args.length,!0),o){var y=i.ensureHandleId(u.thiz);f.setInt32(o,y,!0)}return s&&f.setInt32(s,u.data,!0),i.clearLastError()},napi_get_new_target:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.scopeStore.get(r).callbackInfo,s=o.thiz,i=o.fn,u=null==s||null==s.constructor?0:s instanceof i?a.ensureHandleId(s.constructor):0;return new DataView(n.buffer).setInt32(t,u,!0),a.clearLastError()},napi_new_instance:function(e,r,t,a,o){var s,i;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!r)return u.setLastError(1);if((t>>>=0)>0&&!a)return u.setLastError(1);if(!o)return u.setLastError(1);var f=l.handleStore.get(r).value;if("function"!=typeof f)return u.setLastError(1);var c=void 0,d=new DataView(n.buffer);if(l.feature.supportReflect){var p=Array(t);for(s=0;s<t;s++){var v=d.getInt32(a+4*s,!0);p[s]=l.handleStore.get(v).value}c=Reflect.construct(f,p,f)}else{var h=Array(t+1);for(h[0]=void 0,s=0;s<t;s++){v=d.getInt32(a+4*s,!0);h[s+1]=l.handleStore.get(v).value}c=new(f.bind.apply(f,h))}return o&&(i=u.ensureHandleId(c),d.setInt32(o,i,!0)),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}}});var ye=Object.freeze({__proto__:null,_emnapi_env_ref:function(e){l.envStore.get(e).ref()},_emnapi_env_unref:function(e){l.envStore.get(e).unref()},napi_add_env_cleanup_hook:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);return r?(l.addCleanupHook(n,r,t),0):n.setLastError(1)},napi_close_escapable_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?0===t.openHandleScopes?13:(l.closeScope(t),t.clearLastError()):t.setLastError(1)},napi_close_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return t.checkGCAccess(),r?0===t.openHandleScopes?13:(l.closeScope(t),t.clearLastError()):t.setLastError(1)},napi_create_reference:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!r)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.handleStore.get(r);if(o.moduleApiVersion<10&&!(s.isObject()||s.isFunction()||s.isSymbol()))return o.setLastError(1);var i=l.createReference(o,s.id,t>>>0,1);return new DataView(n.buffer).setInt32(a,i.id,!0),o.clearLastError()},napi_delete_reference:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(l.refStore.get(r).dispose(),t.clearLastError()):t.setLastError(1)},napi_escape_handle:function(e,r,t,a){if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!r)return o.setLastError(1);if(!t)return o.setLastError(1);if(!a)return o.setLastError(1);var s=l.scopeStore.get(r);if(!s.escapeCalled()){var i=s.escape(t),u=i?i.id:0;return new DataView(n.buffer).setInt32(a,u,!0),o.clearLastError()}return o.setLastError(12)},napi_get_reference_value:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.refStore.get(r).get(a);return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()},napi_open_escapable_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.openScope(t);return new DataView(n.buffer).setInt32(r,a.id,!0),t.clearLastError()},napi_open_handle_scope:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var a=l.openScope(t);return new DataView(n.buffer).setInt32(r,a.id,!0),t.clearLastError()},napi_reference_ref:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);var o=l.refStore.get(r).ref();return t&&new DataView(n.buffer).setUint32(t,o,!0),a.clearLastError()},napi_reference_unref:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);var o=l.refStore.get(r);if(0===o.refcount())return a.setLastError(9);var s=o.unref();return t&&new DataView(n.buffer).setUint32(t,s,!0),a.clearLastError()},napi_remove_env_cleanup_hook:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);return r?(l.removeCleanupHook(n,r,t),0):n.setLastError(1)}});var ge=Object.freeze({__proto__:null,_emnapi_get_filename:function(e,r,t){var n=l.envStore.get(e).filename;return r?q.stringToUTF8(n,r,t):q.lengthBytesUTF8(n)}});var _e=Object.freeze({__proto__:null,napi_create_promise:function(e,r,t){var a,o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!r)return s.setLastError(1);if(!t)return s.setLastError(1);var i=new DataView(n.buffer),u=new Promise(function(e,t){var n=l.createDeferred({resolve:e,reject:t});a=n.id,i.setInt32(r,a,!0)});return o=l.addToCurrentScope(u).id,i.setInt32(t,o,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_is_promise:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isPromise()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_reject_deferred:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{return r&&t?(l.deferredStore.get(r).reject(l.handleStore.get(t).value),n.getReturnStatus()):n.setLastError(1)}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}},napi_resolve_deferred:function(e,r,t){if(!e)return 1;var n=l.envStore.get(e);if(n.checkGCAccess(),!n.tryCatch.isEmpty())return n.setLastError(10);if(!n.canCallIntoJs())return n.setLastError(n.moduleApiVersion>=10?23:10);n.clearLastError();try{return r&&t?(l.deferredStore.get(r).resolve(l.handleStore.get(t).value),n.getReturnStatus()):n.setLastError(1)}catch(e){return n.tryCatch.setError(e),n.setLastError(10)}}});function Ee(e,r,t,a,o,s){var i;if(!e)return 1;var u=l.envStore.get(e);if(u.checkGCAccess(),!u.tryCatch.isEmpty())return u.setLastError(10);if(!u.canCallIntoJs())return u.setLastError(u.moduleApiVersion>=10?23:10);u.clearLastError();try{if(!s)return u.setLastError(1);if(!r)return u.setLastError(1);var f=l.handleStore.get(r);if(null==f.value)throw new TypeError("Cannot convert undefined or null to object");var c=void 0;try{c=f.isObject()||f.isFunction()?f.value:Object(f.value)}catch(e){return u.setLastError(2)}if(0!==t&&1!==t)return u.setLastError(1);if(0!==o&&1!==o)return u.setLastError(1);var d=[],p=void 0,v=void 0,h=void 0,y=!0,g=/^(0|[1-9][0-9]*)$/;do{for(p=Object.getOwnPropertyNames(c),v=Object.getOwnPropertySymbols(c),h=0;h<p.length;h++)d.push({name:g.test(p[h])?Number(p[h]):p[h],desc:Object.getOwnPropertyDescriptor(c,p[h]),own:y});for(h=0;h<v.length;h++)d.push({name:v[h],desc:Object.getOwnPropertyDescriptor(c,v[h]),own:y});if(1===t)break;c=Object.getPrototypeOf(c),y=!1}while(c);var _=[],E=function(e,r,t,n){if(-1===e.indexOf(r))if(0===n)e.push(r);else if(1===n){var a="number"==typeof r?String(r):r;"string"==typeof a&&8&t||e.push(a)}};for(h=0;h<d.length;h++){var w=d[h],m=w.name,L=w.desc;if(0===a)E(_,m,a,o);else{if(8&a&&"string"==typeof m)continue;if(16&a&&"symbol"==typeof m)continue;var b=!0;switch(7&a){case 1:b=Boolean(L.writable);break;case 2:b=Boolean(L.enumerable);break;case 3:b=Boolean(L.writable&&L.enumerable);break;case 4:b=Boolean(L.configurable);break;case 5:b=Boolean(L.configurable&&L.writable);break;case 6:b=Boolean(L.configurable&&L.enumerable);break;case 7:b=Boolean(L.configurable&&L.enumerable&&L.writable)}b&&E(_,m,a,o)}}return i=l.addToCurrentScope(_).id,new DataView(n.buffer).setInt32(s,i,!0),u.getReturnStatus()}catch(e){return u.tryCatch.setError(e),u.setLastError(10)}}var we=Object.freeze({__proto__:null,napi_define_properties:function(e,r,t,a){var o,s;if(!e)return 1;var i=l.envStore.get(e);if(i.checkGCAccess(),!i.tryCatch.isEmpty())return i.setLastError(10);if(!i.canCallIntoJs())return i.setLastError(i.moduleApiVersion>=10?23:10);i.clearLastError();try{if((t>>>=0)>0&&!a)return i.setLastError(1);if(!r)return i.setLastError(1);var u=l.handleStore.get(r),f=u.value;if(!u.isObject()&&!u.isFunction())return i.setLastError(2);for(var c=void 0,d=new DataView(n.buffer),p=0;p<t;p++){o=a+32*p;var v=d.getInt32(o,!0),h=d.getInt32(o+4,!0),y=d.getInt32(o+8,!0),g=d.getInt32(o+12,!0),_=d.getInt32(o+16,!0),E=d.getInt32(o+20,!0);s=d.getInt32(o+24,!0);var w=d.getInt32(o+28,!0);if(v)c=q.UTF8ToString(v,-1);else{if(!h)return i.setLastError(4);if("string"!=typeof(c=l.handleStore.get(h).value)&&"symbol"!=typeof c)return i.setLastError(4)}re(i,f,c,y,g,_,E,s,w)}return i.getReturnStatus()}catch(e){return i.tryCatch.setError(e),i.setLastError(10)}},napi_delete_element:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!r)return s.setLastError(1);var i=l.handleStore.get(r);if(!i.isObject()&&!i.isFunction())return s.setLastError(2);if(l.feature.supportReflect)o=Reflect.deleteProperty(i.value,t>>>0);else try{o=delete i.value[t>>>0]}catch(e){o=!1}if(a)new DataView(n.buffer).setInt8(a,o?1:0,!0);return s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_delete_property:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!r)return s.setLastError(1);var i=l.handleStore.get(r);if(!i.isObject()&&!i.isFunction())return s.setLastError(2);var u=l.handleStore.get(t).value;if(l.feature.supportReflect)o=Reflect.deleteProperty(i.value,u);else try{o=delete i.value[u]}catch(e){o=!1}if(a)new DataView(n.buffer).setInt8(a,o?1:0,!0);return s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_get_all_property_names:Ee,napi_get_element:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!a)return s.setLastError(1);if(!r)return s.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(e){return s.setLastError(2)}return o=s.ensureHandleId(u[t>>>0]),new DataView(n.buffer).setInt32(a,o,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_get_named_property:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!a)return s.setLastError(1);if(!r)return s.setLastError(1);if(!t)return s.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(e){return s.setLastError(2)}return o=s.ensureHandleId(u[q.UTF8ToString(t,-1)]),new DataView(n.buffer).setInt32(a,o,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_get_property:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!a)return s.setLastError(1);if(!r)return s.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(e){return s.setLastError(2)}return o=s.ensureHandleId(u[l.handleStore.get(t).value]),new DataView(n.buffer).setInt32(a,o,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_get_property_names:function(e,r,t){return Ee(e,r,0,18,1,t)},napi_has_element:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!a)return s.setLastError(1);if(!r)return s.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(e){return s.setLastError(2)}return o=t>>>0 in u?1:0,new DataView(n.buffer).setInt8(a,o,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_has_named_property:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!a)return s.setLastError(1);if(!r)return s.setLastError(1);if(!t)return s.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(e){return s.setLastError(2)}return o=q.UTF8ToString(t,-1)in u,new DataView(n.buffer).setInt8(a,o?1:0,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_has_own_property:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!a)return s.setLastError(1);if(!r)return s.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(e){return s.setLastError(2)}var f=l.handleStore.get(t).value;return"string"!=typeof f&&"symbol"!=typeof f?s.setLastError(4):(o=Object.prototype.hasOwnProperty.call(u,l.handleStore.get(t).value),new DataView(n.buffer).setInt8(a,o?1:0,!0),s.getReturnStatus())}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_has_property:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!t)return s.setLastError(1);if(!a)return s.setLastError(1);if(!r)return s.setLastError(1);var i=l.handleStore.get(r);if(null==i.value)throw new TypeError("Cannot convert undefined or null to object");var u=void 0;try{u=i.isObject()||i.isFunction()?i.value:Object(i.value)}catch(e){return s.setLastError(2)}return o=l.handleStore.get(t).value in u?1:0,new DataView(n.buffer).setInt8(a,o,!0),s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_object_freeze:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!r)return t.setLastError(1);var n=l.handleStore.get(r),a=n.value;return n.isObject()||n.isFunction()?(Object.freeze(a),t.getReturnStatus()):t.setLastError(2)}catch(e){return t.tryCatch.setError(e),t.setLastError(10)}},napi_object_seal:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!t.tryCatch.isEmpty())return t.setLastError(10);if(!t.canCallIntoJs())return t.setLastError(t.moduleApiVersion>=10?23:10);t.clearLastError();try{if(!r)return t.setLastError(1);var n=l.handleStore.get(r),a=n.value;return n.isObject()||n.isFunction()?(Object.seal(a),t.getReturnStatus()):t.setLastError(2)}catch(e){return t.tryCatch.setError(e),t.setLastError(10)}},napi_set_element:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!n)return a.setLastError(1);if(!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isObject()||o.isFunction()?(o.value[t>>>0]=l.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(2)}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_set_named_property:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!n)return a.setLastError(1);if(!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isObject()||o.isFunction()?t?(l.handleStore.get(r).value[q.UTF8ToString(t,-1)]=l.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(1):a.setLastError(2)}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_set_property:function(e,r,t,n){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!t)return a.setLastError(1);if(!n)return a.setLastError(1);if(!r)return a.setLastError(1);var o=l.handleStore.get(r);return o.isObject()||o.isFunction()?(o.value[l.handleStore.get(t).value]=l.handleStore.get(n).value,a.getReturnStatus()):a.setLastError(2)}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}}});var me=Object.freeze({__proto__:null,napi_run_script:function(e,r,t){var a,o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!r)return s.setLastError(1);if(!t)return s.setLastError(1);var i=l.handleStore.get(r);if(!i.isString())return s.setLastError(3);var u=l.handleStore.get(5).value.eval(i.value);o=s.ensureHandleId(u),new DataView(n.buffer).setInt32(t,o,!0),a=s.getReturnStatus()}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}return a}});var Le=Object.freeze({__proto__:null,napi_coerce_to_bool:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{return r&&t?(a=l.handleStore.get(r).value?4:3,new DataView(n.buffer).setInt32(t,a,!0),o.getReturnStatus()):o.setLastError(1)}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_coerce_to_number:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var s=l.handleStore.get(r);if(s.isBigInt())throw new TypeError("Cannot convert a BigInt value to a number");return a=l.addToCurrentScope(Number(s.value)).id,new DataView(n.buffer).setInt32(t,a,!0),o.getReturnStatus()}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_coerce_to_object:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var s=l.handleStore.get(r);if(null==s.value)throw new TypeError("Cannot convert undefined or null to object");return a=o.ensureHandleId(Object(s.value)),new DataView(n.buffer).setInt32(t,a,!0),o.getReturnStatus()}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_coerce_to_string:function(e,r,t){var a;if(!e)return 1;var o=l.envStore.get(e);if(o.checkGCAccess(),!o.tryCatch.isEmpty())return o.setLastError(10);if(!o.canCallIntoJs())return o.setLastError(o.moduleApiVersion>=10?23:10);o.clearLastError();try{if(!r)return o.setLastError(1);if(!t)return o.setLastError(1);var s=l.handleStore.get(r);if(s.isSymbol())throw new TypeError("Cannot convert a Symbol value to a string");return a=l.addToCurrentScope(String(s.value)).id,new DataView(n.buffer).setInt32(t,a,!0),o.getReturnStatus()}catch(e){return o.tryCatch.setError(e),o.setLastError(10)}},napi_detach_arraybuffer:function(e,r){if(!e)return 1;var t=l.envStore.get(e);if(t.checkGCAccess(),!r)return t.setLastError(1);var n=l.handleStore.get(r).value;if(!(n instanceof ArrayBuffer))return"function"==typeof SharedArrayBuffer&&n instanceof SharedArrayBuffer?t.setLastError(20):t.setLastError(19);try{(new(0,l.feature.MessageChannel)).port1.postMessage(n,[n])}catch(e){return t.setLastError(9)}return t.clearLastError()},napi_instanceof:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{if(!r)return s.setLastError(1);if(!a)return s.setLastError(1);if(!t)return s.setLastError(1);var i=new DataView(n.buffer);i.setInt8(a,0,!0);var u=l.handleStore.get(t);return u.isFunction()?(o=l.handleStore.get(r).value instanceof u.value?1:0,i.setInt8(a,o,!0),s.getReturnStatus()):s.setLastError(5)}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_is_array:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isArray()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_arraybuffer:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isArrayBuffer()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_buffer:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isBuffer(l.feature.Buffer)?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_dataview:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isDataView()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_date:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isDate()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_detached_arraybuffer:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!a.tryCatch.isEmpty())return a.setLastError(10);if(!a.canCallIntoJs())return a.setLastError(a.moduleApiVersion>=10?23:10);a.clearLastError();try{if(!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r),s=new DataView(n.buffer);if(o.isArrayBuffer()&&0===o.value.byteLength)try{new Uint8Array(o.value)}catch(e){return s.setInt8(t,1,!0),a.getReturnStatus()}return s.setInt8(t,0,!0),a.getReturnStatus()}catch(e){return a.tryCatch.setError(e),a.setLastError(10)}},napi_is_error:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).value instanceof Error?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_is_typedarray:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o=l.handleStore.get(r).isTypedArray()?1:0;return new DataView(n.buffer).setInt8(t,o,!0),a.clearLastError()},napi_strict_equals:function(e,r,t,a){var o;if(!e)return 1;var s=l.envStore.get(e);if(s.checkGCAccess(),!s.tryCatch.isEmpty())return s.setLastError(10);if(!s.canCallIntoJs())return s.setLastError(s.moduleApiVersion>=10?23:10);s.clearLastError();try{return r&&(t&&a)?(o=l.handleStore.get(r).value===l.handleStore.get(t).value?1:0,new DataView(n.buffer).setInt8(a,o,!0),s.getReturnStatus()):s.setLastError(1)}catch(e){return s.tryCatch.setError(e),s.setLastError(10)}},napi_typeof:function(e,r,t){if(!e)return 1;var a=l.envStore.get(e);if(a.checkGCAccess(),!r)return a.setLastError(1);if(!t)return a.setLastError(1);var o,s=l.handleStore.get(r);if(s.isNumber())o=3;else if(s.isBigInt())o=9;else if(s.isString())o=4;else if(s.isFunction())o=7;else if(s.isExternal())o=8;else if(s.isObject())o=6;else if(s.isBoolean())o=2;else if(s.isUndefined())o=0;else if(s.isSymbol())o=5;else{if(!s.isNull())return a.setLastError(1);o=1}return new DataView(n.buffer).setInt32(t,o,!0),a.clearLastError()}});var be=Object.freeze({__proto__:null,napi_get_version:function(e,r){if(!e)return 1;var t=l.envStore.get(e);return r?(new DataView(n.buffer).setUint32(r,10,!0),t.clearLastError()):t.setLastError(1)}});function Se(e){for(var r=Object.keys(e),t=0;t<r.length;++t){var n=r[t];0!==n.indexOf("$")&&(0===n.indexOf("emnapi_")?h.imports.emnapi[n]=e[n]:0===n.indexOf("_emnapi_")||"napi_set_last_error"===n||"napi_clear_last_error"===n?h.imports.env[n]=e[n]:h.imports.napi[n]=e[n])}}return M.init(),J.init(),q.init(),U.init(),S.init(),h.emnapi.syncMemory=ie,h.emnapi.getMemoryAddress=ue,Se(D),Se(x),Se(N),Se(T),Se($),Se(K),Se(le),Se(de),Se(oe),Se(pe),Se(fe),Se(ve),Se(he),Se(ye),Se(ge),Se(j),Se(_e),Se(we),Se(me),Se(Le),Se(be),h.imports.napi.napi_create_threadsafe_function=function(e,r,t,a,i,u,f,c,d,p,v){if(!e)return 1;var h=l.envStore.get(e);if(h.checkGCAccess(),!a)return h.setLastError(1);if(i>>>=0,0===(u>>>=0))return h.setLastError(1);if(!v)return h.setLastError(1);var y,g=0;if(r){if("function"!=typeof l.handleStore.get(r).value)return h.setLastError(1);g=l.createReference(h,r,1,1).id}else if(!p)return h.setLastError(1);if(t){if(null==(y=l.handleStore.get(t).value))return h.setLastError(2);y=Object(y)}else y={};var _=h.ensureHandleId(y),E=l.handleStore.get(a).value;if("symbol"==typeof E)return h.setLastError(3);E=String(E);var w=h.ensureHandleId(E),m=U.offset.end,L=o(m);if(!L)return h.setLastError(9);new Uint8Array(n.buffer).subarray(L,L+m).fill(0);var b=l.createReference(h,_,1,1),S=b.id,C=new DataView(n.buffer);return C.setInt32(L,S,!0),U.initQueue(L)?(W(_,w,-1,L+U.offset.async_id),C.setUint32(L+U.offset.thread_count,u,!0),C.setInt32(L+U.offset.context,d,!0),C.setUint32(L+U.offset.max_queue_size,i,!0),C.setInt32(L+U.offset.ref,g,!0),C.setInt32(L+U.offset.env,e,!0),C.setInt32(L+U.offset.finalize_data,f,!0),C.setInt32(L+U.offset.finalize_cb,c,!0),C.setInt32(L+U.offset.call_js_cb,p,!0),l.addCleanupHook(h,U.cleanup,L),h.ref(),l.increaseWaitingRequestCounter(),C.setInt32(L+U.offset.async_ref,1,!0),C.setInt32(v,L,!0),h.clearLastError()):(s(L),b.dispose(),h.setLastError(9))},h.imports.napi.napi_get_threadsafe_function_context=function(e,r){if(!e||!r)return c(),1;var t=U.getContext(e);return new DataView(n.buffer).setInt32(r,t,!0),0},h.imports.napi.napi_call_threadsafe_function=function(e,r,t){return e?U.push(e,r,t):(c(),1)},h.imports.napi.napi_acquire_threadsafe_function=function(e){return e?U.getMutex(e).execute(function(){return U.getIsClosing(e)?16:(U.addThreadCount(e),0)}):(c(),1)},h.imports.napi.napi_release_threadsafe_function=function(e,r){if(!e)return c(),1;var t=U.getMutex(e),n=U.getCond(e);return t.execute(function(){if(0===U.getThreadCount(e))return 1;if((U.subThreadCount(e),0===U.getThreadCount(e)||1===r)&&!U.getIsClosing(e)){var t=1===r?1:0;U.setIsClosing(e,t),t&&U.getMaxQueueSize(e)>0&&n.signal(),U.send(e)}return 0})},h.imports.napi.napi_unref_threadsafe_function=function(e,r){if(!r)return c(),1;var t=r+U.offset.async_ref>>2,a=new Int32Array(n.buffer);return Atomics.load(a,t)&&(Atomics.store(a,t,0),l.decreaseWaitingRequestCounter()),0},h.imports.napi.napi_ref_threadsafe_function=function(e,r){if(!r)return c(),1;var t=r+U.offset.async_ref>>2,a=new Int32Array(n.buffer);return Atomics.load(a,t)||(Atomics.store(a,t,1),l.increaseWaitingRequestCounter()),0},h}();return t}function m(e,r,t,n){var a,o=(n=null!=n?n:{}).getMemory,s=n.getTable,i=n.beforeInit;if(null!=o&&"function"!=typeof o)throw new TypeError("options.getMemory is not a function");if(null!=s&&"function"!=typeof s)throw new TypeError("options.getTable is not a function");if(null!=i&&"function"!=typeof i)throw new TypeError("options.beforeInit is not a function");var u="object"==typeof r&&null!==r;if(u){if(r.loaded)throw new Error("napiModule has already loaded");a=r}else a=w(n);var f,c=n.wasi,l={env:a.imports.env,napi:a.imports.napi,emnapi:a.imports.emnapi};c&&(f=new y(a.childThread?{wasi:c,childThread:!0,postMessage:a.postMessage}:{wasi:c,threadManager:a.PThread,waitThreadStart:a.waitThreadStart}),Object.assign(l,"function"==typeof c.getImportObject?c.getImportObject():{wasi_snapshot_preview1:c.wasiImport}),Object.assign(l,f.getImportObject()));var d=n.overwriteImports;if("function"==typeof d){var p=d(l);"object"==typeof p&&null!==p&&(l=p)}return e(t,l,function(r,t){if(r)throw r;var n=t.instance,d=n,p=n.exports,v="memory"in p,h="memory"in l.env,y=o?o(p):v?p.memory:h?l.env.memory:void 0;if(!y)throw new Error("memory is neither exported nor imported");var g=s?s(p):p.__indirect_function_table;if(c&&!v){var _=Object.create(null);Object.assign(_,p,{memory:y}),d={exports:_}}var E=t.module;c?d=f.initialize(d,E,y):a.PThread.setup(E,y);var w=function(){i&&i({instance:n,module:E}),a.init({instance:d,module:E,memory:y,table:g});var e={instance:n,module:E,usedInstance:d};return u||(e.napiModule=a),e};if(a.PThread.shouldPreloadWorkers()){var m=a.PThread.loadWasmModuleToAllWorkers();if(e===L)return m.then(w);throw new Error("Synchronous loading is not supported with worker pool (reuseWorker.size > 0)")}return w()})}function L(e,r,t){return n(e,r).then(function(e){return t(null,e)},function(e){return t(e)})}function b(e,n,a){var o;try{o=function(e,n){if(!e)throw new TypeError("Invalid wasm source");var a;if(t(n),n=null!=n?n:{},e instanceof ArrayBuffer||ArrayBuffer.isView(e))a=new r.Module(e);else{if(!(e instanceof WebAssembly.Module))throw new TypeError("Invalid wasm source");a=e}return{instance:new r.Instance(a,n),module:a}}(e,n)}catch(e){return a(e)}return a(null,o)}var S=function(e,r){return S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,r){e.__proto__=r}||function(e,r){for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])},S(e,r)};"function"==typeof SuppressedError&&SuppressedError;var C=function(e){function r(r){var t=this;if("function"!=typeof r.onLoad)throw new TypeError("options.onLoad is not a function");return(t=e.call(this,r)||this).napiModule=void 0,t}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function t(){this.constructor=e}S(e,r),e.prototype=null===r?Object.create(r):(t.prototype=r.prototype,new t)}(r,e),r.prototype.instantiate=function(e){var r=this,t=this.onLoad(e);return"function"==typeof t.then?t.then(function(e){return r.napiModule=e.napiModule,e}):(this.napiModule=t.napiModule,t)},r.prototype.handle=function(r){var t,n=this;if(e.prototype.handle.call(this,r),null===(t=null==r?void 0:r.data)||void 0===t?void 0:t.__emnapi__){var a=r.data.__emnapi__.type,o=r.data.__emnapi__.payload;"async-worker-init"===a?this.handleAfterLoad(r,function(){n.napiModule.initWorker(o.arg)}):"async-work-execute"===a&&this.handleAfterLoad(r,function(){n.napiModule.executeAsyncWork(o.work)})}},r}(_);e.MessageHandler=C,e.ThreadManager=d,e.ThreadMessageHandler=_,e.WASIThreads=y,e.createInstanceProxy=v,e.createNapiModule=w,e.instantiateNapiModule=function(e,r){return m(L,void 0,e,r)},e.instantiateNapiModuleSync=function(e,r){return m(b,void 0,e,r)},e.isSharedArrayBuffer=i,e.isTrapError=u,e.loadNapiModule=function(e,r,t){if("object"!=typeof e||null===e)throw new TypeError("Invalid napiModule");return m(L,e,r,t)},e.loadNapiModuleSync=function(e,r,t){if("object"!=typeof e||null===e)throw new TypeError("Invalid napiModule");return m(b,e,r,t)},e.version="1.4.5"});
