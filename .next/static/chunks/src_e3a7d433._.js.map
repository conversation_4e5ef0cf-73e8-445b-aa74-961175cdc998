{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Typography.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface TypographyProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport const Typography = {\n  Title: ({ children, className = '' }: TypographyProps) => (\n    <h1 className={`text-2xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </h1>\n  ),\n  \n  Subtitle: ({ children, className = '' }: TypographyProps) => (\n    <h2 className={`text-base text-gray-600 ${className}`}>\n      {children}\n    </h2>\n  ),\n  \n  Body: ({ children, className = '' }: TypographyProps) => (\n    <p className={`text-lg text-gray-800 leading-relaxed ${className}`}>\n      {children}\n    </p>\n  ),\n  \n  Caption: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-sm text-gray-500 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationWord: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationText: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-base text-gray-700 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  Micro: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xs text-gray-400 ${className}`}>\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,aAAa;IACxB,OAAO;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACnD,6LAAC;YAAG,WAAW,AAAC,oCAA6C,OAAV;sBAChD;;;;;;;IAIL,UAAU;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACtD,6LAAC;YAAG,WAAW,AAAC,2BAAoC,OAAV;sBACvC;;;;;;;IAIL,MAAM;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BAClD,6LAAC;YAAE,WAAW,AAAC,yCAAkD,OAAV;sBACpD;;;;;;;IAIL,SAAS;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACrD,6LAAC;YAAK,WAAW,AAAC,yBAAkC,OAAV;sBACvC;;;;;;;IAIL,iBAAiB;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BAC7D,6LAAC;YAAK,WAAW,AAAC,mCAA4C,OAAV;sBACjD;;;;;;;IAIL,iBAAiB;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BAC7D,6LAAC;YAAK,WAAW,AAAC,2BAAoC,OAAV;sBACzC;;;;;;;IAIL,OAAO;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACnD,6LAAC;YAAK,WAAW,AAAC,yBAAkC,OAAV;sBACvC;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/IconButton.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface IconButtonProps {\n  icon: React.ReactNode\n  onClick: () => void\n  variant?: 'default' | 'ghost' | 'outline'\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  disabled?: boolean\n  'aria-label'?: string\n}\n\nexport function IconButton({\n  icon,\n  onClick,\n  variant = 'default',\n  size = 'md',\n  className = '',\n  disabled = false,\n  'aria-label': ariaLabel,\n}: IconButtonProps) {\n  const baseClasses = 'inline-flex items-center justify-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'\n  \n  const variantClasses = {\n    default: 'bg-gray-100 hover:bg-gray-200 text-gray-700',\n    ghost: 'hover:bg-gray-100 text-gray-600',\n    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'\n  }\n  \n  const sizeClasses = {\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-10 h-10 text-base',\n    lg: 'w-12 h-12 text-lg'\n  }\n  \n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      aria-label={ariaLabel}\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}\n    >\n      {icon}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAYO,SAAS,WAAW,KAQT;QARS,EACzB,IAAI,EACJ,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,cAAc,SAAS,EACP,GARS;IASzB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,cAAY;QACZ,WAAW,AAAC,GAAiB,OAAf,aAAY,KAA8B,OAA3B,cAAc,CAAC,QAAQ,EAAC,KAAwB,OAArB,WAAW,CAAC,KAAK,EAAC,KAAa,OAAV;kBAE5E;;;;;;AAGP;KAjCgB", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Typography } from './Typography'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>\n}\n\nexport class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback\n      return <FallbackComponent error={this.state.error} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-[200px] p-6 bg-red-50 rounded-lg border border-red-200\">\n      <Typography.Title className=\"text-red-800 mb-2\">\n        Something went wrong\n      </Typography.Title>\n      <Typography.Body className=\"text-red-600 text-center mb-4\">\n        {error?.message || 'An unexpected error occurred'}\n      </Typography.Body>\n      <button\n        onClick={resetError}\n        className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n      >\n        Try again\n      </button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAeO,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAMhD,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;IACzD;IAMA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACjD,qBAAO,6LAAC;gBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;QAChF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAxBA,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC,QAYR,+KAAA,cAAa;YACX,IAAI,CAAC,QAAQ,CAAC;gBAAE,UAAU;gBAAO,OAAO;YAAU;QACpD;QAbE,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;AAsBF;AAEA,SAAS,qBAAqB,KAAgE;QAAhE,EAAE,KAAK,EAAE,UAAU,EAA6C,GAAhE;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gBAAC,WAAU;0BAAoB;;;;;;0BAGhD,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,WAAU;0BACxB,CAAA,kBAAA,4BAAA,MAAO,OAAO,KAAI;;;;;;0BAErB,6LAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP;KAjBS", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/AdminScreen.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { ErrorBoundary } from '../ui/ErrorBoundary'\nimport { StoryForm } from '../story/StoryForm'\nimport { PlusIcon, PlayIcon, TrashIcon } from '@radix-ui/react-icons'\nimport { Story } from '@/lib/types'\n\ninterface AdminScreenProps {\n  stories: Story[]\n  onCreateStory: () => void\n  onEditStory: (story: Story) => void\n  onDeleteStory: (storyId: string) => void\n  onPreviewStory: (story: Story) => void\n}\n\nexport function AdminScreen({ \n  stories, \n  onCreateStory, \n  onEditStory, \n  onDeleteStory, \n  onPreviewStory \n}: AdminScreenProps) {\n  const [selectedStory, setSelectedStory] = useState<Story | null>(null)\n\n  const handleStoryClick = useCallback((story: Story) => {\n    setSelectedStory(story)\n  }, [])\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'beginner': return 'bg-green-100 text-green-800'\n      case 'intermediate': return 'bg-yellow-100 text-yellow-800'\n      case 'advanced': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <ErrorBoundary>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <Typography.Title className=\"text-2xl\">\n                Story Management\n              </Typography.Title>\n              <Typography.Subtitle className=\"mt-1\">\n                Manage your language learning stories\n              </Typography.Subtitle>\n            </div>\n            <IconButton\n              icon={<PlusIcon className=\"w-5 h-5\" />}\n              onClick={onCreateStory}\n              className=\"bg-blue-600 text-white hover:bg-blue-700\"\n              aria-label=\"Create new story\"\n            />\n          </div>\n        </header>\n\n        <div className=\"flex h-[calc(100vh-80px)]\">\n          {/* Stories List */}\n          <div className=\"w-1/2 border-r border-gray-200 overflow-y-auto\">\n            <div className=\"p-6\">\n              <Typography.Body className=\"text-lg font-semibold mb-4\">\n                Stories ({stories.length})\n              </Typography.Body>\n              \n              {stories.length === 0 ? (\n                <div className=\"text-center py-12\">\n                  <Typography.Body className=\"text-gray-500 mb-4\">\n                    No stories yet\n                  </Typography.Body>\n                  <button\n                    onClick={onCreateStory}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    Create your first story\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {stories.map((story) => (\n                    <div\n                      key={story.id}\n                      className={`p-4 rounded-lg border cursor-pointer transition-colors ${\n                        selectedStory?.id === story.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 bg-white hover:border-gray-300'\n                      }`}\n                      onClick={() => handleStoryClick(story)}\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <Typography.Body className=\"font-semibold text-gray-900 mb-1\">\n                            {story.title}\n                          </Typography.Body>\n                          {story.chapter && (\n                            <Typography.Caption className=\"text-gray-600 mb-2\">\n                              {story.chapter}\n                            </Typography.Caption>\n                          )}\n                          <div className=\"flex items-center space-x-2\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(story.difficulty)}`}>\n                              {story.difficulty}\n                            </span>\n                            <Typography.Micro className=\"text-gray-500\">\n                              {story.language.toUpperCase()}\n                            </Typography.Micro>\n                            {story.audioUrl && (\n                              <Typography.Micro className=\"text-green-600\">\n                                ♪ Audio\n                              </Typography.Micro>\n                            )}\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-1 ml-4\">\n                          <IconButton\n                            icon={<PlayIcon className=\"w-4 h-4\" />}\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              onPreviewStory(story)\n                            }}\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            aria-label=\"Preview story\"\n                          />\n                          <IconButton\n                            icon={<TrashIcon className=\"w-4 h-4\" />}\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              onDeleteStory(story.id)\n                            }}\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"text-red-600 hover:bg-red-50\"\n                            aria-label=\"Delete story\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Story Details */}\n          <div className=\"w-1/2 overflow-y-auto\">\n            {selectedStory ? (\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <Typography.Title className=\"text-xl\">\n                    Story Details\n                  </Typography.Title>\n                  <button\n                    onClick={() => onEditStory(selectedStory)}\n                    className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n                  >\n                    Edit Story\n                  </button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Title\n                    </Typography.Caption>\n                    <Typography.Body>{selectedStory.title}</Typography.Body>\n                  </div>\n\n                  {selectedStory.chapter && (\n                    <div>\n                      <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                        Chapter\n                      </Typography.Caption>\n                      <Typography.Body>{selectedStory.chapter}</Typography.Body>\n                    </div>\n                  )}\n\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Language & Difficulty\n                    </Typography.Caption>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(selectedStory.difficulty)}`}>\n                        {selectedStory.difficulty}\n                      </span>\n                      <Typography.Caption>{selectedStory.language.toUpperCase()}</Typography.Caption>\n                    </div>\n                  </div>\n\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Content Preview\n                    </Typography.Caption>\n                    <div className=\"bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto\">\n                      <Typography.Body className=\"text-sm\">\n                        {selectedStory.content.substring(0, 300)}\n                        {selectedStory.content.length > 300 && '...'}\n                      </Typography.Body>\n                    </div>\n                  </div>\n\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Audio & Timing\n                    </Typography.Caption>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Typography.Micro>Audio:</Typography.Micro>\n                        <Typography.Micro className={selectedStory.audioUrl ? 'text-green-600' : 'text-red-600'}>\n                          {selectedStory.audioUrl ? '✓ Available' : '✗ Not generated'}\n                        </Typography.Micro>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Typography.Micro>Word Timings:</Typography.Micro>\n                        <Typography.Micro className={selectedStory.wordTimings?.length ? 'text-green-600' : 'text-red-600'}>\n                          {selectedStory.wordTimings?.length ? `✓ ${selectedStory.wordTimings.length} words` : '✗ Not available'}\n                        </Typography.Micro>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"flex items-center justify-center h-full\">\n                <Typography.Body className=\"text-gray-500\">\n                  Select a story to view details\n                </Typography.Body>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;;;AAPA;;;;;;AAkBO,SAAS,YAAY,KAMT;QANS,EAC1B,OAAO,EACP,aAAa,EACb,WAAW,EACX,aAAa,EACb,cAAc,EACG,GANS;QA0MyB,4BAC1B;;IApMzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjE,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACpC,iBAAiB;QACnB;oDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC,4IAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAO,WAAU;8BAChB,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wCAAC,WAAU;kDAAW;;;;;;kDAGvC,6LAAC,yIAAA,CAAA,aAAU,CAAC,QAAQ;wCAAC,WAAU;kDAAO;;;;;;;;;;;;0CAIxC,6LAAC,yIAAA,CAAA,aAAU;gCACT,oBAAM,6LAAC,mLAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAC1B,SAAS;gCACT,WAAU;gCACV,cAAW;;;;;;;;;;;;;;;;;8BAKjB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;wCAAC,WAAU;;4CAA6B;4CAC5C,QAAQ,MAAM;4CAAC;;;;;;;oCAG1B,QAAQ,MAAM,KAAK,kBAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gDAAC,WAAU;0DAAqB;;;;;;0DAGhD,6LAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;6DAKH,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,sBACZ,6LAAC;gDAEC,WAAW,AAAC,0DAIX,OAHC,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,MAAM,EAAE,GAC1B,+BACA;gDAEN,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;oEAAC,WAAU;8EACxB,MAAM,KAAK;;;;;;gEAEb,MAAM,OAAO,kBACZ,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;oEAAC,WAAU;8EAC3B,MAAM,OAAO;;;;;;8EAGlB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAW,AAAC,8CAAkF,OAArC,mBAAmB,MAAM,UAAU;sFAC/F,MAAM,UAAU;;;;;;sFAEnB,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;4EAAC,WAAU;sFACzB,MAAM,QAAQ,CAAC,WAAW;;;;;;wEAE5B,MAAM,QAAQ,kBACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;4EAAC,WAAU;sFAAiB;;;;;;;;;;;;;;;;;;sEAMnD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,yIAAA,CAAA,aAAU;oEACT,oBAAM,6LAAC,mLAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAC1B,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,eAAe;oEACjB;oEACA,SAAQ;oEACR,MAAK;oEACL,cAAW;;;;;;8EAEb,6LAAC,yIAAA,CAAA,aAAU;oEACT,oBAAM,6LAAC,mLAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAC3B,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,cAAc,MAAM,EAAE;oEACxB;oEACA,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,cAAW;;;;;;;;;;;;;;;;;;+CApDZ,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;sCAgEzB,6LAAC;4BAAI,WAAU;sCACZ,8BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DAAU;;;;;;0DAGtC,6LAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;kEAAE,cAAc,KAAK;;;;;;;;;;;;4CAGtC,cAAc,OAAO,kBACpB,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;kEAAE,cAAc,OAAO;;;;;;;;;;;;0DAI3C,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAW,AAAC,8CAA0F,OAA7C,mBAAmB,cAAc,UAAU;0EACvG,cAAc,UAAU;;;;;;0EAE3B,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;0EAAE,cAAc,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;0DAI3D,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;4DAAC,WAAU;;gEACxB,cAAc,OAAO,CAAC,SAAS,CAAC,GAAG;gEACnC,cAAc,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;;;;;;;;;;;;0DAK7C,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;kFAAC;;;;;;kFAClB,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wEAAC,WAAW,cAAc,QAAQ,GAAG,mBAAmB;kFACtE,cAAc,QAAQ,GAAG,gBAAgB;;;;;;;;;;;;0EAG9C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;kFAAC;;;;;;kFAClB,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wEAAC,WAAW,EAAA,6BAAA,cAAc,WAAW,cAAzB,iDAAA,2BAA2B,MAAM,IAAG,mBAAmB;kFACjF,EAAA,8BAAA,cAAc,WAAW,cAAzB,kDAAA,4BAA2B,MAAM,IAAG,AAAC,KAAqC,OAAjC,cAAc,WAAW,CAAC,MAAM,EAAC,YAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAQjG,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;oCAAC,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D;GA9NgB;KAAA", "debugId": null}}, {"offset": {"line": 815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/StoryForm.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { Cross2Icon, PlayIcon } from '@radix-ui/react-icons'\nimport { Story } from '@/lib/types'\n\ninterface StoryFormData {\n  title: string\n  chapter?: string\n  content: string\n  language: string\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  voice?: string\n  speed: number\n  generateAudio: boolean\n}\n\ninterface StoryFormProps {\n  story?: Story\n  isOpen: boolean\n  onClose: () => void\n  onSave: (data: StoryFormData) => Promise<void>\n  onPreview?: (data: StoryFormData) => Promise<void>\n}\n\nconst LANGUAGES = [\n  { code: 'en', name: 'English' },\n  { code: 'fr', name: 'French' },\n  { code: 'es', name: 'Spanish' },\n  { code: 'de', name: 'German' },\n  { code: 'it', name: 'Italian' },\n  { code: 'pt', name: 'Portuguese' }\n]\n\nconst DIFFICULTIES = [\n  { value: 'beginner', label: 'Beginner', description: 'Simple vocabulary and grammar' },\n  { value: 'intermediate', label: 'Intermediate', description: 'Moderate complexity' },\n  { value: 'advanced', label: 'Advanced', description: 'Complex vocabulary and structures' }\n] as const\n\nexport function StoryForm({ story, isOpen, onClose, onSave, onPreview }: StoryFormProps) {\n  const [formData, setFormData] = useState<StoryFormData>({\n    title: story?.title || '',\n    chapter: story?.chapter || '',\n    content: story?.content || '',\n    language: story?.language || 'fr',\n    difficulty: story?.difficulty || 'beginner',\n    voice: '',\n    speed: 1.0,\n    generateAudio: true\n  })\n\n  const [isLoading, setIsLoading] = useState(false)\n  const [isPreviewing, setIsPreviewing] = useState(false)\n\n  const handleInputChange = useCallback((field: keyof StoryFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }, [])\n\n  const handleSave = useCallback(async () => {\n    if (!formData.title.trim() || !formData.content.trim()) {\n      alert('Please fill in title and content')\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      await onSave(formData)\n      onClose()\n    } catch (error) {\n      console.error('Save failed:', error)\n      alert('Failed to save story')\n    } finally {\n      setIsLoading(false)\n    }\n  }, [formData, onSave, onClose])\n\n  const handlePreview = useCallback(async () => {\n    if (!formData.title.trim() || !formData.content.trim()) {\n      alert('Please fill in title and content')\n      return\n    }\n\n    if (!onPreview) return\n\n    setIsPreviewing(true)\n    try {\n      await onPreview(formData)\n    } catch (error) {\n      console.error('Preview failed:', error)\n      alert('Failed to preview story')\n    } finally {\n      setIsPreviewing(false)\n    }\n  }, [formData, onPreview])\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <Typography.Title className=\"text-xl\">\n            {story ? 'Edit Story' : 'Create New Story'}\n          </Typography.Title>\n          <IconButton\n            icon={<Cross2Icon className=\"w-5 h-5\" />}\n            onClick={onClose}\n            variant=\"ghost\"\n            aria-label=\"Close form\"\n          />\n        </div>\n\n        {/* Form */}\n        <div className=\"p-6 space-y-6\">\n          {/* Title */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              value={formData.title}\n              onChange={(e) => handleInputChange('title', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Enter story title\"\n            />\n          </div>\n\n          {/* Chapter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Chapter (optional)\n            </label>\n            <input\n              type=\"text\"\n              value={formData.chapter}\n              onChange={(e) => handleInputChange('chapter', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Chapter 1, Part A\"\n            />\n          </div>\n\n          {/* Language and Difficulty */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Language *\n              </label>\n              <select\n                value={formData.language}\n                onChange={(e) => handleInputChange('language', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {LANGUAGES.map(lang => (\n                  <option key={lang.code} value={lang.code}>\n                    {lang.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Difficulty *\n              </label>\n              <select\n                value={formData.difficulty}\n                onChange={(e) => handleInputChange('difficulty', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {DIFFICULTIES.map(diff => (\n                  <option key={diff.value} value={diff.value}>\n                    {diff.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Story Content *\n            </label>\n            <textarea\n              value={formData.content}\n              onChange={(e) => handleInputChange('content', e.target.value)}\n              rows={8}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n              placeholder=\"Enter the story content...\"\n            />\n            <Typography.Micro className=\"text-gray-500 mt-1\">\n              {formData.content.length} characters\n            </Typography.Micro>\n          </div>\n\n          {/* Audio Settings */}\n          <div className=\"border-t border-gray-200 pt-6\">\n            <Typography.Body className=\"font-semibold mb-4\">\n              Audio Settings\n            </Typography.Body>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"generateAudio\"\n                  checked={formData.generateAudio}\n                  onChange={(e) => handleInputChange('generateAudio', e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"generateAudio\" className=\"ml-2 text-sm text-gray-700\">\n                  Generate audio automatically\n                </label>\n              </div>\n\n              {formData.generateAudio && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Speech Speed\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0.5\"\n                    max=\"2.0\"\n                    step=\"0.1\"\n                    value={formData.speed}\n                    onChange={(e) => handleInputChange('speed', parseFloat(e.target.value))}\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                    <span>Slow (0.5x)</span>\n                    <span>Normal ({formData.speed}x)</span>\n                    <span>Fast (2.0x)</span>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors\"\n          >\n            Cancel\n          </button>\n          \n          <div className=\"flex items-center space-x-3\">\n            {onPreview && (\n              <button\n                onClick={handlePreview}\n                disabled={isPreviewing}\n                className=\"px-4 py-2 text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n              >\n                <PlayIcon className=\"w-4 h-4\" />\n                <span>{isPreviewing ? 'Processing...' : 'Preview'}</span>\n              </button>\n            )}\n            \n            <button\n              onClick={handleSave}\n              disabled={isLoading}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50\"\n            >\n              {isLoading ? 'Saving...' : 'Save Story'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AA2BA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;IAAU;IAC9B;QAAE,MAAM;QAAM,MAAM;IAAS;IAC7B;QAAE,MAAM;QAAM,MAAM;IAAU;IAC9B;QAAE,MAAM;QAAM,MAAM;IAAS;IAC7B;QAAE,MAAM;QAAM,MAAM;IAAU;IAC9B;QAAE,MAAM;QAAM,MAAM;IAAa;CAClC;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAgC;IACrF;QAAE,OAAO;QAAgB,OAAO;QAAgB,aAAa;IAAsB;IACnF;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAoC;CAC1F;AAEM,SAAS,UAAU,KAA6D;QAA7D,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAkB,GAA7D;;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,OAAO,CAAA,kBAAA,4BAAA,MAAO,KAAK,KAAI;QACvB,SAAS,CAAA,kBAAA,4BAAA,MAAO,OAAO,KAAI;QAC3B,SAAS,CAAA,kBAAA,4BAAA,MAAO,OAAO,KAAI;QAC3B,UAAU,CAAA,kBAAA,4BAAA,MAAO,QAAQ,KAAI;QAC7B,YAAY,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;QACjC,OAAO;QACP,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC,OAA4B;YACjE;4DAAY,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,CAAC;;QAClD;mDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YAC7B,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;gBACtD,MAAM;gBACN;YACF;YAEA,aAAa;YACb,IAAI;gBACF,MAAM,OAAO;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,MAAM;YACR,SAAU;gBACR,aAAa;YACf;QACF;4CAAG;QAAC;QAAU;QAAQ;KAAQ;IAE9B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAChC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;gBACtD,MAAM;gBACN;YACF;YAEA,IAAI,CAAC,WAAW;YAEhB,gBAAgB;YAChB,IAAI;gBACF,MAAM,UAAU;YAClB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mBAAmB;gBACjC,MAAM;YACR,SAAU;gBACR,gBAAgB;YAClB;QACF;+CAAG;QAAC;QAAU;KAAU;IAExB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;4BAAC,WAAU;sCACzB,QAAQ,eAAe;;;;;;sCAE1B,6LAAC,yIAAA,CAAA,aAAU;4BACT,oBAAM,6LAAC,mLAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAC5B,SAAS;4BACT,SAAQ;4BACR,cAAW;;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,WAAU;sDAET,UAAU,GAAG,CAAC,CAAA,qBACb,6LAAC;oDAAuB,OAAO,KAAK,IAAI;8DACrC,KAAK,IAAI;mDADC,KAAK,IAAI;;;;;;;;;;;;;;;;8CAO5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAU;sDAET,aAAa,GAAG,CAAC,CAAA,qBAChB,6LAAC;oDAAwB,OAAO,KAAK,KAAK;8DACvC,KAAK,KAAK;mDADA,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAS/B,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;8CAEd,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;oCAAC,WAAU;;wCACzB,SAAS,OAAO,CAAC,MAAM;wCAAC;;;;;;;;;;;;;sCAK7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;oCAAC,WAAU;8CAAqB;;;;;;8CAIhD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,SAAS,SAAS,aAAa;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,OAAO;oDACpE,WAAU;;;;;;8DAEZ,6LAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAA6B;;;;;;;;;;;;wCAKvE,SAAS,aAAa,kBACrB,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK;oDACrE,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAK;gEAAS,SAAS,KAAK;gEAAC;;;;;;;sEAC9B,6LAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,6LAAC;4BAAI,WAAU;;gCACZ,2BACC,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC,mLAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAM,eAAe,kBAAkB;;;;;;;;;;;;8CAI5C,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;GA5OgB;KAAA", "debugId": null}}, {"offset": {"line": 1361, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/StoryHeader.tsx"], "sourcesContent": ["import React from 'react'\nimport { BookmarkIcon, HamburgerMenuIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\n\ninterface StoryHeaderProps {\n  title: string\n  chapter?: string\n  isAudioPlaying?: boolean\n  onBookmarkClick: () => void\n  onMenuClick: () => void\n}\n\nexport function StoryHeader({ title, chapter, isAudioPlaying, onBookmarkClick, onMenuClick }: StoryHeaderProps) {\n  return (\n    <header className=\"flex items-center justify-between p-4 bg-white border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-2\">\n          <Typography.Title className=\"text-xl font-bold text-gray-900\">\n            {title}\n          </Typography.Title>\n          {isAudioPlaying && (\n            <div className=\"flex space-x-1\">\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\"></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n            </div>\n          )}\n        </div>\n        {chapter && (\n          <Typography.Subtitle className=\"text-sm text-gray-600 mt-1\">\n            {chapter}\n          </Typography.Subtitle>\n        )}\n      </div>\n      \n      <div className=\"flex items-center space-x-2\">\n        <IconButton\n          icon={<BookmarkIcon className=\"w-5 h-5\" />}\n          onClick={onBookmarkClick}\n          variant=\"ghost\"\n          aria-label=\"Bookmark story\"\n        />\n        <IconButton\n          icon={<HamburgerMenuIcon className=\"w-5 h-5\" />}\n          onClick={onMenuClick}\n          variant=\"ghost\"\n          aria-label=\"Open menu\"\n        />\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAUO,SAAS,YAAY,KAAkF;QAAlF,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAoB,GAAlF;IAC1B,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB;;;;;;4BAEF,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC3F,6LAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;oBAIhG,yBACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,QAAQ;wBAAC,WAAU;kCAC5B;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,aAAU;wBACT,oBAAM,6LAAC,mLAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;kCAEb,6LAAC,yIAAA,CAAA,aAAU;wBACT,oBAAM,6LAAC,mLAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;wBACnC,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;;;;;;;;;;;;;AAKrB;KAvCgB", "debugId": null}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/WordHighlighter.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface WordHighlighterProps {\n  text: string\n  currentWordIndex?: number\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n}\n\nexport function WordHighlighter({ text, currentWordIndex, onWordClick }: WordHighlighterProps) {\n  const words = text.split(/(\\s+)/)\n  let wordIndex = 0\n\n  return (\n    <div className=\"text-lg leading-relaxed text-gray-800 select-none\">\n      {words.map((segment, segmentIndex) => {\n        // Skip whitespace segments\n        if (/^\\s+$/.test(segment)) {\n          return <span key={segmentIndex}>{segment}</span>\n        }\n\n        const isCurrentWord = wordIndex === currentWordIndex\n        const currentIndex = wordIndex\n        wordIndex++\n\n        return (\n          <span\n            key={segmentIndex}\n            className={`cursor-pointer transition-all duration-300 hover:bg-blue-100 px-1 py-0.5 rounded ${\n              isCurrentWord\n                ? 'bg-blue-300 text-blue-900 font-semibold shadow-sm scale-105'\n                : 'hover:scale-102'\n            }`}\n            onClick={(event) => onWordClick(segment, currentIndex, event)}\n          >\n            {segment}\n          </span>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,gBAAgB,KAA6D;QAA7D,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAwB,GAA7D;IAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,YAAY;IAEhB,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,SAAS;YACnB,2BAA2B;YAC3B,IAAI,QAAQ,IAAI,CAAC,UAAU;gBACzB,qBAAO,6LAAC;8BAAyB;mBAAf;;;;;YACpB;YAEA,MAAM,gBAAgB,cAAc;YACpC,MAAM,eAAe;YACrB;YAEA,qBACE,6LAAC;gBAEC,WAAW,AAAC,oFAIX,OAHC,gBACI,gEACA;gBAEN,SAAS,CAAC,QAAU,YAAY,SAAS,cAAc;0BAEtD;eARI;;;;;QAWX;;;;;;AAGN;KAhCgB", "debugId": null}}, {"offset": {"line": 1560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/TranslationCard.tsx"], "sourcesContent": ["import React from 'react'\nimport { Cross2Icon, SpeakerLoudIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { TranslationResult } from '@/lib/types'\n\ninterface TranslationCardProps {\n  word: string\n  translation?: TranslationResult\n  isLoading?: boolean\n  onPlayAudio: () => void\n  onClose: () => void\n}\n\nexport function TranslationCard({\n  word,\n  translation,\n  isLoading = false,\n  onPlayAudio,\n  onClose\n}: TranslationCardProps) {\n  return (\n    <div className=\"bg-gray-50 rounded-xl p-4 mx-5 mb-6 relative shadow-sm border border-gray-200\">\n      <IconButton\n        icon={<Cross2Icon className=\"w-4 h-4\" />}\n        onClick={onClose}\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"absolute top-2 right-2\"\n        aria-label=\"Close translation\"\n      />\n      \n      <div className=\"flex items-center justify-between mb-3\">\n        <div>\n          <Typography.TranslationWord className=\"text-xl\">\n            {word}\n          </Typography.TranslationWord>\n          {translation?.pronunciation && (\n            <Typography.Caption className=\"block mt-1\">\n              /{translation.pronunciation}/\n            </Typography.Caption>\n          )}\n        </div>\n        <IconButton \n          icon={<SpeakerLoudIcon className=\"w-4 h-4\" />} \n          onClick={onPlayAudio}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Play pronunciation\"\n        />\n      </div>\n      \n      {isLoading ? (\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n          <Typography.Caption className=\"text-gray-400 mt-2\">\n            Translating...\n          </Typography.Caption>\n        </div>\n      ) : translation ? (\n        <>\n          <Typography.TranslationText className=\"block mb-3\">\n            {translation.translation}\n          </Typography.TranslationText>\n          \n          {translation.example && (\n            <div className=\"text-sm text-gray-600 border-t border-gray-200 pt-3\">\n              <Typography.Caption className=\"italic block mb-1\">\n                {translation.example.original}\n              </Typography.Caption>\n              <Typography.Caption className=\"block\">\n                {translation.example.translation}\n              </Typography.Caption>\n            </div>\n          )}\n        </>\n      ) : (\n        <Typography.Caption className=\"text-gray-500\">\n          Translation not available\n        </Typography.Caption>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAWO,SAAS,gBAAgB,KAMT;QANS,EAC9B,IAAI,EACJ,WAAW,EACX,YAAY,KAAK,EACjB,WAAW,EACX,OAAO,EACc,GANS;IAO9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,aAAU;gBACT,oBAAM,6LAAC,mLAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAC5B,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,cAAW;;;;;;0BAGb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,eAAe;gCAAC,WAAU;0CACnC;;;;;;4BAEF,CAAA,wBAAA,kCAAA,YAAa,aAAa,mBACzB,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;;oCAAa;oCACvC,YAAY,aAAa;oCAAC;;;;;;;;;;;;;kCAIlC,6LAAC,yIAAA,CAAA,aAAU;wBACT,oBAAM,6LAAC,mLAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;wBACjC,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;YAId,0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wBAAC,WAAU;kCAAqB;;;;;;;;;;;uBAInD,4BACF;;kCACE,6LAAC,yIAAA,CAAA,aAAU,CAAC,eAAe;wBAAC,WAAU;kCACnC,YAAY,WAAW;;;;;;oBAGzB,YAAY,OAAO,kBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,QAAQ;;;;;;0CAE/B,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,WAAW;;;;;;;;;;;;;6CAMxC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;0BAAgB;;;;;;;;;;;;AAMtD;KAvEgB", "debugId": null}}, {"offset": {"line": 1748, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/ReadingArea.tsx"], "sourcesContent": ["import React from 'react'\nimport { WordHighlighter } from './WordHighlighter'\nimport { TranslationCard } from './TranslationCard'\nimport { TranslationCardState } from '@/lib/types'\n\ninterface ReadingAreaProps {\n  content: string\n  currentWordIndex?: number\n  translationCard: TranslationCardState\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n  onTranslationClose: () => void\n  onTranslationPlayAudio: () => void\n}\n\nexport function ReadingArea({\n  content,\n  currentWordIndex,\n  translationCard,\n  onWordClick,\n  onTranslationClose,\n  onTranslationPlayAudio\n}: ReadingAreaProps) {\n  return (\n    <div className=\"flex-1 overflow-y-auto\">\n      <div className=\"max-w-sm mx-auto p-6\">\n        <div className=\"mb-6\">\n          <WordHighlighter\n            text={content}\n            currentWordIndex={currentWordIndex}\n            onWordClick={onWordClick}\n          />\n        </div>\n        \n        {translationCard.isVisible && (\n          <TranslationCard\n            word={translationCard.word}\n            translation={translationCard.translation}\n            isLoading={translationCard.isLoading}\n            onPlayAudio={onTranslationPlayAudio}\n            onClose={onTranslationClose}\n          />\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAYO,SAAS,YAAY,KAOT;QAPS,EAC1B,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,sBAAsB,EACL,GAPS;IAQ1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iJAAA,CAAA,kBAAe;wBACd,MAAM;wBACN,kBAAkB;wBAClB,aAAa;;;;;;;;;;;gBAIhB,gBAAgB,SAAS,kBACxB,6LAAC,iJAAA,CAAA,kBAAe;oBACd,MAAM,gBAAgB,IAAI;oBAC1B,aAAa,gBAAgB,WAAW;oBACxC,WAAW,gBAAgB,SAAS;oBACpC,aAAa;oBACb,SAAS;;;;;;;;;;;;;;;;;AAMrB;KA/BgB", "debugId": null}}, {"offset": {"line": 1815, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Slider.tsx"], "sourcesContent": ["import React from 'react'\nimport * as RadixSlider from '@radix-ui/react-slider'\n\ninterface SliderProps {\n  value: number[]\n  onValueChange: (value: number[]) => void\n  max?: number\n  min?: number\n  step?: number\n  className?: string\n  disabled?: boolean\n}\n\nexport function Slider({\n  value,\n  onValueChange,\n  max = 100,\n  min = 0,\n  step = 1,\n  className = '',\n  disabled = false,\n}: SliderProps) {\n  return (\n    <RadixSlider.Root\n      className={`relative flex items-center select-none touch-none w-full h-5 ${className}`}\n      value={value}\n      onValueChange={onValueChange}\n      max={max}\n      min={min}\n      step={step}\n      disabled={disabled}\n    >\n      <RadixSlider.Track className=\"bg-gray-200 relative grow rounded-full h-1\">\n        <RadixSlider.Range className=\"absolute bg-gray-900 rounded-full h-full\" />\n      </RadixSlider.Track>\n      <RadixSlider.Thumb\n        className=\"block w-4 h-4 bg-white border-2 border-gray-900 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n        aria-label=\"Volume\"\n      />\n    </RadixSlider.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAYO,SAAS,OAAO,KAQT;QARS,EACrB,KAAK,EACL,aAAa,EACb,MAAM,GAAG,EACT,MAAM,CAAC,EACP,OAAO,CAAC,EACR,YAAY,EAAE,EACd,WAAW,KAAK,EACJ,GARS;IASrB,qBACE,6LAAC,qKAAA,CAAA,OAAgB;QACf,WAAW,AAAC,gEAAyE,OAAV;QAC3E,OAAO;QACP,eAAe;QACf,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;;0BAEV,6LAAC,qKAAA,CAAA,QAAiB;gBAAC,WAAU;0BAC3B,cAAA,6LAAC,qKAAA,CAAA,QAAiB;oBAAC,WAAU;;;;;;;;;;;0BAE/B,6LAAC,qKAAA,CAAA,QAAiB;gBAChB,WAAU;gBACV,cAAW;;;;;;;;;;;;AAInB;KA5BgB", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/AudioPlayer.tsx"], "sourcesContent": ["import React from 'react'\nimport { PlayIcon, PauseIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Slider } from '../ui/Slider'\nimport { Typography } from '../ui/Typography'\nimport { AudioPlayerState } from '@/lib/types'\n\ninterface AudioPlayerProps {\n  state: AudioPlayerState\n  onPlayPause: () => void\n  onSeek: (time: number) => void\n  onVolumeChange: (volume: number) => void\n}\n\nfunction formatTime(seconds: number): string {\n  const minutes = Math.floor(seconds / 60)\n  const remainingSeconds = Math.floor(seconds % 60)\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n}\n\nexport function AudioPlayer({ state, onPlayPause, onSeek, onVolumeChange }: AudioPlayerProps) {\n  const { isPlaying, currentTime, duration, volume } = state\n\n  const handleProgressChange = (value: number[]) => {\n    onSeek(value[0])\n  }\n\n  const handleVolumeChange = (value: number[]) => {\n    onVolumeChange(value[0] / 100)\n  }\n\n  return (\n    <div className=\"bg-white border-t border-gray-200 p-4\">\n      {/* Progress Bar */}\n      <div className=\"mb-4\">\n        <Slider\n          value={[currentTime]}\n          onValueChange={handleProgressChange}\n          max={duration}\n          min={0}\n          step={0.1}\n          className=\"w-full\"\n        />\n        <div className=\"flex justify-between mt-2\">\n          <Typography.Micro>\n            {formatTime(currentTime)}\n          </Typography.Micro>\n          <Typography.Micro>\n            {formatTime(duration)}\n          </Typography.Micro>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex items-center justify-center\">\n        <IconButton\n          icon={isPlaying ? <PauseIcon className=\"w-6 h-6\" /> : <PlayIcon className=\"w-6 h-6\" />}\n          onClick={onPlayPause}\n          size=\"lg\"\n          className=\"bg-gray-900 text-white hover:bg-gray-800\"\n          aria-label={isPlaying ? 'Pause' : 'Play'}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAUA,SAAS,WAAW,OAAe;IACjC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;IAC9C,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAgD,OAA7C,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG;AAC/D;AAEO,SAAS,YAAY,KAAgE;QAAhE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAoB,GAAhE;IAC1B,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAErD,MAAM,uBAAuB,CAAC;QAC5B,OAAO,KAAK,CAAC,EAAE;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO;4BAAC;yBAAY;wBACpB,eAAe;wBACf,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;0CAEd,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;oBACT,MAAM,0BAAY,6LAAC,mLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;+CAAe,6LAAC,mLAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1E,SAAS;oBACT,MAAK;oBACL,WAAU;oBACV,cAAY,YAAY,UAAU;;;;;;;;;;;;;;;;;AAK5C;KA7CgB", "debugId": null}}, {"offset": {"line": 1999, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/PageNavigation.tsx"], "sourcesContent": ["import React from 'react'\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Typography } from '../ui/Typography'\n\ninterface PageNavigationProps {\n  currentPage: number\n  totalPages: number\n  onPreviousPage: () => void\n  onNextPage: () => void\n}\n\nexport function PageNavigation({ \n  currentPage, \n  totalPages, \n  onPreviousPage, \n  onNextPage \n}: PageNavigationProps) {\n  return (\n    <div className=\"flex items-center justify-between p-4 bg-white border-t border-gray-100\">\n      <IconButton\n        icon={<ChevronLeftIcon className=\"w-5 h-5\" />}\n        onClick={onPreviousPage}\n        variant=\"ghost\"\n        disabled={currentPage <= 1}\n        aria-label=\"Previous page\"\n      />\n      \n      <Typography.Caption className=\"text-gray-500\">\n        Page {currentPage} of {totalPages}\n      </Typography.Caption>\n      \n      <IconButton\n        icon={<ChevronRightIcon className=\"w-5 h-5\" />}\n        onClick={onNextPage}\n        variant=\"ghost\"\n        disabled={currentPage >= totalPages}\n        aria-label=\"Next page\"\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,eAAe,KAKT;QALS,EAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACU,GALS;IAM7B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,aAAU;gBACT,oBAAM,6LAAC,mLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;gBACjC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;0BAGb,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;;oBAAgB;oBACtC;oBAAY;oBAAK;;;;;;;0BAGzB,6LAAC,yIAAA,CAAA,aAAU;gBACT,oBAAM,6LAAC,mLAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAClC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;;;;;;;AAInB;KA7BgB", "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/KeyboardShortcutsHelp.tsx"], "sourcesContent": ["import React, { useState } from 'react'\nimport { QuestionMarkCircledIcon, Cross2Icon, KeyboardIcon } from '@radix-ui/react-icons'\nimport { IconButton } from './IconButton'\nimport { Typography } from './Typography'\n\nexport function KeyboardShortcutsHelp() {\n  const [isVisible, setIsVisible] = useState(false)\n\n  const shortcuts = [\n    { key: 'Space', description: 'Play/Pause audio' },\n    { key: 'Shift + →', description: 'Skip forward 10s' },\n    { key: 'Shift + ←', description: 'Skip backward 10s' },\n    { key: 'Esc', description: 'Close translation' },\n  ]\n\n  const handleClose = () => setIsVisible(false)\n\n  if (!isVisible) {\n    return (\n      <button\n        onClick={() => setIsVisible(true)}\n        className=\"bg-white border border-gray-200 rounded-lg p-2 shadow-sm hover:shadow-md transition-all duration-200 hover:bg-gray-50\"\n        aria-label=\"Show keyboard shortcuts\"\n      >\n        <KeyboardIcon className=\"w-4 h-4 text-gray-600\" />\n      </button>\n    )\n  }\n\n  return (\n    <>\n      {/* Overlay */}\n      <div\n        className=\"fixed inset-0 z-40\"\n        onClick={handleClose}\n      />\n\n      {/* Help Panel */}\n      <div className=\"relative z-50 bg-white rounded-lg shadow-lg border border-gray-200 max-w-xs\">\n        <div className=\"flex items-center justify-between p-3 border-b border-gray-100\">\n          <div className=\"flex items-center space-x-2\">\n            <KeyboardIcon className=\"w-4 h-4 text-gray-600\" />\n            <Typography.Caption className=\"font-semibold text-gray-700\">\n              Shortcuts\n            </Typography.Caption>\n          </div>\n          <IconButton\n            icon={<Cross2Icon className=\"w-3 h-3\" />}\n            onClick={handleClose}\n            variant=\"ghost\"\n            size=\"sm\"\n            aria-label=\"Close shortcuts help\"\n          />\n        </div>\n\n      <div className=\"p-3 space-y-2\">\n        {shortcuts.map((shortcut, index) => (\n          <div key={index} className=\"flex items-center justify-between\">\n            <Typography.Micro className=\"font-mono bg-gray-100 px-2 py-1 rounded text-gray-700 min-w-[4rem] text-center\">\n              {shortcut.key}\n            </Typography.Micro>\n            <Typography.Micro className=\"text-gray-600 ml-3 flex-1\">\n              {shortcut.description}\n            </Typography.Micro>\n          </div>\n        ))}\n      </div>\n    </div>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAEO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY;QAChB;YAAE,KAAK;YAAS,aAAa;QAAmB;QAChD;YAAE,KAAK;YAAa,aAAa;QAAmB;QACpD;YAAE,KAAK;YAAa,aAAa;QAAoB;QACrD;YAAE,KAAK;YAAO,aAAa;QAAoB;KAChD;IAED,MAAM,cAAc,IAAM,aAAa;IAEvC,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YACC,SAAS,IAAM,aAAa;YAC5B,WAAU;YACV,cAAW;sBAEX,cAAA,6LAAC,mLAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;IAG9B;IAEA,qBACE;;0BAEE,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mLAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wCAAC,WAAU;kDAA8B;;;;;;;;;;;;0CAI9D,6LAAC,yIAAA,CAAA,aAAU;gCACT,oBAAM,6LAAC,mLAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAC5B,SAAS;gCACT,SAAQ;gCACR,MAAK;gCACL,cAAW;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC;gCAAgB,WAAU;;kDACzB,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wCAAC,WAAU;kDACzB,SAAS,GAAG;;;;;;kDAEf,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wCAAC,WAAU;kDACzB,SAAS,WAAW;;;;;;;+BALf;;;;;;;;;;;;;;;;;;AAapB;GAjEgB;KAAA", "debugId": null}}, {"offset": {"line": 2252, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/ProgressDisplay.tsx"], "sourcesContent": ["import React, { useState } from 'react'\nimport { ChevronUpIcon, ChevronDownIcon, BarChartIcon } from '@radix-ui/react-icons'\nimport { Typography } from './Typography'\nimport { IconButton } from './IconButton'\nimport { UserProgress, ProgressStats } from '@/lib/services/progress-service'\n\ninterface ProgressDisplayProps {\n  storyProgress?: UserProgress | null\n  overallStats?: ProgressStats | null\n  className?: string\n}\n\nexport function ProgressDisplay({ storyProgress, overallStats, className = '' }: ProgressDisplayProps) {\n  const [isExpanded, setIsExpanded] = useState(false)\n\n  const formatTime = (seconds: number): string => {\n    const hours = Math.floor(seconds / 3600)\n    const minutes = Math.floor((seconds % 3600) / 60)\n\n    if (hours > 0) {\n      return `${hours}h ${minutes}m`\n    }\n    return `${minutes}m`\n  }\n\n  const formatPercentage = (percentage: number): string => {\n    return `${Math.round(percentage)}%`\n  }\n\n  // Collapsed view - just show essential info\n  if (!isExpanded) {\n    return (\n      <div className={`bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 ${className}`}>\n        <button\n          onClick={() => setIsExpanded(true)}\n          className=\"w-full p-3 flex items-center justify-between hover:bg-gray-50 transition-colors rounded-lg\"\n        >\n          <div className=\"flex items-center space-x-2\">\n            <BarChartIcon className=\"w-4 h-4 text-blue-600\" />\n            <Typography.Caption className=\"font-medium text-gray-700\">\n              Progress\n            </Typography.Caption>\n          </div>\n\n          <div className=\"flex items-center space-x-3\">\n            {storyProgress && (\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 bg-gray-200 rounded-full h-1.5\">\n                  <div\n                    className=\"bg-blue-600 h-1.5 rounded-full transition-all duration-300\"\n                    style={{ width: `${storyProgress.completionPercentage}%` }}\n                  />\n                </div>\n                <Typography.Micro className=\"text-gray-500 min-w-[2rem]\">\n                  {formatPercentage(storyProgress.completionPercentage)}\n                </Typography.Micro>\n              </div>\n            )}\n            <ChevronUpIcon className=\"w-4 h-4 text-gray-400\" />\n          </div>\n        </button>\n      </div>\n    )\n  }\n\n  // Expanded view - full details\n  return (\n    <div className={`bg-white rounded-lg border border-gray-200 shadow-lg ${className}`}>\n      <div className=\"flex items-center justify-between p-3 border-b border-gray-100\">\n        <div className=\"flex items-center space-x-2\">\n          <BarChartIcon className=\"w-4 h-4 text-blue-600\" />\n          <Typography.Body className=\"font-semibold text-gray-800\">\n            Learning Progress\n          </Typography.Body>\n        </div>\n        <IconButton\n          icon={<ChevronDownIcon className=\"w-4 h-4\" />}\n          onClick={() => setIsExpanded(false)}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Collapse progress\"\n        />\n      </div>\n\n      <div className=\"p-3\">\n\n      {/* Current Story Progress */}\n      {storyProgress && (\n        <div className=\"mb-4 pb-4 border-b border-gray-100\">\n          <Typography.Caption className=\"text-gray-600 mb-2\">\n            Current Story\n          </Typography.Caption>\n          \n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between items-center\">\n              <Typography.Micro className=\"text-gray-500\">\n                Completion\n              </Typography.Micro>\n              <Typography.Micro className=\"font-medium text-blue-600\">\n                {formatPercentage(storyProgress.completionPercentage)}\n              </Typography.Micro>\n            </div>\n            \n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div \n                className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\n                style={{ width: `${storyProgress.completionPercentage}%` }}\n              />\n            </div>\n            \n            <div className=\"grid grid-cols-2 gap-4 mt-3\">\n              <div>\n                <Typography.Micro className=\"text-gray-500\">\n                  Words Learned\n                </Typography.Micro>\n                <Typography.Caption className=\"font-medium text-green-600\">\n                  {storyProgress.wordsLearned.length}\n                </Typography.Caption>\n              </div>\n              \n              <div>\n                <Typography.Micro className=\"text-gray-500\">\n                  Time Spent\n                </Typography.Micro>\n                <Typography.Caption className=\"font-medium text-purple-600\">\n                  {formatTime(storyProgress.timeSpent)}\n                </Typography.Caption>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Overall Statistics */}\n      {overallStats && (\n        <div>\n          <Typography.Caption className=\"text-gray-600 mb-2\">\n            Overall Stats\n          </Typography.Caption>\n          \n          <div className=\"grid grid-cols-2 gap-3\">\n            <div className=\"text-center p-2 bg-blue-50 rounded-lg\">\n              <Typography.Body className=\"font-bold text-blue-600\">\n                {overallStats.totalStoriesStarted}\n              </Typography.Body>\n              <Typography.Micro className=\"text-blue-600\">\n                Stories Started\n              </Typography.Micro>\n            </div>\n            \n            <div className=\"text-center p-2 bg-green-50 rounded-lg\">\n              <Typography.Body className=\"font-bold text-green-600\">\n                {overallStats.totalStoriesCompleted}\n              </Typography.Body>\n              <Typography.Micro className=\"text-green-600\">\n                Completed\n              </Typography.Micro>\n            </div>\n            \n            <div className=\"text-center p-2 bg-purple-50 rounded-lg\">\n              <Typography.Body className=\"font-bold text-purple-600\">\n                {overallStats.totalWordsLearned}\n              </Typography.Body>\n              <Typography.Micro className=\"text-purple-600\">\n                Words Learned\n              </Typography.Micro>\n            </div>\n            \n            <div className=\"text-center p-2 bg-orange-50 rounded-lg\">\n              <Typography.Body className=\"font-bold text-orange-600\">\n                {formatTime(overallStats.totalTimeSpent)}\n              </Typography.Body>\n              <Typography.Micro className=\"text-orange-600\">\n                Total Time\n              </Typography.Micro>\n            </div>\n          </div>\n          \n          {overallStats.streakDays > 0 && (\n            <div className=\"mt-3 text-center p-2 bg-yellow-50 rounded-lg\">\n              <Typography.Caption className=\"font-medium text-yellow-700\">\n                🔥 {overallStats.streakDays} day streak!\n              </Typography.Caption>\n            </div>\n          )}\n        </div>\n      )}\n\n        {!storyProgress && !overallStats && (\n          <div className=\"text-center py-4\">\n            <Typography.Caption className=\"text-gray-500\">\n              Start reading to track your progress\n            </Typography.Caption>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AASO,SAAS,gBAAgB,KAAqE;QAArE,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,EAAwB,GAArE;;IAC9B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa,CAAC;QAClB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAE9C,IAAI,QAAQ,GAAG;YACb,OAAO,AAAC,GAAY,OAAV,OAAM,MAAY,OAAR,SAAQ;QAC9B;QACA,OAAO,AAAC,GAAU,OAAR,SAAQ;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,AAAC,GAAyB,OAAvB,KAAK,KAAK,CAAC,aAAY;IACnC;IAEA,4CAA4C;IAC5C,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC;YAAI,WAAW,AAAC,oGAA6G,OAAV;sBAClH,cAAA,6LAAC;gBACC,SAAS,IAAM,cAAc;gBAC7B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mLAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAA4B;;;;;;;;;;;;kCAK5D,6LAAC;wBAAI,WAAU;;4BACZ,+BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAAqC,OAAnC,cAAc,oBAAoB,EAAC;4CAAG;;;;;;;;;;;kDAG7D,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wCAAC,WAAU;kDACzB,iBAAiB,cAAc,oBAAoB;;;;;;;;;;;;0CAI1D,6LAAC,mLAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKnC;IAEA,+BAA+B;IAC/B,qBACE,6LAAC;QAAI,WAAW,AAAC,wDAAiE,OAAV;;0BACtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mLAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gCAAC,WAAU;0CAA8B;;;;;;;;;;;;kCAI3D,6LAAC,yIAAA,CAAA,aAAU;wBACT,oBAAM,6LAAC,mLAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;wBACjC,SAAS,IAAM,cAAc;wBAC7B,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;0BAIf,6LAAC;gBAAI,WAAU;;oBAGd,+BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAAqB;;;;;;0CAInD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DAAgB;;;;;;0DAG5C,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DACzB,iBAAiB,cAAc,oBAAoB;;;;;;;;;;;;kDAIxD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,AAAC,GAAqC,OAAnC,cAAc,oBAAoB,EAAC;4CAAG;;;;;;;;;;;kDAI7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wDAAC,WAAU;kEAAgB;;;;;;kEAG5C,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAC3B,cAAc,YAAY,CAAC,MAAM;;;;;;;;;;;;0DAItC,6LAAC;;kEACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;wDAAC,WAAU;kEAAgB;;;;;;kEAG5C,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAC3B,WAAW,cAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS9C,8BACC,6LAAC;;0CACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAAqB;;;;;;0CAInD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gDAAC,WAAU;0DACxB,aAAa,mBAAmB;;;;;;0DAEnC,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DAAgB;;;;;;;;;;;;kDAK9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gDAAC,WAAU;0DACxB,aAAa,qBAAqB;;;;;;0DAErC,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DAAiB;;;;;;;;;;;;kDAK/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gDAAC,WAAU;0DACxB,aAAa,iBAAiB;;;;;;0DAEjC,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DAAkB;;;;;;;;;;;;kDAKhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gDAAC,WAAU;0DACxB,WAAW,aAAa,cAAc;;;;;;0DAEzC,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DAAkB;;;;;;;;;;;;;;;;;;4BAMjD,aAAa,UAAU,GAAG,mBACzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;oCAAC,WAAU;;wCAA8B;wCACtD,aAAa,UAAU;wCAAC;;;;;;;;;;;;;;;;;;oBAOnC,CAAC,iBAAiB,CAAC,8BAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;4BAAC,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GA1LgB;KAAA", "debugId": null}}, {"offset": {"line": 2742, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useAudioPlayer.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { AudioPlayerState, WordTiming } from '../types'\n\ninterface UseAudioPlayerProps {\n  audioUrl?: string\n  wordTimings?: WordTiming[]\n  onWordHighlight?: (wordIndex: number) => void\n}\n\nexport function useAudioPlayer({ \n  audioUrl, \n  wordTimings = [], \n  onWordHighlight \n}: UseAudioPlayerProps) {\n  const audioRef = useRef<HTMLAudioElement | null>(null)\n  const [state, setState] = useState<AudioPlayerState>({\n    isPlaying: false,\n    currentTime: 0,\n    duration: 0,\n    volume: 1.0\n  })\n\n  // Initialize audio element\n  useEffect(() => {\n    if (!audioUrl) return\n\n    const audio = new Audio(audioUrl)\n    audioRef.current = audio\n\n    const handleLoadedMetadata = () => {\n      setState(prev => ({\n        ...prev,\n        duration: audio.duration || 0\n      }))\n    }\n\n    const handleTimeUpdate = () => {\n      setState(prev => ({\n        ...prev,\n        currentTime: audio.currentTime\n      }))\n\n      // Find current word based on time\n      if (wordTimings.length > 0 && onWordHighlight) {\n        const currentWordIndex = wordTimings.findIndex(\n          timing => audio.currentTime >= timing.startTime && audio.currentTime <= timing.endTime\n        )\n        if (currentWordIndex !== -1) {\n          onWordHighlight(currentWordIndex)\n        }\n      }\n    }\n\n    const handleEnded = () => {\n      setState(prev => ({\n        ...prev,\n        isPlaying: false,\n        currentTime: 0\n      }))\n    }\n\n    const handleError = (e: Event) => {\n      console.error('Audio error:', e)\n      setState(prev => ({\n        ...prev,\n        isPlaying: false\n      }))\n    }\n\n    audio.addEventListener('loadedmetadata', handleLoadedMetadata)\n    audio.addEventListener('timeupdate', handleTimeUpdate)\n    audio.addEventListener('ended', handleEnded)\n    audio.addEventListener('error', handleError)\n\n    return () => {\n      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)\n      audio.removeEventListener('timeupdate', handleTimeUpdate)\n      audio.removeEventListener('ended', handleEnded)\n      audio.removeEventListener('error', handleError)\n      audio.pause()\n    }\n  }, [audioUrl, wordTimings, onWordHighlight])\n\n  const play = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.play()\n      setState(prev => ({ ...prev, isPlaying: true }))\n    }\n  }, [])\n\n  const pause = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.pause()\n      setState(prev => ({ ...prev, isPlaying: false }))\n    }\n  }, [])\n\n  const togglePlayPause = useCallback(() => {\n    if (state.isPlaying) {\n      pause()\n    } else {\n      play()\n    }\n  }, [state.isPlaying, play, pause])\n\n  const seek = useCallback((time: number) => {\n    if (audioRef.current) {\n      audioRef.current.currentTime = time\n      setState(prev => ({ ...prev, currentTime: time }))\n    }\n  }, [])\n\n  const setVolume = useCallback((volume: number) => {\n    if (audioRef.current) {\n      audioRef.current.volume = volume\n      setState(prev => ({ ...prev, volume }))\n    }\n  }, [])\n\n  return {\n    state,\n    play,\n    pause,\n    togglePlayPause,\n    seek,\n    setVolume,\n    isReady: !!audioRef.current && state.duration > 0\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAWO,SAAS,eAAe,KAIT;QAJS,EAC7B,QAAQ,EACR,cAAc,EAAE,EAChB,eAAe,EACK,GAJS;;IAK7B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,WAAW;QACX,aAAa;QACb,UAAU;QACV,QAAQ;IACV;IAEA,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,UAAU;YAEf,MAAM,QAAQ,IAAI,MAAM;YACxB,SAAS,OAAO,GAAG;YAEnB,MAAM;iEAAuB;oBAC3B;yEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,UAAU,MAAM,QAAQ,IAAI;4BAC9B,CAAC;;gBACH;;YAEA,MAAM;6DAAmB;oBACvB;qEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,aAAa,MAAM,WAAW;4BAChC,CAAC;;oBAED,kCAAkC;oBAClC,IAAI,YAAY,MAAM,GAAG,KAAK,iBAAiB;wBAC7C,MAAM,mBAAmB,YAAY,SAAS;0FAC5C,CAAA,SAAU,MAAM,WAAW,IAAI,OAAO,SAAS,IAAI,MAAM,WAAW,IAAI,OAAO,OAAO;;wBAExF,IAAI,qBAAqB,CAAC,GAAG;4BAC3B,gBAAgB;wBAClB;oBACF;gBACF;;YAEA,MAAM;wDAAc;oBAClB;gEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,WAAW;gCACX,aAAa;4BACf,CAAC;;gBACH;;YAEA,MAAM;wDAAc,CAAC;oBACnB,QAAQ,KAAK,CAAC,gBAAgB;oBAC9B;gEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,WAAW;4BACb,CAAC;;gBACH;;YAEA,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,SAAS;YAEhC;4CAAO;oBACL,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,KAAK;gBACb;;QACF;mCAAG;QAAC;QAAU;QAAa;KAAgB;IAE3C,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACvB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,IAAI;gBACrB;wDAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;wBAAK,CAAC;;YAChD;QACF;2CAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YACxB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,KAAK;gBACtB;yDAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;wBAAM,CAAC;;YACjD;QACF;4CAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAClC,IAAI,MAAM,SAAS,EAAE;gBACnB;YACF,OAAO;gBACL;YACF;QACF;sDAAG;QAAC,MAAM,SAAS;QAAE;QAAM;KAAM;IAEjC,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YACxB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,WAAW,GAAG;gBAC/B;wDAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,aAAa;wBAAK,CAAC;;YAClD;QACF;2CAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC7B,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,MAAM,GAAG;gBAC1B;6DAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE;wBAAO,CAAC;;YACvC;QACF;gDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,CAAC,CAAC,SAAS,OAAO,IAAI,MAAM,QAAQ,GAAG;IAClD;AACF;GAvHgB", "debugId": null}}, {"offset": {"line": 2920, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useTranslation.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { TranslationResult } from '../types'\n\n// Mock translation service - in a real app this would call an API\nconst mockTranslationService = {\n  async translateWord(word: string, fromLang: string, toLang: string): Promise<TranslationResult> {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 800))\n    \n    // Enhanced mock translations with more examples\n    const mockTranslations: Record<string, TranslationResult> = {\n      'plane': {\n        translation: 'avion',\n        pronunciation: 'a-vi-ɔ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'I like riding on planes.',\n          translation: 'J\\'aime voyager en avion.'\n        }\n      },\n      'aircraft': {\n        translation: 'aéronef',\n        pronunciation: 'a-e-ʁo-nɛf',\n        provider: 'mock-translate',\n        example: {\n          original: 'The aircraft landed safely.',\n          translation: 'L\\'aéronef a atterri en sécurité.'\n        }\n      },\n      'huge': {\n        translation: 'énorme',\n        pronunciation: 'e-nɔʁm',\n        provider: 'mock-translate',\n        example: {\n          original: 'That\\'s a huge building.',\n          translation: 'C\\'est un énorme bâtiment.'\n        }\n      },\n      'cloud': {\n        translation: 'nuage',\n        pronunciation: 'ny-aʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'The cloud is white.',\n          translation: 'Le nuage est blanc.'\n        }\n      },\n      'airport': {\n        translation: 'aéroport',\n        pronunciation: 'a-e-ʁo-pɔʁ',\n        provider: 'mock-translate',\n        example: {\n          original: 'We arrived at the airport.',\n          translation: 'Nous sommes arrivés à l\\'aéroport.'\n        }\n      },\n      'seat': {\n        translation: 'siège',\n        pronunciation: 'sjɛʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'Please take a seat.',\n          translation: 'Veuillez prendre un siège.'\n        }\n      },\n      'time': {\n        translation: 'temps',\n        pronunciation: 'tɑ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'What time is it?',\n          translation: 'Quelle heure est-il?'\n        }\n      },\n      'world': {\n        translation: 'monde',\n        pronunciation: 'mɔ̃d',\n        provider: 'mock-translate',\n        example: {\n          original: 'The world is beautiful.',\n          translation: 'Le monde est beau.'\n        }\n      }\n    }\n    \n    const cleanWord = word.toLowerCase().replace(/[.,!?;:]/, '')\n    const translation = mockTranslations[cleanWord]\n    \n    if (translation) {\n      return translation\n    }\n    \n    // Fallback for unknown words\n    return {\n      translation: `[Translation for \"${word}\" not available]`,\n      provider: 'mock-translate',\n      confidence: 0.5\n    }\n  }\n}\n\ninterface UseTranslationState {\n  isLoading: boolean\n  error: string | null\n  lastTranslation: TranslationResult | null\n}\n\nexport function useTranslation() {\n  const [state, setState] = useState<UseTranslationState>({\n    isLoading: false,\n    error: null,\n    lastTranslation: null\n  })\n\n  const translateWord = useCallback(async (\n    word: string, \n    fromLang: string = 'en', \n    toLang: string = 'fr'\n  ): Promise<TranslationResult | null> => {\n    setState(prev => ({\n      ...prev,\n      isLoading: true,\n      error: null\n    }))\n\n    try {\n      const result = await mockTranslationService.translateWord(word, fromLang, toLang)\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        lastTranslation: result\n      }))\n      \n      return result\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Translation failed'\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: errorMessage\n      }))\n      \n      return null\n    }\n  }, [])\n\n  const clearError = useCallback(() => {\n    setState(prev => ({ ...prev, error: null }))\n  }, [])\n\n  return {\n    ...state,\n    translateWord,\n    clearError\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKA,kEAAkE;AAClE,MAAM,yBAAyB;IAC7B,MAAM,eAAc,IAAY,EAAE,QAAgB,EAAE,MAAc;QAChE,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gDAAgD;QAChD,MAAM,mBAAsD;YAC1D,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,YAAY;gBACV,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,WAAW;gBACT,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;QACF;QAEA,MAAM,YAAY,KAAK,WAAW,GAAG,OAAO,CAAC,YAAY;QACzD,MAAM,cAAc,gBAAgB,CAAC,UAAU;QAE/C,IAAI,aAAa;YACf,OAAO;QACT;QAEA,6BAA6B;QAC7B,OAAO;YACL,aAAa,AAAC,qBAAyB,OAAL,MAAK;YACvC,UAAU;YACV,YAAY;QACd;IACF;AACF;AAQO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,WAAW;QACX,OAAO;QACP,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,eAChC;gBACA,4EAAmB,MACnB,0EAAiB;YAEjB;6DAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO;oBACT,CAAC;;YAED,IAAI;gBACF,MAAM,SAAS,MAAM,uBAAuB,aAAa,CAAC,MAAM,UAAU;gBAE1E;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,WAAW;4BACX,iBAAiB;wBACnB,CAAC;;gBAED,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAE9D;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,WAAW;4BACX,OAAO;wBACT,CAAC;;gBAED,OAAO;YACT;QACF;oDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B;0DAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO;oBAAK,CAAC;;QAC5C;iDAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA;IACF;AACF;GAlDgB", "debugId": null}}, {"offset": {"line": 3086, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useKeyboardShortcuts.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\ninterface KeyboardShortcuts {\n  onPlayPause?: () => void\n  onSeekForward?: () => void\n  onSeekBackward?: () => void\n  onCloseTranslation?: () => void\n}\n\nexport function useKeyboardShortcuts({\n  onPlayPause,\n  onSeekForward,\n  onSeekBackward,\n  onCloseTranslation\n}: KeyboardShortcuts) {\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Don't trigger shortcuts if user is typing in an input\n      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {\n        return\n      }\n\n      switch (event.code) {\n        case 'Space':\n          event.preventDefault()\n          onPlayPause?.()\n          break\n        case 'ArrowRight':\n          if (event.shiftKey) {\n            event.preventDefault()\n            onSeekForward?.()\n          }\n          break\n        case 'ArrowLeft':\n          if (event.shiftKey) {\n            event.preventDefault()\n            onSeekBackward?.()\n          }\n          break\n        case 'Escape':\n          event.preventDefault()\n          onCloseTranslation?.()\n          break\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [onPlayPause, onSeekForward, onSeekBackward, onCloseTranslation])\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAWO,SAAS,qBAAqB,KAKjB;QALiB,EACnC,WAAW,EACX,aAAa,EACb,cAAc,EACd,kBAAkB,EACA,GALiB;;IAMnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;gEAAgB,CAAC;oBACrB,wDAAwD;oBACxD,IAAI,MAAM,MAAM,YAAY,oBAAoB,MAAM,MAAM,YAAY,qBAAqB;wBAC3F;oBACF;oBAEA,OAAQ,MAAM,IAAI;wBAChB,KAAK;4BACH,MAAM,cAAc;4BACpB,wBAAA,kCAAA;4BACA;wBACF,KAAK;4BACH,IAAI,MAAM,QAAQ,EAAE;gCAClB,MAAM,cAAc;gCACpB,0BAAA,oCAAA;4BACF;4BACA;wBACF,KAAK;4BACH,IAAI,MAAM,QAAQ,EAAE;gCAClB,MAAM,cAAc;gCACpB,2BAAA,qCAAA;4BACF;4BACA;wBACF,KAAK;4BACH,MAAM,cAAc;4BACpB,+BAAA,yCAAA;4BACA;oBACJ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;kDAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;yCAAG;QAAC;QAAa;QAAe;QAAgB;KAAmB;AACrE;GAxCgB", "debugId": null}}, {"offset": {"line": 3150, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/services/progress-service.ts"], "sourcesContent": ["// User Progress Tracking Service\n// This service handles user progress, statistics, and learning analytics\n\nexport interface UserProgress {\n  userId: string\n  storyId: string\n  wordsLearned: string[]\n  completionPercentage: number\n  timeSpent: number // in seconds\n  lastAccessedAt: string\n  startedAt: string\n  completedAt?: string\n  statistics: {\n    totalWords: number\n    uniqueWordsEncountered: number\n    translationsRequested: number\n    audioPlayTime: number\n    sessionsCount: number\n  }\n}\n\nexport interface LearningSession {\n  sessionId: string\n  storyId: string\n  startTime: string\n  endTime?: string\n  wordsEncountered: string[]\n  translationsRequested: string[]\n  audioPlayTime: number\n  completionPercentage: number\n}\n\nexport interface ProgressStats {\n  totalStoriesStarted: number\n  totalStoriesCompleted: number\n  totalWordsLearned: number\n  totalTimeSpent: number\n  averageCompletionRate: number\n  streakDays: number\n  lastActiveDate: string\n}\n\nclass ProgressService {\n  private storageKey = 'polistory-progress'\n  private sessionKey = 'polistory-current-session'\n\n  // Get user's overall progress statistics\n  getProgressStats(): ProgressStats {\n    const allProgress = this.getAllProgress()\n    const now = new Date()\n    const today = now.toDateString()\n\n    const totalStoriesStarted = allProgress.length\n    const totalStoriesCompleted = allProgress.filter(p => p.completedAt).length\n    const totalWordsLearned = [...new Set(allProgress.flatMap(p => p.wordsLearned))].length\n    const totalTimeSpent = allProgress.reduce((sum, p) => sum + p.timeSpent, 0)\n    const averageCompletionRate = totalStoriesStarted > 0 \n      ? allProgress.reduce((sum, p) => sum + p.completionPercentage, 0) / totalStoriesStarted \n      : 0\n\n    // Calculate streak (simplified - just check if user was active today)\n    const lastActiveDate = allProgress.length > 0 \n      ? allProgress.reduce((latest, p) => \n          new Date(p.lastAccessedAt) > new Date(latest) ? p.lastAccessedAt : latest\n        , allProgress[0].lastAccessedAt)\n      : today\n\n    const streakDays = new Date(lastActiveDate).toDateString() === today ? 1 : 0\n\n    return {\n      totalStoriesStarted,\n      totalStoriesCompleted,\n      totalWordsLearned,\n      totalTimeSpent,\n      averageCompletionRate,\n      streakDays,\n      lastActiveDate\n    }\n  }\n\n  // Get progress for a specific story\n  getStoryProgress(storyId: string): UserProgress | null {\n    const allProgress = this.getAllProgress()\n    return allProgress.find(p => p.storyId === storyId) || null\n  }\n\n  // Start a new learning session\n  startSession(storyId: string): LearningSession {\n    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    const session: LearningSession = {\n      sessionId,\n      storyId,\n      startTime: new Date().toISOString(),\n      wordsEncountered: [],\n      translationsRequested: [],\n      audioPlayTime: 0,\n      completionPercentage: 0\n    }\n\n    localStorage.setItem(this.sessionKey, JSON.stringify(session))\n    return session\n  }\n\n  // Update current session\n  updateSession(updates: Partial<LearningSession>): void {\n    const currentSession = this.getCurrentSession()\n    if (!currentSession) return\n\n    const updatedSession = { ...currentSession, ...updates }\n    localStorage.setItem(this.sessionKey, JSON.stringify(updatedSession))\n  }\n\n  // End current session and update progress\n  endSession(): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    const endTime = new Date().toISOString()\n    const sessionDuration = new Date(endTime).getTime() - new Date(session.startTime).getTime()\n    const sessionDurationSeconds = Math.floor(sessionDuration / 1000)\n\n    // Update or create progress record\n    let progress = this.getStoryProgress(session.storyId)\n    \n    if (!progress) {\n      progress = {\n        userId: 'default-user', // In a real app, this would be the actual user ID\n        storyId: session.storyId,\n        wordsLearned: [],\n        completionPercentage: 0,\n        timeSpent: 0,\n        lastAccessedAt: endTime,\n        startedAt: session.startTime,\n        statistics: {\n          totalWords: 0,\n          uniqueWordsEncountered: 0,\n          translationsRequested: 0,\n          audioPlayTime: 0,\n          sessionsCount: 0\n        }\n      }\n    }\n\n    // Update progress with session data\n    const newWordsLearned = session.translationsRequested.filter(\n      word => !progress!.wordsLearned.includes(word)\n    )\n    \n    progress.wordsLearned = [...progress.wordsLearned, ...newWordsLearned]\n    progress.completionPercentage = Math.max(progress.completionPercentage, session.completionPercentage)\n    progress.timeSpent += sessionDurationSeconds\n    progress.lastAccessedAt = endTime\n    progress.statistics.uniqueWordsEncountered = [...new Set([\n      ...session.wordsEncountered,\n      ...progress.wordsLearned\n    ])].length\n    progress.statistics.translationsRequested += session.translationsRequested.length\n    progress.statistics.audioPlayTime += session.audioPlayTime\n    progress.statistics.sessionsCount += 1\n\n    // Mark as completed if 90% or more\n    if (progress.completionPercentage >= 90 && !progress.completedAt) {\n      progress.completedAt = endTime\n    }\n\n    this.saveStoryProgress(progress)\n    localStorage.removeItem(this.sessionKey)\n  }\n\n  // Track word encounter\n  trackWordEncounter(word: string): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    if (!session.wordsEncountered.includes(word)) {\n      session.wordsEncountered.push(word)\n      this.updateSession({ wordsEncountered: session.wordsEncountered })\n    }\n  }\n\n  // Track translation request\n  trackTranslationRequest(word: string): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    if (!session.translationsRequested.includes(word)) {\n      session.translationsRequested.push(word)\n      this.updateSession({ translationsRequested: session.translationsRequested })\n    }\n  }\n\n  // Track audio play time\n  trackAudioPlayTime(seconds: number): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    this.updateSession({ audioPlayTime: session.audioPlayTime + seconds })\n  }\n\n  // Update completion percentage\n  updateCompletionPercentage(percentage: number): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    this.updateSession({ completionPercentage: Math.max(session.completionPercentage, percentage) })\n  }\n\n  // Private helper methods\n  private getCurrentSession(): LearningSession | null {\n    const sessionData = localStorage.getItem(this.sessionKey)\n    return sessionData ? JSON.parse(sessionData) : null\n  }\n\n  private getAllProgress(): UserProgress[] {\n    const progressData = localStorage.getItem(this.storageKey)\n    return progressData ? JSON.parse(progressData) : []\n  }\n\n  private saveStoryProgress(progress: UserProgress): void {\n    const allProgress = this.getAllProgress()\n    const existingIndex = allProgress.findIndex(p => p.storyId === progress.storyId)\n    \n    if (existingIndex >= 0) {\n      allProgress[existingIndex] = progress\n    } else {\n      allProgress.push(progress)\n    }\n    \n    localStorage.setItem(this.storageKey, JSON.stringify(allProgress))\n  }\n}\n\nexport const progressService = new ProgressService()\n"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,yEAAyE;;;;;;AAyCzE,MAAM;IAIJ,yCAAyC;IACzC,mBAAkC;QAChC,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,YAAY;QAE9B,MAAM,sBAAsB,YAAY,MAAM;QAC9C,MAAM,wBAAwB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;QAC3E,MAAM,oBAAoB;eAAI,IAAI,IAAI,YAAY,OAAO,CAAC,CAAA,IAAK,EAAE,YAAY;SAAG,CAAC,MAAM;QACvF,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE;QACzE,MAAM,wBAAwB,sBAAsB,IAChD,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,oBAAoB,EAAE,KAAK,sBAClE;QAEJ,sEAAsE;QACtE,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,MAAM,CAAC,CAAC,QAAQ,IAC1B,IAAI,KAAK,EAAE,cAAc,IAAI,IAAI,KAAK,UAAU,EAAE,cAAc,GAAG,QACnE,WAAW,CAAC,EAAE,CAAC,cAAc,IAC/B;QAEJ,MAAM,aAAa,IAAI,KAAK,gBAAgB,YAAY,OAAO,QAAQ,IAAI;QAE3E,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,oCAAoC;IACpC,iBAAiB,OAAe,EAAuB;QACrD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,YAAY;IACzD;IAEA,+BAA+B;IAC/B,aAAa,OAAe,EAAmB;QAC7C,MAAM,YAAY,AAAC,WAAwB,OAAd,KAAK,GAAG,IAAG,KAA2C,OAAxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChF,MAAM,UAA2B;YAC/B;YACA;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,kBAAkB,EAAE;YACpB,uBAAuB,EAAE;YACzB,eAAe;YACf,sBAAsB;QACxB;QAEA,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;QACrD,OAAO;IACT;IAEA,yBAAyB;IACzB,cAAc,OAAiC,EAAQ;QACrD,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAC7C,IAAI,CAAC,gBAAgB;QAErB,MAAM,iBAAiB;YAAE,GAAG,cAAc;YAAE,GAAG,OAAO;QAAC;QACvD,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;IACvD;IAEA,0CAA0C;IAC1C,aAAmB;QACjB,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,MAAM,UAAU,IAAI,OAAO,WAAW;QACtC,MAAM,kBAAkB,IAAI,KAAK,SAAS,OAAO,KAAK,IAAI,KAAK,QAAQ,SAAS,EAAE,OAAO;QACzF,MAAM,yBAAyB,KAAK,KAAK,CAAC,kBAAkB;QAE5D,mCAAmC;QACnC,IAAI,WAAW,IAAI,CAAC,gBAAgB,CAAC,QAAQ,OAAO;QAEpD,IAAI,CAAC,UAAU;YACb,WAAW;gBACT,QAAQ;gBACR,SAAS,QAAQ,OAAO;gBACxB,cAAc,EAAE;gBAChB,sBAAsB;gBACtB,WAAW;gBACX,gBAAgB;gBAChB,WAAW,QAAQ,SAAS;gBAC5B,YAAY;oBACV,YAAY;oBACZ,wBAAwB;oBACxB,uBAAuB;oBACvB,eAAe;oBACf,eAAe;gBACjB;YACF;QACF;QAEA,oCAAoC;QACpC,MAAM,kBAAkB,QAAQ,qBAAqB,CAAC,MAAM,CAC1D,CAAA,OAAQ,CAAC,SAAU,YAAY,CAAC,QAAQ,CAAC;QAG3C,SAAS,YAAY,GAAG;eAAI,SAAS,YAAY;eAAK;SAAgB;QACtE,SAAS,oBAAoB,GAAG,KAAK,GAAG,CAAC,SAAS,oBAAoB,EAAE,QAAQ,oBAAoB;QACpG,SAAS,SAAS,IAAI;QACtB,SAAS,cAAc,GAAG;QAC1B,SAAS,UAAU,CAAC,sBAAsB,GAAG;eAAI,IAAI,IAAI;mBACpD,QAAQ,gBAAgB;mBACxB,SAAS,YAAY;aACzB;SAAE,CAAC,MAAM;QACV,SAAS,UAAU,CAAC,qBAAqB,IAAI,QAAQ,qBAAqB,CAAC,MAAM;QACjF,SAAS,UAAU,CAAC,aAAa,IAAI,QAAQ,aAAa;QAC1D,SAAS,UAAU,CAAC,aAAa,IAAI;QAErC,mCAAmC;QACnC,IAAI,SAAS,oBAAoB,IAAI,MAAM,CAAC,SAAS,WAAW,EAAE;YAChE,SAAS,WAAW,GAAG;QACzB;QAEA,IAAI,CAAC,iBAAiB,CAAC;QACvB,aAAa,UAAU,CAAC,IAAI,CAAC,UAAU;IACzC;IAEA,uBAAuB;IACvB,mBAAmB,IAAY,EAAQ;QACrC,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,CAAC,OAAO;YAC5C,QAAQ,gBAAgB,CAAC,IAAI,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC;gBAAE,kBAAkB,QAAQ,gBAAgB;YAAC;QAClE;IACF;IAEA,4BAA4B;IAC5B,wBAAwB,IAAY,EAAQ;QAC1C,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,QAAQ,qBAAqB,CAAC,QAAQ,CAAC,OAAO;YACjD,QAAQ,qBAAqB,CAAC,IAAI,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC;gBAAE,uBAAuB,QAAQ,qBAAqB;YAAC;QAC5E;IACF;IAEA,wBAAwB;IACxB,mBAAmB,OAAe,EAAQ;QACxC,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,aAAa,CAAC;YAAE,eAAe,QAAQ,aAAa,GAAG;QAAQ;IACtE;IAEA,+BAA+B;IAC/B,2BAA2B,UAAkB,EAAQ;QACnD,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,aAAa,CAAC;YAAE,sBAAsB,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE;QAAY;IAChG;IAEA,yBAAyB;IACjB,oBAA4C;QAClD,MAAM,cAAc,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;QACxD,OAAO,cAAc,KAAK,KAAK,CAAC,eAAe;IACjD;IAEQ,iBAAiC;QACvC,MAAM,eAAe,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;QACzD,OAAO,eAAe,KAAK,KAAK,CAAC,gBAAgB,EAAE;IACrD;IAEQ,kBAAkB,QAAsB,EAAQ;QACtD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,gBAAgB,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAAS,OAAO;QAE/E,IAAI,iBAAiB,GAAG;YACtB,WAAW,CAAC,cAAc,GAAG;QAC/B,OAAO;YACL,YAAY,IAAI,CAAC;QACnB;QAEA,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;IACvD;;QA1LA,+KAAQ,cAAa;QACrB,+KAAQ,cAAa;;AA0LvB;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 3337, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useProgress.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { progressService, UserProgress, ProgressStats, LearningSession } from '../services/progress-service'\n\nexport function useProgress(storyId?: string) {\n  const [storyProgress, setStoryProgress] = useState<UserProgress | null>(null)\n  const [overallStats, setOverallStats] = useState<ProgressStats | null>(null)\n\n  // Load progress data\n  useEffect(() => {\n    if (storyId) {\n      const progress = progressService.getStoryProgress(storyId)\n      setStoryProgress(progress)\n    }\n    \n    const stats = progressService.getProgressStats()\n    setOverallStats(stats)\n  }, [storyId])\n\n  // Start a learning session (pure service call, no state updates)\n  const startSession = useCallback((sessionStoryId: string) => {\n    return progressService.startSession(sessionStoryId)\n  }, [])\n\n  // End the current session (pure service call, no state updates)\n  const endSession = useCallback(() => {\n    progressService.endSession()\n  }, [])\n\n  // Track word encounter\n  const trackWordEncounter = useCallback((word: string) => {\n    progressService.trackWordEncounter(word)\n  }, [])\n\n  // Track translation request\n  const trackTranslationRequest = useCallback((word: string) => {\n    progressService.trackTranslationRequest(word)\n  }, [])\n\n  // Track audio play time\n  const trackAudioPlayTime = useCallback((seconds: number) => {\n    progressService.trackAudioPlayTime(seconds)\n  }, [])\n\n  // Update completion percentage\n  const updateCompletionPercentage = useCallback((percentage: number) => {\n    progressService.updateCompletionPercentage(percentage)\n  }, [])\n\n  // Calculate reading progress based on current word position (without triggering updates)\n  const calculateReadingProgress = useCallback((currentWordIndex: number, totalWords: number) => {\n    if (totalWords === 0) return 0\n    return Math.round((currentWordIndex / totalWords) * 100)\n  }, [])\n\n  // Refresh progress data manually\n  const refreshProgress = useCallback(() => {\n    if (storyId) {\n      const progress = progressService.getStoryProgress(storyId)\n      setStoryProgress(progress)\n    }\n\n    const stats = progressService.getProgressStats()\n    setOverallStats(stats)\n  }, [storyId])\n\n  return {\n    storyProgress,\n    overallStats,\n    startSession,\n    endSession,\n    trackWordEncounter,\n    trackTranslationRequest,\n    trackAudioPlayTime,\n    updateCompletionPercentage,\n    calculateReadingProgress,\n    refreshProgress\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;;AAHA;;;AAKO,SAAS,YAAY,OAAgB;;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAEvE,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,SAAS;gBACX,MAAM,WAAW,gJAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC;gBAClD,iBAAiB;YACnB;YAEA,MAAM,QAAQ,gJAAA,CAAA,kBAAe,CAAC,gBAAgB;YAC9C,gBAAgB;QAClB;gCAAG;QAAC;KAAQ;IAEZ,iEAAiE;IACjE,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAChC,OAAO,gJAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QACtC;gDAAG,EAAE;IAEL,gEAAgE;IAChE,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC7B,gJAAA,CAAA,kBAAe,CAAC,UAAU;QAC5B;8CAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACtC,gJAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;QACrC;sDAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE,CAAC;YAC3C,gJAAA,CAAA,kBAAe,CAAC,uBAAuB,CAAC;QAC1C;2DAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACtC,gJAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;QACrC;sDAAG,EAAE;IAEL,+BAA+B;IAC/B,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC;YAC9C,gJAAA,CAAA,kBAAe,CAAC,0BAA0B,CAAC;QAC7C;8DAAG,EAAE;IAEL,yFAAyF;IACzF,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC,kBAA0B;YACtE,IAAI,eAAe,GAAG,OAAO;YAC7B,OAAO,KAAK,KAAK,CAAC,AAAC,mBAAmB,aAAc;QACtD;4DAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAClC,IAAI,SAAS;gBACX,MAAM,WAAW,gJAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC;gBAClD,iBAAiB;YACnB;YAEA,MAAM,QAAQ,gJAAA,CAAA,kBAAe,CAAC,gBAAgB;YAC9C,gBAAgB;QAClB;mDAAG;QAAC;KAAQ;IAEZ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GA1EgB", "debugId": null}}, {"offset": {"line": 3442, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/StoryReaderScreen.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { StoryHeader } from '../story/StoryHeader'\nimport { ReadingArea } from '../story/ReadingArea'\nimport { AudioPlayer } from '../story/AudioPlayer'\nimport { PageNavigation } from '../story/PageNavigation'\nimport { ErrorBoundary } from '../ui/ErrorBoundary'\nimport { KeyboardShortcutsHelp } from '../ui/KeyboardShortcutsHelp'\nimport { ProgressDisplay } from '../ui/ProgressDisplay'\nimport { useAudioPlayer } from '@/lib/hooks/useAudioPlayer'\nimport { useTranslation } from '@/lib/hooks/useTranslation'\nimport { useKeyboardShortcuts } from '@/lib/hooks/useKeyboardShortcuts'\nimport { useProgress } from '@/lib/hooks/useProgress'\nimport { Story, StoryReaderState, TranslationResult } from '@/lib/types'\n\ninterface StoryReaderScreenProps {\n  story: Story\n}\n\n\n\nexport function StoryReaderScreen({ story }: StoryReaderScreenProps) {\n  const [state, setState] = useState<StoryReaderState>({\n    currentWordIndex: -1,\n    selectedWord: undefined,\n    translationCard: {\n      isVisible: false,\n      word: '',\n      position: { x: 0, y: 0 },\n      translation: undefined,\n      isLoading: false\n    },\n    audioPlayer: {\n      isPlaying: false,\n      currentTime: 0,\n      duration: 0,\n      volume: 1.0\n    }\n  })\n\n  // Use translation hook\n  const translation = useTranslation()\n\n  // Use progress tracking\n  const progress = useProgress(story.id)\n\n  // Handle word highlighting during audio playback\n  const handleWordHighlight = useCallback((wordIndex: number) => {\n    setState(prev => ({\n      ...prev,\n      currentWordIndex: wordIndex\n    }))\n  }, [])\n\n  // Use audio player hook with real functionality\n  const audioPlayer = useAudioPlayer({\n    audioUrl: story.audioUrl,\n    wordTimings: story.wordTimings,\n    onWordHighlight: handleWordHighlight\n  })\n\n  // Start progress tracking session on mount\n  React.useEffect(() => {\n    progress.startSession(story.id)\n\n    return () => {\n      progress.endSession()\n      // Refresh progress data after session ends\n      setTimeout(() => {\n        progress.refreshProgress()\n      }, 100)\n    }\n  }, [story.id, progress])\n\n  // Track reading progress when current word changes (debounced)\n  React.useEffect(() => {\n    if (state.currentWordIndex >= 0 && story.wordTimings) {\n      const totalWords = story.wordTimings.length\n      if (totalWords > 0) {\n        const progressPercentage = Math.round((state.currentWordIndex / totalWords) * 100)\n\n        // Debounce progress updates to avoid excessive calls\n        const timeoutId = setTimeout(() => {\n          progress.updateCompletionPercentage(progressPercentage)\n        }, 100)\n\n        return () => clearTimeout(timeoutId)\n      }\n    }\n  }, [state.currentWordIndex, story.wordTimings, progress])\n\n  // Update state when audio player state changes\n  React.useEffect(() => {\n    setState(prev => ({\n      ...prev,\n      audioPlayer: audioPlayer.state\n    }))\n  }, [audioPlayer.state])\n\n  const handleWordClick = useCallback(async (word: string, index: number, event: React.MouseEvent) => {\n    // Close existing translation card if clicking the same word\n    if (state.translationCard.isVisible && state.translationCard.word === word) {\n      setState(prev => ({\n        ...prev,\n        translationCard: { ...prev.translationCard, isVisible: false }\n      }))\n      return\n    }\n\n    // Show loading state\n    setState(prev => ({\n      ...prev,\n      selectedWord: word,\n      translationCard: {\n        isVisible: true,\n        word,\n        position: { x: event.clientX, y: event.clientY },\n        translation: undefined,\n        isLoading: true\n      }\n    }))\n\n    // Track word encounter and translation request\n    progress.trackWordEncounter(word)\n    progress.trackTranslationRequest(word)\n\n    // Get translation using the hook\n    const translationResult = await translation.translateWord(word, 'en', 'fr')\n\n    setState(prev => ({\n      ...prev,\n      translationCard: {\n        ...prev.translationCard,\n        translation: translationResult || undefined,\n        isLoading: false\n      }\n    }))\n  }, [state.translationCard.isVisible, state.translationCard.word, translation])\n\n  const handleTranslationClose = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      translationCard: { ...prev.translationCard, isVisible: false }\n    }))\n  }, [])\n\n  const handlePlayPause = useCallback(() => {\n    audioPlayer.togglePlayPause()\n  }, [audioPlayer])\n\n  const handleSeek = useCallback((time: number) => {\n    audioPlayer.seek(time)\n  }, [audioPlayer])\n\n  const handleVolumeChange = useCallback((volume: number) => {\n    audioPlayer.setVolume(volume)\n  }, [audioPlayer])\n\n  const handleSeekForward = useCallback(() => {\n    const newTime = Math.min(state.audioPlayer.currentTime + 10, state.audioPlayer.duration)\n    audioPlayer.seek(newTime)\n  }, [audioPlayer, state.audioPlayer.currentTime, state.audioPlayer.duration])\n\n  const handleSeekBackward = useCallback(() => {\n    const newTime = Math.max(state.audioPlayer.currentTime - 10, 0)\n    audioPlayer.seek(newTime)\n  }, [audioPlayer, state.audioPlayer.currentTime])\n\n  // Setup keyboard shortcuts\n  useKeyboardShortcuts({\n    onPlayPause: handlePlayPause,\n    onSeekForward: handleSeekForward,\n    onSeekBackward: handleSeekBackward,\n    onCloseTranslation: handleTranslationClose\n  })\n\n  return (\n    <ErrorBoundary>\n      <div className=\"flex flex-col h-screen bg-white\">\n        <StoryHeader\n          title={story.title}\n          chapter={story.chapter}\n          isAudioPlaying={state.audioPlayer.isPlaying}\n          onBookmarkClick={() => console.log('Bookmark clicked')}\n          onMenuClick={() => console.log('Menu clicked')}\n        />\n        \n        <ReadingArea\n          content={story.content}\n          currentWordIndex={state.currentWordIndex}\n          translationCard={state.translationCard}\n          onWordClick={handleWordClick}\n          onTranslationClose={handleTranslationClose}\n          onTranslationPlayAudio={() => console.log('Play translation audio')}\n        />\n        \n        <PageNavigation\n          currentPage={24}\n          totalPages={120}\n          onPreviousPage={() => console.log('Previous page')}\n          onNextPage={() => console.log('Next page')}\n        />\n        \n        <AudioPlayer\n          state={state.audioPlayer}\n          onPlayPause={handlePlayPause}\n          onSeek={handleSeek}\n          onVolumeChange={handleVolumeChange}\n        />\n\n        {/* Floating UI Elements - positioned above audio player */}\n        <div className=\"fixed bottom-24 left-4 z-40 max-w-xs\">\n          <ProgressDisplay\n            storyProgress={progress.storyProgress}\n            overallStats={progress.overallStats}\n          />\n        </div>\n\n        <div className=\"fixed bottom-24 right-4 z-40\">\n          <KeyboardShortcutsHelp />\n        </div>\n      </div>\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;;AAsBO,SAAS,kBAAkB,KAAiC;QAAjC,EAAE,KAAK,EAA0B,GAAjC;;IAChC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,kBAAkB,CAAC;QACnB,cAAc;QACd,iBAAiB;YACf,WAAW;YACX,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAG,GAAG;YAAE;YACvB,aAAa;YACb,WAAW;QACb;QACA,aAAa;YACX,WAAW;YACX,aAAa;YACb,UAAU;YACV,QAAQ;QACV;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,iBAAc,AAAD;IAEjC,wBAAwB;IACxB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE;IAErC,iDAAiD;IACjD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACvC;sEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,kBAAkB;oBACpB,CAAC;;QACH;6DAAG,EAAE;IAEL,gDAAgD;IAChD,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,iBAAiB;IACnB;IAEA,2CAA2C;IAC3C,6JAAA,CAAA,UAAK,CAAC,SAAS;uCAAC;YACd,SAAS,YAAY,CAAC,MAAM,EAAE;YAE9B;+CAAO;oBACL,SAAS,UAAU;oBACnB,2CAA2C;oBAC3C;uDAAW;4BACT,SAAS,eAAe;wBAC1B;sDAAG;gBACL;;QACF;sCAAG;QAAC,MAAM,EAAE;QAAE;KAAS;IAEvB,+DAA+D;IAC/D,6JAAA,CAAA,UAAK,CAAC,SAAS;uCAAC;YACd,IAAI,MAAM,gBAAgB,IAAI,KAAK,MAAM,WAAW,EAAE;gBACpD,MAAM,aAAa,MAAM,WAAW,CAAC,MAAM;gBAC3C,IAAI,aAAa,GAAG;oBAClB,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,MAAM,gBAAgB,GAAG,aAAc;oBAE9E,qDAAqD;oBACrD,MAAM,YAAY;iEAAW;4BAC3B,SAAS,0BAA0B,CAAC;wBACtC;gEAAG;oBAEH;uDAAO,IAAM,aAAa;;gBAC5B;YACF;QACF;sCAAG;QAAC,MAAM,gBAAgB;QAAE,MAAM,WAAW;QAAE;KAAS;IAExD,+CAA+C;IAC/C,6JAAA,CAAA,UAAK,CAAC,SAAS;uCAAC;YACd;+CAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,aAAa,YAAY,KAAK;oBAChC,CAAC;;QACH;sCAAG;QAAC,YAAY,KAAK;KAAC;IAEtB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO,MAAc,OAAe;YACtE,4DAA4D;YAC5D,IAAI,MAAM,eAAe,CAAC,SAAS,IAAI,MAAM,eAAe,CAAC,IAAI,KAAK,MAAM;gBAC1E;sEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,iBAAiB;gCAAE,GAAG,KAAK,eAAe;gCAAE,WAAW;4BAAM;wBAC/D,CAAC;;gBACD;YACF;YAEA,qBAAqB;YACrB;kEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,cAAc;wBACd,iBAAiB;4BACf,WAAW;4BACX;4BACA,UAAU;gCAAE,GAAG,MAAM,OAAO;gCAAE,GAAG,MAAM,OAAO;4BAAC;4BAC/C,aAAa;4BACb,WAAW;wBACb;oBACF,CAAC;;YAED,+CAA+C;YAC/C,SAAS,kBAAkB,CAAC;YAC5B,SAAS,uBAAuB,CAAC;YAEjC,iCAAiC;YACjC,MAAM,oBAAoB,MAAM,YAAY,aAAa,CAAC,MAAM,MAAM;YAEtE;kEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;4BACf,GAAG,KAAK,eAAe;4BACvB,aAAa,qBAAqB;4BAClC,WAAW;wBACb;oBACF,CAAC;;QACH;yDAAG;QAAC,MAAM,eAAe,CAAC,SAAS;QAAE,MAAM,eAAe,CAAC,IAAI;QAAE;KAAY;IAE7E,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YACzC;yEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;4BAAE,GAAG,KAAK,eAAe;4BAAE,WAAW;wBAAM;oBAC/D,CAAC;;QACH;gEAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YAClC,YAAY,eAAe;QAC7B;yDAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC9B,YAAY,IAAI,CAAC;QACnB;oDAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACtC,YAAY,SAAS,CAAC;QACxB;4DAAG;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4DAAE;YACpC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW,GAAG,IAAI,MAAM,WAAW,CAAC,QAAQ;YACvF,YAAY,IAAI,CAAC;QACnB;2DAAG;QAAC;QAAa,MAAM,WAAW,CAAC,WAAW;QAAE,MAAM,WAAW,CAAC,QAAQ;KAAC;IAE3E,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACrC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW,GAAG,IAAI;YAC7D,YAAY,IAAI,CAAC;QACnB;4DAAG;QAAC;QAAa,MAAM,WAAW,CAAC,WAAW;KAAC;IAE/C,2BAA2B;IAC3B,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD,EAAE;QACnB,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,qBACE,6LAAC,4IAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,KAAK;oBAClB,SAAS,MAAM,OAAO;oBACtB,gBAAgB,MAAM,WAAW,CAAC,SAAS;oBAC3C,iBAAiB,IAAM,QAAQ,GAAG,CAAC;oBACnC,aAAa,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGjC,6LAAC,6IAAA,CAAA,cAAW;oBACV,SAAS,MAAM,OAAO;oBACtB,kBAAkB,MAAM,gBAAgB;oBACxC,iBAAiB,MAAM,eAAe;oBACtC,aAAa;oBACb,oBAAoB;oBACpB,wBAAwB,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAG5C,6LAAC,gJAAA,CAAA,iBAAc;oBACb,aAAa;oBACb,YAAY;oBACZ,gBAAgB,IAAM,QAAQ,GAAG,CAAC;oBAClC,YAAY,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGhC,6LAAC,6IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,WAAW;oBACxB,aAAa;oBACb,QAAQ;oBACR,gBAAgB;;;;;;8BAIlB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8IAAA,CAAA,kBAAe;wBACd,eAAe,SAAS,aAAa;wBACrC,cAAc,SAAS,YAAY;;;;;;;;;;;8BAIvC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oJAAA,CAAA,wBAAqB;;;;;;;;;;;;;;;;;;;;;AAKhC;GA3MgB;;QAoBM,wIAAA,CAAA,iBAAc;QAGjB,qIAAA,CAAA,cAAW;QAWR,wIAAA,CAAA,iBAAc;QAkHlC,8IAAA,CAAA,uBAAoB;;;KApJN", "debugId": null}}, {"offset": {"line": 3793, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useCallback } from 'react'\nimport { AdminScreen } from '@/components/screens/AdminScreen'\nimport { StoryForm } from '@/components/story/StoryForm'\nimport { StoryReaderScreen } from '@/components/screens/StoryReaderScreen'\nimport { Story } from '@/lib/types'\n\ninterface StoryFormData {\n  title: string\n  chapter?: string\n  content: string\n  language: string\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  voice?: string\n  speed: number\n  generateAudio: boolean\n}\n\nexport default function AdminPage() {\n  const [stories, setStories] = useState<Story[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [showForm, setShowForm] = useState(false)\n  const [editingStory, setEditingStory] = useState<Story | null>(null)\n  const [previewStory, setPreviewStory] = useState<Story | null>(null)\n\n  // Load stories on mount\n  useEffect(() => {\n    loadStories()\n  }, [])\n\n  const loadStories = async () => {\n    try {\n      const response = await fetch('/api/stories')\n      if (response.ok) {\n        const storiesData = await response.json()\n        setStories(storiesData)\n      } else {\n        console.error('Failed to load stories')\n      }\n    } catch (error) {\n      console.error('Error loading stories:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleCreateStory = useCallback(() => {\n    setEditingStory(null)\n    setShowForm(true)\n  }, [])\n\n  const handleEditStory = useCallback((story: Story) => {\n    setEditingStory(story)\n    setShowForm(true)\n  }, [])\n\n  const handleDeleteStory = useCallback(async (storyId: string) => {\n    if (!confirm('Are you sure you want to delete this story?')) {\n      return\n    }\n\n    try {\n      const response = await fetch(`/api/stories/${storyId}`, {\n        method: 'DELETE'\n      })\n\n      if (response.ok) {\n        setStories(prev => prev.filter(story => story.id !== storyId))\n      } else {\n        alert('Failed to delete story')\n      }\n    } catch (error) {\n      console.error('Error deleting story:', error)\n      alert('Failed to delete story')\n    }\n  }, [])\n\n  const handlePreviewStory = useCallback((story: Story) => {\n    setPreviewStory(story)\n  }, [])\n\n  const handleSaveStory = useCallback(async (data: StoryFormData) => {\n    try {\n      const response = await fetch('/api/stories', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data)\n      })\n\n      if (response.ok) {\n        const newStory = await response.json()\n        setStories(prev => [...prev, newStory])\n        setShowForm(false)\n        setEditingStory(null)\n      } else {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to save story')\n      }\n    } catch (error) {\n      console.error('Error saving story:', error)\n      throw error\n    }\n  }, [])\n\n  const handlePreviewFormStory = useCallback(async (data: StoryFormData) => {\n    try {\n      const response = await fetch('/api/stories/process', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data)\n      })\n\n      if (response.ok) {\n        const result = await response.json()\n        setPreviewStory(result.story)\n        setShowForm(false)\n      } else {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to process story')\n      }\n    } catch (error) {\n      console.error('Error processing story:', error)\n      throw error\n    }\n  }, [])\n\n  const handleCloseForm = useCallback(() => {\n    setShowForm(false)\n    setEditingStory(null)\n  }, [])\n\n  const handleClosePreview = useCallback(() => {\n    setPreviewStory(null)\n  }, [])\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading stories...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show preview mode\n  if (previewStory) {\n    return (\n      <div className=\"relative\">\n        <button\n          onClick={handleClosePreview}\n          className=\"absolute top-4 left-4 z-50 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors\"\n        >\n          ← Back to Admin\n        </button>\n        <StoryReaderScreen story={previewStory} />\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <AdminScreen\n        stories={stories}\n        onCreateStory={handleCreateStory}\n        onEditStory={handleEditStory}\n        onDeleteStory={handleDeleteStory}\n        onPreviewStory={handlePreviewStory}\n      />\n\n      <StoryForm\n        story={editingStory}\n        isOpen={showForm}\n        onClose={handleCloseForm}\n        onSave={handleSaveStory}\n        onPreview={handlePreviewFormStory}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IAE/D,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,cAAc,MAAM,SAAS,IAAI;gBACvC,WAAW;YACb,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YACpC,gBAAgB;YAChB,YAAY;QACd;mDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACnC,gBAAgB;YAChB,YAAY;QACd;iDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YAC3C,IAAI,CAAC,QAAQ,gDAAgD;gBAC3D;YACF;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,gBAAuB,OAAR,UAAW;oBACtD,QAAQ;gBACV;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf;oEAAW,CAAA,OAAQ,KAAK,MAAM;4EAAC,CAAA,QAAS,MAAM,EAAE,KAAK;;;gBACvD,OAAO;oBACL,MAAM;gBACR;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM;YACR;QACF;mDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACtC,gBAAgB;QAClB;oDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,OAAO;YACzC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;oBAC3C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,WAAW,MAAM,SAAS,IAAI;oBACpC;kEAAW,CAAA,OAAQ;mCAAI;gCAAM;6BAAS;;oBACtC,YAAY;oBACZ,gBAAgB;gBAClB,OAAO;oBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,MAAM;YACR;QACF;iDAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,OAAO;YAChD,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;oBACnD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,SAAS,MAAM,SAAS,IAAI;oBAClC,gBAAgB,OAAO,KAAK;oBAC5B,YAAY;gBACd,OAAO;oBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,MAAM;YACR;QACF;wDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAClC,YAAY;YACZ,gBAAgB;QAClB;iDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACrC,gBAAgB;QAClB;oDAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,oBAAoB;IACpB,IAAI,cAAc;QAChB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;8BAGD,6LAAC,qJAAA,CAAA,oBAAiB;oBAAC,OAAO;;;;;;;;;;;;IAGhC;IAEA,qBACE;;0BACE,6LAAC,+IAAA,CAAA,cAAW;gBACV,SAAS;gBACT,eAAe;gBACf,aAAa;gBACb,eAAe;gBACf,gBAAgB;;;;;;0BAGlB,6LAAC,2IAAA,CAAA,YAAS;gBACR,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,WAAW;;;;;;;;AAInB;GAtKwB;KAAA", "debugId": null}}]}