(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Typography": ()=>Typography
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
const Typography = {
    Title: (param)=>{
        let { children, className = '' } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
            className: "text-2xl font-bold text-gray-900 ".concat(className),
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Typography.tsx",
            lineNumber: 10,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    },
    Subtitle: (param)=>{
        let { children, className = '' } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
            className: "text-base text-gray-600 ".concat(className),
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Typography.tsx",
            lineNumber: 16,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    },
    Body: (param)=>{
        let { children, className = '' } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            className: "text-lg text-gray-800 leading-relaxed ".concat(className),
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Typography.tsx",
            lineNumber: 22,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    },
    Caption: (param)=>{
        let { children, className = '' } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "text-sm text-gray-500 ".concat(className),
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Typography.tsx",
            lineNumber: 28,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    },
    TranslationWord: (param)=>{
        let { children, className = '' } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "text-xl font-bold text-gray-900 ".concat(className),
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Typography.tsx",
            lineNumber: 34,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    },
    TranslationText: (param)=>{
        let { children, className = '' } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "text-base text-gray-700 ".concat(className),
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Typography.tsx",
            lineNumber: 40,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    },
    Micro: (param)=>{
        let { children, className = '' } = param;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
            className: "text-xs text-gray-400 ".concat(className),
            children: children
        }, void 0, false, {
            fileName: "[project]/src/components/ui/Typography.tsx",
            lineNumber: 46,
            columnNumber: 5
        }, ("TURBOPACK compile-time value", void 0));
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/IconButton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "IconButton": ()=>IconButton
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function IconButton(param) {
    let { icon, onClick, variant = 'default', size = 'md', className = '', disabled = false, 'aria-label': ariaLabel } = param;
    const baseClasses = 'inline-flex items-center justify-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    const variantClasses = {
        default: 'bg-gray-100 hover:bg-gray-200 text-gray-700',
        ghost: 'hover:bg-gray-100 text-gray-600',
        outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'
    };
    const sizeClasses = {
        sm: 'w-8 h-8 text-sm',
        md: 'w-10 h-10 text-base',
        lg: 'w-12 h-12 text-lg'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: onClick,
        disabled: disabled,
        "aria-label": ariaLabel,
        className: "".concat(baseClasses, " ").concat(variantClasses[variant], " ").concat(sizeClasses[size], " ").concat(className),
        children: icon
    }, void 0, false, {
        fileName: "[project]/src/components/ui/IconButton.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, this);
}
_c = IconButton;
var _c;
__turbopack_context__.k.register(_c, "IconButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/story/StoryHeader.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "StoryHeader": ()=>StoryHeader
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/IconButton.tsx [app-client] (ecmascript)");
;
;
;
;
function StoryHeader(param) {
    let { title, chapter, isAudioPlaying, onBookmarkClick, onMenuClick } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
        className: "flex items-center justify-between p-4 bg-white border-b border-gray-100",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Title, {
                                className: "text-xl font-bold text-gray-900",
                                children: title
                            }, void 0, false, {
                                fileName: "[project]/src/components/story/StoryHeader.tsx",
                                lineNumber: 19,
                                columnNumber: 11
                            }, this),
                            isAudioPlaying && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex space-x-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-1 h-4 bg-blue-500 rounded animate-pulse"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/story/StoryHeader.tsx",
                                        lineNumber: 24,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-1 h-4 bg-blue-500 rounded animate-pulse",
                                        style: {
                                            animationDelay: '0.2s'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/story/StoryHeader.tsx",
                                        lineNumber: 25,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-1 h-4 bg-blue-500 rounded animate-pulse",
                                        style: {
                                            animationDelay: '0.4s'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/story/StoryHeader.tsx",
                                        lineNumber: 26,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/story/StoryHeader.tsx",
                                lineNumber: 23,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/story/StoryHeader.tsx",
                        lineNumber: 18,
                        columnNumber: 9
                    }, this),
                    chapter && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Subtitle, {
                        className: "text-sm text-gray-600 mt-1",
                        children: chapter
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/StoryHeader.tsx",
                        lineNumber: 31,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/story/StoryHeader.tsx",
                lineNumber: 17,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center space-x-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BookmarkIcon"], {
                            className: "w-5 h-5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/story/StoryHeader.tsx",
                            lineNumber: 39,
                            columnNumber: 17
                        }, void 0),
                        onClick: onBookmarkClick,
                        variant: "ghost",
                        "aria-label": "Bookmark story"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/StoryHeader.tsx",
                        lineNumber: 38,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HamburgerMenuIcon"], {
                            className: "w-5 h-5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/story/StoryHeader.tsx",
                            lineNumber: 45,
                            columnNumber: 17
                        }, void 0),
                        onClick: onMenuClick,
                        variant: "ghost",
                        "aria-label": "Open menu"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/StoryHeader.tsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/story/StoryHeader.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/story/StoryHeader.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, this);
}
_c = StoryHeader;
var _c;
__turbopack_context__.k.register(_c, "StoryHeader");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/story/WordHighlighter.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "WordHighlighter": ()=>WordHighlighter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function WordHighlighter(param) {
    let { text, currentWordIndex, onWordClick } = param;
    const words = text.split(/(\s+)/);
    let wordIndex = 0;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "text-lg leading-relaxed text-gray-800 select-none",
        children: words.map((segment, segmentIndex)=>{
            // Skip whitespace segments
            if (/^\s+$/.test(segment)) {
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    children: segment
                }, segmentIndex, false, {
                    fileName: "[project]/src/components/story/WordHighlighter.tsx",
                    lineNumber: 18,
                    columnNumber: 18
                }, this);
            }
            const isCurrentWord = wordIndex === currentWordIndex;
            const currentIndex = wordIndex;
            wordIndex++;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "cursor-pointer transition-all duration-300 hover:bg-blue-100 px-1 py-0.5 rounded ".concat(isCurrentWord ? 'bg-blue-300 text-blue-900 font-semibold shadow-sm scale-105' : 'hover:scale-102'),
                onClick: (event)=>onWordClick(segment, currentIndex, event),
                children: segment
            }, segmentIndex, false, {
                fileName: "[project]/src/components/story/WordHighlighter.tsx",
                lineNumber: 26,
                columnNumber: 11
            }, this);
        })
    }, void 0, false, {
        fileName: "[project]/src/components/story/WordHighlighter.tsx",
        lineNumber: 14,
        columnNumber: 5
    }, this);
}
_c = WordHighlighter;
var _c;
__turbopack_context__.k.register(_c, "WordHighlighter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/story/TranslationCard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "TranslationCard": ()=>TranslationCard
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/IconButton.tsx [app-client] (ecmascript)");
;
;
;
;
function TranslationCard(param) {
    let { word, translation, isLoading = false, onPlayAudio, onClose } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-gray-50 rounded-xl p-4 mx-5 mb-6 relative shadow-sm border border-gray-200",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cross2Icon"], {
                    className: "w-4 h-4"
                }, void 0, false, {
                    fileName: "[project]/src/components/story/TranslationCard.tsx",
                    lineNumber: 25,
                    columnNumber: 15
                }, void 0),
                onClick: onClose,
                variant: "ghost",
                size: "sm",
                className: "absolute top-2 right-2",
                "aria-label": "Close translation"
            }, void 0, false, {
                fileName: "[project]/src/components/story/TranslationCard.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between mb-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].TranslationWord, {
                                className: "text-xl",
                                children: word
                            }, void 0, false, {
                                fileName: "[project]/src/components/story/TranslationCard.tsx",
                                lineNumber: 35,
                                columnNumber: 11
                            }, this),
                            (translation === null || translation === void 0 ? void 0 : translation.pronunciation) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                className: "block mt-1",
                                children: [
                                    "/",
                                    translation.pronunciation,
                                    "/"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/story/TranslationCard.tsx",
                                lineNumber: 39,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SpeakerLoudIcon"], {
                            className: "w-4 h-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/story/TranslationCard.tsx",
                            lineNumber: 45,
                            columnNumber: 17
                        }, void 0),
                        onClick: onPlayAudio,
                        variant: "ghost",
                        size: "sm",
                        "aria-label": "Play pronunciation"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/story/TranslationCard.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-4 bg-gray-200 rounded w-3/4 mb-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 55,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-3 bg-gray-200 rounded w-1/2 mb-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 56,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-3 bg-gray-200 rounded w-2/3"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 57,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                        className: "text-gray-400 mt-2",
                        children: "Translating..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 58,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/story/TranslationCard.tsx",
                lineNumber: 54,
                columnNumber: 9
            }, this) : translation ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].TranslationText, {
                        className: "block mb-3",
                        children: translation.translation
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 64,
                        columnNumber: 11
                    }, this),
                    translation.example && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-sm text-gray-600 border-t border-gray-200 pt-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                className: "italic block mb-1",
                                children: translation.example.original
                            }, void 0, false, {
                                fileName: "[project]/src/components/story/TranslationCard.tsx",
                                lineNumber: 70,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                className: "block",
                                children: translation.example.translation
                            }, void 0, false, {
                                fileName: "[project]/src/components/story/TranslationCard.tsx",
                                lineNumber: 73,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/story/TranslationCard.tsx",
                        lineNumber: 69,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                className: "text-gray-500",
                children: "Translation not available"
            }, void 0, false, {
                fileName: "[project]/src/components/story/TranslationCard.tsx",
                lineNumber: 80,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/story/TranslationCard.tsx",
        lineNumber: 23,
        columnNumber: 5
    }, this);
}
_c = TranslationCard;
var _c;
__turbopack_context__.k.register(_c, "TranslationCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/story/ReadingArea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReadingArea": ()=>ReadingArea
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$WordHighlighter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/story/WordHighlighter.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$TranslationCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/story/TranslationCard.tsx [app-client] (ecmascript)");
;
;
;
function ReadingArea(param) {
    let { content, currentWordIndex, translationCard, onWordClick, onTranslationClose, onTranslationPlayAudio } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex-1 overflow-y-auto",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-sm mx-auto p-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$WordHighlighter$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WordHighlighter"], {
                        text: content,
                        currentWordIndex: currentWordIndex,
                        onWordClick: onWordClick
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/ReadingArea.tsx",
                        lineNumber: 27,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/story/ReadingArea.tsx",
                    lineNumber: 26,
                    columnNumber: 9
                }, this),
                translationCard.isVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$TranslationCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TranslationCard"], {
                    word: translationCard.word,
                    translation: translationCard.translation,
                    isLoading: translationCard.isLoading,
                    onPlayAudio: onTranslationPlayAudio,
                    onClose: onTranslationClose
                }, void 0, false, {
                    fileName: "[project]/src/components/story/ReadingArea.tsx",
                    lineNumber: 35,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/story/ReadingArea.tsx",
            lineNumber: 25,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/story/ReadingArea.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_c = ReadingArea;
var _c;
__turbopack_context__.k.register(_c, "ReadingArea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/Slider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Slider": ()=>Slider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slider/dist/index.mjs [app-client] (ecmascript)");
;
;
function Slider(param) {
    let { value, onValueChange, max = 100, min = 0, step = 1, className = '', disabled = false } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Root"], {
        className: "relative flex items-center select-none touch-none w-full h-5 ".concat(className),
        value: value,
        onValueChange: onValueChange,
        max: max,
        min: min,
        step: step,
        disabled: disabled,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Track"], {
                className: "bg-gray-200 relative grow rounded-full h-1",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"], {
                    className: "absolute bg-gray-900 rounded-full h-full"
                }, void 0, false, {
                    fileName: "[project]/src/components/ui/Slider.tsx",
                    lineNumber: 34,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Slider.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slider$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Thumb"], {
                className: "block w-4 h-4 bg-white border-2 border-gray-900 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50",
                "aria-label": "Volume"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/Slider.tsx",
                lineNumber: 36,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/Slider.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_c = Slider;
var _c;
__turbopack_context__.k.register(_c, "Slider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/story/AudioPlayer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AudioPlayer": ()=>AudioPlayer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/IconButton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Slider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Slider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)");
;
;
;
;
;
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return "".concat(minutes, ":").concat(remainingSeconds.toString().padStart(2, '0'));
}
function AudioPlayer(param) {
    let { state, onPlayPause, onSeek, onVolumeChange } = param;
    const { isPlaying, currentTime, duration, volume } = state;
    const handleProgressChange = (value)=>{
        onSeek(value[0]);
    };
    const handleVolumeChange = (value)=>{
        onVolumeChange(value[0] / 100);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white border-t border-gray-200 p-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Slider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slider"], {
                        value: [
                            currentTime
                        ],
                        onValueChange: handleProgressChange,
                        max: duration,
                        min: 0,
                        step: 0.1,
                        className: "w-full"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/AudioPlayer.tsx",
                        lineNumber: 36,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between mt-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                children: formatTime(currentTime)
                            }, void 0, false, {
                                fileName: "[project]/src/components/story/AudioPlayer.tsx",
                                lineNumber: 45,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                children: formatTime(duration)
                            }, void 0, false, {
                                fileName: "[project]/src/components/story/AudioPlayer.tsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/story/AudioPlayer.tsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/story/AudioPlayer.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                    icon: isPlaying ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PauseIcon"], {
                        className: "w-6 h-6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/AudioPlayer.tsx",
                        lineNumber: 57,
                        columnNumber: 29
                    }, void 0) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PlayIcon"], {
                        className: "w-6 h-6"
                    }, void 0, false, {
                        fileName: "[project]/src/components/story/AudioPlayer.tsx",
                        lineNumber: 57,
                        columnNumber: 65
                    }, void 0),
                    onClick: onPlayPause,
                    size: "lg",
                    className: "bg-gray-900 text-white hover:bg-gray-800",
                    "aria-label": isPlaying ? 'Pause' : 'Play'
                }, void 0, false, {
                    fileName: "[project]/src/components/story/AudioPlayer.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/story/AudioPlayer.tsx",
                lineNumber: 55,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/story/AudioPlayer.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
_c = AudioPlayer;
var _c;
__turbopack_context__.k.register(_c, "AudioPlayer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/story/PageNavigation.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageNavigation": ()=>PageNavigation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/IconButton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)");
;
;
;
;
function PageNavigation(param) {
    let { currentPage, totalPages, onPreviousPage, onNextPage } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center justify-between p-4 bg-white border-t border-gray-100",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChevronLeftIcon"], {
                    className: "w-5 h-5"
                }, void 0, false, {
                    fileName: "[project]/src/components/story/PageNavigation.tsx",
                    lineNumber: 22,
                    columnNumber: 15
                }, void 0),
                onClick: onPreviousPage,
                variant: "ghost",
                disabled: currentPage <= 1,
                "aria-label": "Previous page"
            }, void 0, false, {
                fileName: "[project]/src/components/story/PageNavigation.tsx",
                lineNumber: 21,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                className: "text-gray-500",
                children: [
                    "Page ",
                    currentPage,
                    " of ",
                    totalPages
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/story/PageNavigation.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChevronRightIcon"], {
                    className: "w-5 h-5"
                }, void 0, false, {
                    fileName: "[project]/src/components/story/PageNavigation.tsx",
                    lineNumber: 34,
                    columnNumber: 15
                }, void 0),
                onClick: onNextPage,
                variant: "ghost",
                disabled: currentPage >= totalPages,
                "aria-label": "Next page"
            }, void 0, false, {
                fileName: "[project]/src/components/story/PageNavigation.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/story/PageNavigation.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
_c = PageNavigation;
var _c;
__turbopack_context__.k.register(_c, "PageNavigation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/ErrorBoundary.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ErrorBoundary": ()=>ErrorBoundary
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)");
'use client';
;
;
;
;
class ErrorBoundary extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Component {
    static getDerivedStateFromError(error) {
        return {
            hasError: true,
            error
        };
    }
    componentDidCatch(error, errorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo);
    }
    render() {
        if (this.state.hasError) {
            const FallbackComponent = this.props.fallback || DefaultErrorFallback;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(FallbackComponent, {
                error: this.state.error,
                resetError: this.resetError
            }, void 0, false, {
                fileName: "[project]/src/components/ui/ErrorBoundary.tsx",
                lineNumber: 37,
                columnNumber: 14
            }, this);
        }
        return this.props.children;
    }
    constructor(props){
        super(props), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "resetError", ()=>{
            this.setState({
                hasError: false,
                error: undefined
            });
        });
        this.state = {
            hasError: false
        };
    }
}
function DefaultErrorFallback(param) {
    let { error, resetError } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col items-center justify-center min-h-[200px] p-6 bg-red-50 rounded-lg border border-red-200",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Title, {
                className: "text-red-800 mb-2",
                children: "Something went wrong"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/ErrorBoundary.tsx",
                lineNumber: 47,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Body, {
                className: "text-red-600 text-center mb-4",
                children: (error === null || error === void 0 ? void 0 : error.message) || 'An unexpected error occurred'
            }, void 0, false, {
                fileName: "[project]/src/components/ui/ErrorBoundary.tsx",
                lineNumber: 50,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: resetError,
                className: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors",
                children: "Try again"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/ErrorBoundary.tsx",
                lineNumber: 53,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/ErrorBoundary.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
}
_c = DefaultErrorFallback;
var _c;
__turbopack_context__.k.register(_c, "DefaultErrorFallback");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/KeyboardShortcutsHelp.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "KeyboardShortcutsHelp": ()=>KeyboardShortcutsHelp
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/IconButton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function KeyboardShortcutsHelp() {
    _s();
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const shortcuts = [
        {
            key: 'Space',
            description: 'Play/Pause audio'
        },
        {
            key: 'Shift + →',
            description: 'Skip forward 10s'
        },
        {
            key: 'Shift + ←',
            description: 'Skip backward 10s'
        },
        {
            key: 'Esc',
            description: 'Close translation'
        }
    ];
    const handleClose = ()=>setIsVisible(false);
    if (!isVisible) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
            onClick: ()=>setIsVisible(true),
            className: "bg-white border border-gray-200 rounded-lg p-2 shadow-sm hover:shadow-md transition-all duration-200 hover:bg-gray-50",
            "aria-label": "Show keyboard shortcuts",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KeyboardIcon"], {
                className: "w-4 h-4 text-gray-600"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                lineNumber: 25,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
            lineNumber: 20,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 z-40",
                onClick: handleClose
            }, void 0, false, {
                fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-50 bg-white rounded-lg shadow-lg border border-gray-200 max-w-xs",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between p-3 border-b border-gray-100",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KeyboardIcon"], {
                                        className: "w-4 h-4 text-gray-600"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                        lineNumber: 42,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                        className: "font-semibold text-gray-700",
                                        children: "Shortcuts"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                        lineNumber: 43,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                lineNumber: 41,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                                icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Cross2Icon"], {
                                    className: "w-3 h-3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                    lineNumber: 48,
                                    columnNumber: 19
                                }, void 0),
                                onClick: handleClose,
                                variant: "ghost",
                                size: "sm",
                                "aria-label": "Close shortcuts help"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                lineNumber: 47,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                        lineNumber: 40,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-3 space-y-2",
                        children: shortcuts.map((shortcut, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                        className: "font-mono bg-gray-100 px-2 py-1 rounded text-gray-700 min-w-[4rem] text-center",
                                        children: shortcut.key
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                        lineNumber: 59,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                        className: "text-gray-600 ml-3 flex-1",
                                        children: shortcut.description
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                        lineNumber: 62,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                                lineNumber: 58,
                                columnNumber: 11
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                        lineNumber: 56,
                        columnNumber: 7
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/KeyboardShortcutsHelp.tsx",
                lineNumber: 39,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(KeyboardShortcutsHelp, "QjDZesRvLCmcrZLxgN677nXnVLA=");
_c = KeyboardShortcutsHelp;
var _c;
__turbopack_context__.k.register(_c, "KeyboardShortcutsHelp");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/ProgressDisplay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProgressDisplay": ()=>ProgressDisplay
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-icons/dist/react-icons.esm.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/Typography.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/IconButton.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ProgressDisplay(param) {
    let { storyProgress, overallStats, className = '' } = param;
    _s();
    const [isExpanded, setIsExpanded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const formatTime = (seconds)=>{
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor(seconds % 3600 / 60);
        if (hours > 0) {
            return "".concat(hours, "h ").concat(minutes, "m");
        }
        return "".concat(minutes, "m");
    };
    const formatPercentage = (percentage)=>{
        return "".concat(Math.round(percentage), "%");
    };
    // Collapsed view - just show essential info
    if (!isExpanded) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 ".concat(className),
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setIsExpanded(true),
                className: "w-full p-3 flex items-center justify-between hover:bg-gray-50 transition-colors rounded-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BarChartIcon"], {
                                className: "w-4 h-4 text-blue-600"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 39,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                className: "font-medium text-gray-700",
                                children: "Progress"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 40,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                        lineNumber: 38,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-3",
                        children: [
                            storyProgress && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-8 bg-gray-200 rounded-full h-1.5",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-blue-600 h-1.5 rounded-full transition-all duration-300",
                                            style: {
                                                width: "".concat(storyProgress.completionPercentage, "%")
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                            lineNumber: 49,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 48,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                        className: "text-gray-500 min-w-[2rem]",
                                        children: formatPercentage(storyProgress.completionPercentage)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 54,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 47,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChevronUpIcon"], {
                                className: "w-4 h-4 text-gray-400"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 59,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                        lineNumber: 45,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                lineNumber: 34,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
            lineNumber: 33,
            columnNumber: 7
        }, this);
    }
    // Expanded view - full details
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white rounded-lg border border-gray-200 shadow-lg ".concat(className),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between p-3 border-b border-gray-100",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BarChartIcon"], {
                                className: "w-4 h-4 text-blue-600"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Body, {
                                className: "font-semibold text-gray-800",
                                children: "Learning Progress"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$IconButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["IconButton"], {
                        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$icons$2f$dist$2f$react$2d$icons$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ChevronDownIcon"], {
                            className: "w-4 h-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                            lineNumber: 77,
                            columnNumber: 17
                        }, void 0),
                        onClick: ()=>setIsExpanded(false),
                        variant: "ghost",
                        size: "sm",
                        "aria-label": "Collapse progress"
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                lineNumber: 69,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-3",
                children: [
                    storyProgress && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-4 pb-4 border-b border-gray-100",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                className: "text-gray-600 mb-2",
                                children: "Current Story"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 90,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-between items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                className: "text-gray-500",
                                                children: "Completion"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 96,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                className: "font-medium text-blue-600",
                                                children: formatPercentage(storyProgress.completionPercentage)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 99,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 95,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-full bg-gray-200 rounded-full h-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-blue-600 h-2 rounded-full transition-all duration-300",
                                            style: {
                                                width: "".concat(storyProgress.completionPercentage, "%")
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                            lineNumber: 105,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 104,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-2 gap-4 mt-3",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                        className: "text-gray-500",
                                                        children: "Words Learned"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                        lineNumber: 113,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                                        className: "font-medium text-green-600",
                                                        children: storyProgress.wordsLearned.length
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                        lineNumber: 116,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 112,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                        className: "text-gray-500",
                                                        children: "Time Spent"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                        lineNumber: 122,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                                        className: "font-medium text-purple-600",
                                                        children: formatTime(storyProgress.timeSpent)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                        lineNumber: 125,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 121,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 111,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this),
                    overallStats && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                className: "text-gray-600 mb-2",
                                children: "Overall Stats"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 gap-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center p-2 bg-blue-50 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Body, {
                                                className: "font-bold text-blue-600",
                                                children: overallStats.totalStoriesStarted
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 143,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                className: "text-blue-600",
                                                children: "Stories Started"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 146,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 142,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center p-2 bg-green-50 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Body, {
                                                className: "font-bold text-green-600",
                                                children: overallStats.totalStoriesCompleted
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 152,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                className: "text-green-600",
                                                children: "Completed"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 155,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 151,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center p-2 bg-purple-50 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Body, {
                                                className: "font-bold text-purple-600",
                                                children: overallStats.totalWordsLearned
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 161,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                className: "text-purple-600",
                                                children: "Words Learned"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 164,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 160,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-center p-2 bg-orange-50 rounded-lg",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Body, {
                                                className: "font-bold text-orange-600",
                                                children: formatTime(overallStats.totalTimeSpent)
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 170,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Micro, {
                                                className: "text-orange-600",
                                                children: "Total Time"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                                lineNumber: 173,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                        lineNumber: 169,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 141,
                                columnNumber: 11
                            }, this),
                            overallStats.streakDays > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-3 text-center p-2 bg-yellow-50 rounded-lg",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                                    className: "font-medium text-yellow-700",
                                    children: [
                                        "🔥 ",
                                        overallStats.streakDays,
                                        " day streak!"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                    lineNumber: 181,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                                lineNumber: 180,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                        lineNumber: 136,
                        columnNumber: 9
                    }, this),
                    !storyProgress && !overallStats && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-center py-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$Typography$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Typography"].Caption, {
                            className: "text-gray-500",
                            children: "Start reading to track your progress"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                            lineNumber: 191,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                        lineNumber: 190,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/ProgressDisplay.tsx",
        lineNumber: 68,
        columnNumber: 5
    }, this);
}
_s(ProgressDisplay, "FPNvbbHVlWWR4LKxxNntSxiIS38=");
_c = ProgressDisplay;
var _c;
__turbopack_context__.k.register(_c, "ProgressDisplay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/hooks/useAudioPlayer.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAudioPlayer": ()=>useAudioPlayer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function useAudioPlayer(param) {
    let { audioUrl, wordTimings = [], onWordHighlight } = param;
    _s();
    const audioRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isPlaying: false,
        currentTime: 0,
        duration: 0,
        volume: 1.0
    });
    // Initialize audio element
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAudioPlayer.useEffect": ()=>{
            if (!audioUrl) return;
            const audio = new Audio(audioUrl);
            audioRef.current = audio;
            const handleLoadedMetadata = {
                "useAudioPlayer.useEffect.handleLoadedMetadata": ()=>{
                    setState({
                        "useAudioPlayer.useEffect.handleLoadedMetadata": (prev)=>({
                                ...prev,
                                duration: audio.duration || 0
                            })
                    }["useAudioPlayer.useEffect.handleLoadedMetadata"]);
                }
            }["useAudioPlayer.useEffect.handleLoadedMetadata"];
            const handleTimeUpdate = {
                "useAudioPlayer.useEffect.handleTimeUpdate": ()=>{
                    setState({
                        "useAudioPlayer.useEffect.handleTimeUpdate": (prev)=>({
                                ...prev,
                                currentTime: audio.currentTime
                            })
                    }["useAudioPlayer.useEffect.handleTimeUpdate"]);
                    // Find current word based on time
                    if (wordTimings.length > 0 && onWordHighlight) {
                        const currentWordIndex = wordTimings.findIndex({
                            "useAudioPlayer.useEffect.handleTimeUpdate.currentWordIndex": (timing)=>audio.currentTime >= timing.startTime && audio.currentTime <= timing.endTime
                        }["useAudioPlayer.useEffect.handleTimeUpdate.currentWordIndex"]);
                        if (currentWordIndex !== -1) {
                            onWordHighlight(currentWordIndex);
                        }
                    }
                }
            }["useAudioPlayer.useEffect.handleTimeUpdate"];
            const handleEnded = {
                "useAudioPlayer.useEffect.handleEnded": ()=>{
                    setState({
                        "useAudioPlayer.useEffect.handleEnded": (prev)=>({
                                ...prev,
                                isPlaying: false,
                                currentTime: 0
                            })
                    }["useAudioPlayer.useEffect.handleEnded"]);
                }
            }["useAudioPlayer.useEffect.handleEnded"];
            const handleError = {
                "useAudioPlayer.useEffect.handleError": (e)=>{
                    console.error('Audio error:', e);
                    setState({
                        "useAudioPlayer.useEffect.handleError": (prev)=>({
                                ...prev,
                                isPlaying: false
                            })
                    }["useAudioPlayer.useEffect.handleError"]);
                }
            }["useAudioPlayer.useEffect.handleError"];
            audio.addEventListener('loadedmetadata', handleLoadedMetadata);
            audio.addEventListener('timeupdate', handleTimeUpdate);
            audio.addEventListener('ended', handleEnded);
            audio.addEventListener('error', handleError);
            return ({
                "useAudioPlayer.useEffect": ()=>{
                    audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
                    audio.removeEventListener('timeupdate', handleTimeUpdate);
                    audio.removeEventListener('ended', handleEnded);
                    audio.removeEventListener('error', handleError);
                    audio.pause();
                }
            })["useAudioPlayer.useEffect"];
        }
    }["useAudioPlayer.useEffect"], [
        audioUrl,
        wordTimings,
        onWordHighlight
    ]);
    const play = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioPlayer.useCallback[play]": ()=>{
            if (audioRef.current) {
                audioRef.current.play();
                setState({
                    "useAudioPlayer.useCallback[play]": (prev)=>({
                            ...prev,
                            isPlaying: true
                        })
                }["useAudioPlayer.useCallback[play]"]);
            }
        }
    }["useAudioPlayer.useCallback[play]"], []);
    const pause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioPlayer.useCallback[pause]": ()=>{
            if (audioRef.current) {
                audioRef.current.pause();
                setState({
                    "useAudioPlayer.useCallback[pause]": (prev)=>({
                            ...prev,
                            isPlaying: false
                        })
                }["useAudioPlayer.useCallback[pause]"]);
            }
        }
    }["useAudioPlayer.useCallback[pause]"], []);
    const togglePlayPause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioPlayer.useCallback[togglePlayPause]": ()=>{
            if (state.isPlaying) {
                pause();
            } else {
                play();
            }
        }
    }["useAudioPlayer.useCallback[togglePlayPause]"], [
        state.isPlaying,
        play,
        pause
    ]);
    const seek = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioPlayer.useCallback[seek]": (time)=>{
            if (audioRef.current) {
                audioRef.current.currentTime = time;
                setState({
                    "useAudioPlayer.useCallback[seek]": (prev)=>({
                            ...prev,
                            currentTime: time
                        })
                }["useAudioPlayer.useCallback[seek]"]);
            }
        }
    }["useAudioPlayer.useCallback[seek]"], []);
    const setVolume = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useAudioPlayer.useCallback[setVolume]": (volume)=>{
            if (audioRef.current) {
                audioRef.current.volume = volume;
                setState({
                    "useAudioPlayer.useCallback[setVolume]": (prev)=>({
                            ...prev,
                            volume
                        })
                }["useAudioPlayer.useCallback[setVolume]"]);
            }
        }
    }["useAudioPlayer.useCallback[setVolume]"], []);
    return {
        state,
        play,
        pause,
        togglePlayPause,
        seek,
        setVolume,
        isReady: !!audioRef.current && state.duration > 0
    };
}
_s(useAudioPlayer, "HRGSdqu6T915SQoJ6/5/suq8o1M=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/hooks/useTranslation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useTranslation": ()=>useTranslation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
// Mock translation service - in a real app this would call an API
const mockTranslationService = {
    async translateWord (word, fromLang, toLang) {
        // Simulate API delay
        await new Promise((resolve)=>setTimeout(resolve, 800));
        // Enhanced mock translations with more examples
        const mockTranslations = {
            'plane': {
                translation: 'avion',
                pronunciation: 'a-vi-ɔ̃',
                provider: 'mock-translate',
                example: {
                    original: 'I like riding on planes.',
                    translation: 'J\'aime voyager en avion.'
                }
            },
            'aircraft': {
                translation: 'aéronef',
                pronunciation: 'a-e-ʁo-nɛf',
                provider: 'mock-translate',
                example: {
                    original: 'The aircraft landed safely.',
                    translation: 'L\'aéronef a atterri en sécurité.'
                }
            },
            'huge': {
                translation: 'énorme',
                pronunciation: 'e-nɔʁm',
                provider: 'mock-translate',
                example: {
                    original: 'That\'s a huge building.',
                    translation: 'C\'est un énorme bâtiment.'
                }
            },
            'cloud': {
                translation: 'nuage',
                pronunciation: 'ny-aʒ',
                provider: 'mock-translate',
                example: {
                    original: 'The cloud is white.',
                    translation: 'Le nuage est blanc.'
                }
            },
            'airport': {
                translation: 'aéroport',
                pronunciation: 'a-e-ʁo-pɔʁ',
                provider: 'mock-translate',
                example: {
                    original: 'We arrived at the airport.',
                    translation: 'Nous sommes arrivés à l\'aéroport.'
                }
            },
            'seat': {
                translation: 'siège',
                pronunciation: 'sjɛʒ',
                provider: 'mock-translate',
                example: {
                    original: 'Please take a seat.',
                    translation: 'Veuillez prendre un siège.'
                }
            },
            'time': {
                translation: 'temps',
                pronunciation: 'tɑ̃',
                provider: 'mock-translate',
                example: {
                    original: 'What time is it?',
                    translation: 'Quelle heure est-il?'
                }
            },
            'world': {
                translation: 'monde',
                pronunciation: 'mɔ̃d',
                provider: 'mock-translate',
                example: {
                    original: 'The world is beautiful.',
                    translation: 'Le monde est beau.'
                }
            }
        };
        const cleanWord = word.toLowerCase().replace(/[.,!?;:]/, '');
        const translation = mockTranslations[cleanWord];
        if (translation) {
            return translation;
        }
        // Fallback for unknown words
        return {
            translation: '[Translation for "'.concat(word, '" not available]'),
            provider: 'mock-translate',
            confidence: 0.5
        };
    }
};
function useTranslation() {
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        isLoading: false,
        error: null,
        lastTranslation: null
    });
    const translateWord = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTranslation.useCallback[translateWord]": async function(word) {
            let fromLang = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'en', toLang = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'fr';
            setState({
                "useTranslation.useCallback[translateWord]": (prev)=>({
                        ...prev,
                        isLoading: true,
                        error: null
                    })
            }["useTranslation.useCallback[translateWord]"]);
            try {
                const result = await mockTranslationService.translateWord(word, fromLang, toLang);
                setState({
                    "useTranslation.useCallback[translateWord]": (prev)=>({
                            ...prev,
                            isLoading: false,
                            lastTranslation: result
                        })
                }["useTranslation.useCallback[translateWord]"]);
                return result;
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Translation failed';
                setState({
                    "useTranslation.useCallback[translateWord]": (prev)=>({
                            ...prev,
                            isLoading: false,
                            error: errorMessage
                        })
                }["useTranslation.useCallback[translateWord]"]);
                return null;
            }
        }
    }["useTranslation.useCallback[translateWord]"], []);
    const clearError = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useTranslation.useCallback[clearError]": ()=>{
            setState({
                "useTranslation.useCallback[clearError]": (prev)=>({
                        ...prev,
                        error: null
                    })
            }["useTranslation.useCallback[clearError]"]);
        }
    }["useTranslation.useCallback[clearError]"], []);
    return {
        ...state,
        translateWord,
        clearError
    };
}
_s(useTranslation, "d9XP0mK2MT2Wweq/wP8zIEzAMzk=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/hooks/useKeyboardShortcuts.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useKeyboardShortcuts": ()=>useKeyboardShortcuts
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function useKeyboardShortcuts(param) {
    let { onPlayPause, onSeekForward, onSeekBackward, onCloseTranslation } = param;
    _s();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useKeyboardShortcuts.useEffect": ()=>{
            const handleKeyDown = {
                "useKeyboardShortcuts.useEffect.handleKeyDown": (event)=>{
                    // Don't trigger shortcuts if user is typing in an input
                    if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
                        return;
                    }
                    switch(event.code){
                        case 'Space':
                            event.preventDefault();
                            onPlayPause === null || onPlayPause === void 0 ? void 0 : onPlayPause();
                            break;
                        case 'ArrowRight':
                            if (event.shiftKey) {
                                event.preventDefault();
                                onSeekForward === null || onSeekForward === void 0 ? void 0 : onSeekForward();
                            }
                            break;
                        case 'ArrowLeft':
                            if (event.shiftKey) {
                                event.preventDefault();
                                onSeekBackward === null || onSeekBackward === void 0 ? void 0 : onSeekBackward();
                            }
                            break;
                        case 'Escape':
                            event.preventDefault();
                            onCloseTranslation === null || onCloseTranslation === void 0 ? void 0 : onCloseTranslation();
                            break;
                    }
                }
            }["useKeyboardShortcuts.useEffect.handleKeyDown"];
            document.addEventListener('keydown', handleKeyDown);
            return ({
                "useKeyboardShortcuts.useEffect": ()=>document.removeEventListener('keydown', handleKeyDown)
            })["useKeyboardShortcuts.useEffect"];
        }
    }["useKeyboardShortcuts.useEffect"], [
        onPlayPause,
        onSeekForward,
        onSeekBackward,
        onCloseTranslation
    ]);
}
_s(useKeyboardShortcuts, "OD7bBpZva5O2jO+Puf00hKivP7c=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/services/progress-service.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// User Progress Tracking Service
// This service handles user progress, statistics, and learning analytics
__turbopack_context__.s({
    "progressService": ()=>progressService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class ProgressService {
    // Get user's overall progress statistics
    getProgressStats() {
        const allProgress = this.getAllProgress();
        const now = new Date();
        const today = now.toDateString();
        const totalStoriesStarted = allProgress.length;
        const totalStoriesCompleted = allProgress.filter((p)=>p.completedAt).length;
        const totalWordsLearned = [
            ...new Set(allProgress.flatMap((p)=>p.wordsLearned))
        ].length;
        const totalTimeSpent = allProgress.reduce((sum, p)=>sum + p.timeSpent, 0);
        const averageCompletionRate = totalStoriesStarted > 0 ? allProgress.reduce((sum, p)=>sum + p.completionPercentage, 0) / totalStoriesStarted : 0;
        // Calculate streak (simplified - just check if user was active today)
        const lastActiveDate = allProgress.length > 0 ? allProgress.reduce((latest, p)=>new Date(p.lastAccessedAt) > new Date(latest) ? p.lastAccessedAt : latest, allProgress[0].lastAccessedAt) : today;
        const streakDays = new Date(lastActiveDate).toDateString() === today ? 1 : 0;
        return {
            totalStoriesStarted,
            totalStoriesCompleted,
            totalWordsLearned,
            totalTimeSpent,
            averageCompletionRate,
            streakDays,
            lastActiveDate
        };
    }
    // Get progress for a specific story
    getStoryProgress(storyId) {
        const allProgress = this.getAllProgress();
        return allProgress.find((p)=>p.storyId === storyId) || null;
    }
    // Start a new learning session
    startSession(storyId) {
        const sessionId = "session-".concat(Date.now(), "-").concat(Math.random().toString(36).substr(2, 9));
        const session = {
            sessionId,
            storyId,
            startTime: new Date().toISOString(),
            wordsEncountered: [],
            translationsRequested: [],
            audioPlayTime: 0,
            completionPercentage: 0
        };
        localStorage.setItem(this.sessionKey, JSON.stringify(session));
        return session;
    }
    // Update current session
    updateSession(updates) {
        const currentSession = this.getCurrentSession();
        if (!currentSession) return;
        const updatedSession = {
            ...currentSession,
            ...updates
        };
        localStorage.setItem(this.sessionKey, JSON.stringify(updatedSession));
    }
    // End current session and update progress
    endSession() {
        const session = this.getCurrentSession();
        if (!session) return;
        const endTime = new Date().toISOString();
        const sessionDuration = new Date(endTime).getTime() - new Date(session.startTime).getTime();
        const sessionDurationSeconds = Math.floor(sessionDuration / 1000);
        // Update or create progress record
        let progress = this.getStoryProgress(session.storyId);
        if (!progress) {
            progress = {
                userId: 'default-user',
                storyId: session.storyId,
                wordsLearned: [],
                completionPercentage: 0,
                timeSpent: 0,
                lastAccessedAt: endTime,
                startedAt: session.startTime,
                statistics: {
                    totalWords: 0,
                    uniqueWordsEncountered: 0,
                    translationsRequested: 0,
                    audioPlayTime: 0,
                    sessionsCount: 0
                }
            };
        }
        // Update progress with session data
        const newWordsLearned = session.translationsRequested.filter((word)=>!progress.wordsLearned.includes(word));
        progress.wordsLearned = [
            ...progress.wordsLearned,
            ...newWordsLearned
        ];
        progress.completionPercentage = Math.max(progress.completionPercentage, session.completionPercentage);
        progress.timeSpent += sessionDurationSeconds;
        progress.lastAccessedAt = endTime;
        progress.statistics.uniqueWordsEncountered = [
            ...new Set([
                ...session.wordsEncountered,
                ...progress.wordsLearned
            ])
        ].length;
        progress.statistics.translationsRequested += session.translationsRequested.length;
        progress.statistics.audioPlayTime += session.audioPlayTime;
        progress.statistics.sessionsCount += 1;
        // Mark as completed if 90% or more
        if (progress.completionPercentage >= 90 && !progress.completedAt) {
            progress.completedAt = endTime;
        }
        this.saveStoryProgress(progress);
        localStorage.removeItem(this.sessionKey);
    }
    // Track word encounter
    trackWordEncounter(word) {
        const session = this.getCurrentSession();
        if (!session) return;
        if (!session.wordsEncountered.includes(word)) {
            session.wordsEncountered.push(word);
            this.updateSession({
                wordsEncountered: session.wordsEncountered
            });
        }
    }
    // Track translation request
    trackTranslationRequest(word) {
        const session = this.getCurrentSession();
        if (!session) return;
        if (!session.translationsRequested.includes(word)) {
            session.translationsRequested.push(word);
            this.updateSession({
                translationsRequested: session.translationsRequested
            });
        }
    }
    // Track audio play time
    trackAudioPlayTime(seconds) {
        const session = this.getCurrentSession();
        if (!session) return;
        this.updateSession({
            audioPlayTime: session.audioPlayTime + seconds
        });
    }
    // Update completion percentage
    updateCompletionPercentage(percentage) {
        const session = this.getCurrentSession();
        if (!session) return;
        this.updateSession({
            completionPercentage: Math.max(session.completionPercentage, percentage)
        });
    }
    // Private helper methods
    getCurrentSession() {
        const sessionData = localStorage.getItem(this.sessionKey);
        return sessionData ? JSON.parse(sessionData) : null;
    }
    getAllProgress() {
        const progressData = localStorage.getItem(this.storageKey);
        return progressData ? JSON.parse(progressData) : [];
    }
    saveStoryProgress(progress) {
        const allProgress = this.getAllProgress();
        const existingIndex = allProgress.findIndex((p)=>p.storyId === progress.storyId);
        if (existingIndex >= 0) {
            allProgress[existingIndex] = progress;
        } else {
            allProgress.push(progress);
        }
        localStorage.setItem(this.storageKey, JSON.stringify(allProgress));
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "storageKey", 'polistory-progress');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "sessionKey", 'polistory-current-session');
    }
}
const progressService = new ProgressService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/hooks/useProgress.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useProgress": ()=>useProgress
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/progress-service.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function useProgress(storyId) {
    _s();
    const [storyProgress, setStoryProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [overallStats, setOverallStats] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Load progress data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useProgress.useEffect": ()=>{
            if (storyId) {
                const progress = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].getStoryProgress(storyId);
                setStoryProgress(progress);
            }
            const stats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].getProgressStats();
            setOverallStats(stats);
        }
    }["useProgress.useEffect"], [
        storyId
    ]);
    // Start a learning session (pure service call, no state updates)
    const startSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[startSession]": (sessionStoryId)=>{
            return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].startSession(sessionStoryId);
        }
    }["useProgress.useCallback[startSession]"], []);
    // End the current session (pure service call, no state updates)
    const endSession = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[endSession]": ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].endSession();
        }
    }["useProgress.useCallback[endSession]"], []);
    // Track word encounter
    const trackWordEncounter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[trackWordEncounter]": (word)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].trackWordEncounter(word);
        }
    }["useProgress.useCallback[trackWordEncounter]"], []);
    // Track translation request
    const trackTranslationRequest = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[trackTranslationRequest]": (word)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].trackTranslationRequest(word);
        }
    }["useProgress.useCallback[trackTranslationRequest]"], []);
    // Track audio play time
    const trackAudioPlayTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[trackAudioPlayTime]": (seconds)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].trackAudioPlayTime(seconds);
        }
    }["useProgress.useCallback[trackAudioPlayTime]"], []);
    // Update completion percentage
    const updateCompletionPercentage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[updateCompletionPercentage]": (percentage)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].updateCompletionPercentage(percentage);
        }
    }["useProgress.useCallback[updateCompletionPercentage]"], []);
    // Calculate reading progress based on current word position (without triggering updates)
    const calculateReadingProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[calculateReadingProgress]": (currentWordIndex, totalWords)=>{
            if (totalWords === 0) return 0;
            return Math.round(currentWordIndex / totalWords * 100);
        }
    }["useProgress.useCallback[calculateReadingProgress]"], []);
    // Refresh progress data manually
    const refreshProgress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useProgress.useCallback[refreshProgress]": ()=>{
            if (storyId) {
                const progress = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].getStoryProgress(storyId);
                setStoryProgress(progress);
            }
            const stats = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$progress$2d$service$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["progressService"].getProgressStats();
            setOverallStats(stats);
        }
    }["useProgress.useCallback[refreshProgress]"], [
        storyId
    ]);
    return {
        storyProgress,
        overallStats,
        startSession,
        endSession,
        trackWordEncounter,
        trackTranslationRequest,
        trackAudioPlayTime,
        updateCompletionPercentage,
        calculateReadingProgress,
        refreshProgress
    };
}
_s(useProgress, "dzx2hUPLxMM3JWQ82Vc2/UEJ9PA=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/screens/StoryReaderScreen.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "StoryReaderScreen": ()=>StoryReaderScreen
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$StoryHeader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/story/StoryHeader.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$ReadingArea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/story/ReadingArea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$AudioPlayer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/story/AudioPlayer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$PageNavigation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/story/PageNavigation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/ErrorBoundary.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$KeyboardShortcutsHelp$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/KeyboardShortcutsHelp.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ProgressDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/ProgressDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useAudioPlayer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hooks/useAudioPlayer.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useTranslation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hooks/useTranslation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useKeyboardShortcuts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hooks/useKeyboardShortcuts.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useProgress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/hooks/useProgress.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
function StoryReaderScreen(param) {
    let { story } = param;
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        currentWordIndex: -1,
        selectedWord: undefined,
        translationCard: {
            isVisible: false,
            word: '',
            position: {
                x: 0,
                y: 0
            },
            translation: undefined,
            isLoading: false
        },
        audioPlayer: {
            isPlaying: false,
            currentTime: 0,
            duration: 0,
            volume: 1.0
        }
    });
    // Use translation hook
    const translation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useTranslation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"])();
    // Use progress tracking
    const progress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useProgress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProgress"])(story.id);
    // Handle word highlighting during audio playback
    const handleWordHighlight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handleWordHighlight]": (wordIndex)=>{
            setState({
                "StoryReaderScreen.useCallback[handleWordHighlight]": (prev)=>({
                        ...prev,
                        currentWordIndex: wordIndex
                    })
            }["StoryReaderScreen.useCallback[handleWordHighlight]"]);
        }
    }["StoryReaderScreen.useCallback[handleWordHighlight]"], []);
    // Use audio player hook with real functionality
    const audioPlayer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useAudioPlayer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAudioPlayer"])({
        audioUrl: story.audioUrl,
        wordTimings: story.wordTimings,
        onWordHighlight: handleWordHighlight
    });
    // Start progress tracking session on mount
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "StoryReaderScreen.useEffect": ()=>{
            progress.startSession(story.id);
            return ({
                "StoryReaderScreen.useEffect": ()=>{
                    progress.endSession();
                    // Refresh progress data after session ends
                    setTimeout({
                        "StoryReaderScreen.useEffect": ()=>{
                            progress.refreshProgress();
                        }
                    }["StoryReaderScreen.useEffect"], 100);
                }
            })["StoryReaderScreen.useEffect"];
        }
    }["StoryReaderScreen.useEffect"], [
        story.id,
        progress
    ]);
    // Track reading progress when current word changes (debounced)
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "StoryReaderScreen.useEffect": ()=>{
            if (state.currentWordIndex >= 0 && story.wordTimings) {
                const totalWords = story.wordTimings.length;
                if (totalWords > 0) {
                    const progressPercentage = Math.round(state.currentWordIndex / totalWords * 100);
                    // Debounce progress updates to avoid excessive calls
                    const timeoutId = setTimeout({
                        "StoryReaderScreen.useEffect.timeoutId": ()=>{
                            progress.updateCompletionPercentage(progressPercentage);
                        }
                    }["StoryReaderScreen.useEffect.timeoutId"], 100);
                    return ({
                        "StoryReaderScreen.useEffect": ()=>clearTimeout(timeoutId)
                    })["StoryReaderScreen.useEffect"];
                }
            }
        }
    }["StoryReaderScreen.useEffect"], [
        state.currentWordIndex,
        story.wordTimings,
        progress
    ]);
    // Update state when audio player state changes
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "StoryReaderScreen.useEffect": ()=>{
            setState({
                "StoryReaderScreen.useEffect": (prev)=>({
                        ...prev,
                        audioPlayer: audioPlayer.state
                    })
            }["StoryReaderScreen.useEffect"]);
        }
    }["StoryReaderScreen.useEffect"], [
        audioPlayer.state
    ]);
    const handleWordClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handleWordClick]": async (word, index, event)=>{
            // Close existing translation card if clicking the same word
            if (state.translationCard.isVisible && state.translationCard.word === word) {
                setState({
                    "StoryReaderScreen.useCallback[handleWordClick]": (prev)=>({
                            ...prev,
                            translationCard: {
                                ...prev.translationCard,
                                isVisible: false
                            }
                        })
                }["StoryReaderScreen.useCallback[handleWordClick]"]);
                return;
            }
            // Show loading state
            setState({
                "StoryReaderScreen.useCallback[handleWordClick]": (prev)=>({
                        ...prev,
                        selectedWord: word,
                        translationCard: {
                            isVisible: true,
                            word,
                            position: {
                                x: event.clientX,
                                y: event.clientY
                            },
                            translation: undefined,
                            isLoading: true
                        }
                    })
            }["StoryReaderScreen.useCallback[handleWordClick]"]);
            // Track word encounter and translation request
            progress.trackWordEncounter(word);
            progress.trackTranslationRequest(word);
            // Get translation using the hook
            const translationResult = await translation.translateWord(word, 'en', 'fr');
            setState({
                "StoryReaderScreen.useCallback[handleWordClick]": (prev)=>({
                        ...prev,
                        translationCard: {
                            ...prev.translationCard,
                            translation: translationResult || undefined,
                            isLoading: false
                        }
                    })
            }["StoryReaderScreen.useCallback[handleWordClick]"]);
        }
    }["StoryReaderScreen.useCallback[handleWordClick]"], [
        state.translationCard.isVisible,
        state.translationCard.word,
        translation
    ]);
    const handleTranslationClose = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handleTranslationClose]": ()=>{
            setState({
                "StoryReaderScreen.useCallback[handleTranslationClose]": (prev)=>({
                        ...prev,
                        translationCard: {
                            ...prev.translationCard,
                            isVisible: false
                        }
                    })
            }["StoryReaderScreen.useCallback[handleTranslationClose]"]);
        }
    }["StoryReaderScreen.useCallback[handleTranslationClose]"], []);
    const handlePlayPause = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handlePlayPause]": ()=>{
            audioPlayer.togglePlayPause();
        }
    }["StoryReaderScreen.useCallback[handlePlayPause]"], [
        audioPlayer
    ]);
    const handleSeek = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handleSeek]": (time)=>{
            audioPlayer.seek(time);
        }
    }["StoryReaderScreen.useCallback[handleSeek]"], [
        audioPlayer
    ]);
    const handleVolumeChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handleVolumeChange]": (volume)=>{
            audioPlayer.setVolume(volume);
        }
    }["StoryReaderScreen.useCallback[handleVolumeChange]"], [
        audioPlayer
    ]);
    const handleSeekForward = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handleSeekForward]": ()=>{
            const newTime = Math.min(state.audioPlayer.currentTime + 10, state.audioPlayer.duration);
            audioPlayer.seek(newTime);
        }
    }["StoryReaderScreen.useCallback[handleSeekForward]"], [
        audioPlayer,
        state.audioPlayer.currentTime,
        state.audioPlayer.duration
    ]);
    const handleSeekBackward = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "StoryReaderScreen.useCallback[handleSeekBackward]": ()=>{
            const newTime = Math.max(state.audioPlayer.currentTime - 10, 0);
            audioPlayer.seek(newTime);
        }
    }["StoryReaderScreen.useCallback[handleSeekBackward]"], [
        audioPlayer,
        state.audioPlayer.currentTime
    ]);
    // Setup keyboard shortcuts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useKeyboardShortcuts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKeyboardShortcuts"])({
        onPlayPause: handlePlayPause,
        onSeekForward: handleSeekForward,
        onSeekBackward: handleSeekBackward,
        onCloseTranslation: handleTranslationClose
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ErrorBoundary$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ErrorBoundary"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col h-screen bg-white",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$StoryHeader$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StoryHeader"], {
                    title: story.title,
                    chapter: story.chapter,
                    isAudioPlaying: state.audioPlayer.isPlaying,
                    onBookmarkClick: ()=>console.log('Bookmark clicked'),
                    onMenuClick: ()=>console.log('Menu clicked')
                }, void 0, false, {
                    fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                    lineNumber: 181,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$ReadingArea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReadingArea"], {
                    content: story.content,
                    currentWordIndex: state.currentWordIndex,
                    translationCard: state.translationCard,
                    onWordClick: handleWordClick,
                    onTranslationClose: handleTranslationClose,
                    onTranslationPlayAudio: ()=>console.log('Play translation audio')
                }, void 0, false, {
                    fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                    lineNumber: 189,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$PageNavigation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PageNavigation"], {
                    currentPage: 24,
                    totalPages: 120,
                    onPreviousPage: ()=>console.log('Previous page'),
                    onNextPage: ()=>console.log('Next page')
                }, void 0, false, {
                    fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                    lineNumber: 198,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$story$2f$AudioPlayer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AudioPlayer"], {
                    state: state.audioPlayer,
                    onPlayPause: handlePlayPause,
                    onSeek: handleSeek,
                    onVolumeChange: handleVolumeChange
                }, void 0, false, {
                    fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                    lineNumber: 205,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "fixed bottom-24 left-4 z-40 max-w-xs",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$ProgressDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProgressDisplay"], {
                        storyProgress: progress.storyProgress,
                        overallStats: progress.overallStats
                    }, void 0, false, {
                        fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                        lineNumber: 214,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                    lineNumber: 213,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "fixed bottom-24 right-4 z-40",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$KeyboardShortcutsHelp$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["KeyboardShortcutsHelp"], {}, void 0, false, {
                        fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                        lineNumber: 221,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
                    lineNumber: 220,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
            lineNumber: 180,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/screens/StoryReaderScreen.tsx",
        lineNumber: 179,
        columnNumber: 5
    }, this);
}
_s(StoryReaderScreen, "BUSrsdvUJH3o4Q8OurwjX6YW0Mc=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useTranslation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useProgress$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProgress"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useAudioPlayer$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAudioPlayer"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$hooks$2f$useKeyboardShortcuts$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useKeyboardShortcuts"]
    ];
});
_c = StoryReaderScreen;
var _c;
__turbopack_context__.k.register(_c, "StoryReaderScreen");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/data/stories.json (json)": ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("[{\"id\":\"norwegian-wood\",\"title\":\"Norwegian Wood\",\"chapter\":\"Chapter 3\",\"language\":\"fr\",\"difficulty\":\"intermediate\",\"content\":\"I was 37 then, strapped in my seat as the huge 747 plunged through dense cloud cover on a airport. Gently served to remind me of another time, almost a third of a century earlier. The plane completed its endless descent and finally touched down. I had no idea what time it was or which day of the week. I felt as if I'd been away from the world I had known forever.\",\"audioUrl\":\"https://www.learningcontainer.com/wp-content/uploads/2020/02/Kalimba.mp3\",\"wordTimings\":[{\"word\":\"I\",\"startTime\":0.0,\"endTime\":0.2},{\"word\":\"was\",\"startTime\":0.2,\"endTime\":0.4},{\"word\":\"37\",\"startTime\":0.4,\"endTime\":0.8},{\"word\":\"then,\",\"startTime\":0.8,\"endTime\":1.2},{\"word\":\"strapped\",\"startTime\":1.2,\"endTime\":1.8},{\"word\":\"in\",\"startTime\":1.8,\"endTime\":2.0},{\"word\":\"my\",\"startTime\":2.0,\"endTime\":2.2},{\"word\":\"seat\",\"startTime\":2.2,\"endTime\":2.6},{\"word\":\"as\",\"startTime\":2.6,\"endTime\":2.8},{\"word\":\"the\",\"startTime\":2.8,\"endTime\":3.0},{\"word\":\"huge\",\"startTime\":3.0,\"endTime\":3.4},{\"word\":\"747\",\"startTime\":3.4,\"endTime\":4.0},{\"word\":\"plunged\",\"startTime\":4.0,\"endTime\":4.6},{\"word\":\"through\",\"startTime\":4.6,\"endTime\":5.0},{\"word\":\"dense\",\"startTime\":5.0,\"endTime\":5.4},{\"word\":\"cloud\",\"startTime\":5.4,\"endTime\":5.8},{\"word\":\"cover\",\"startTime\":5.8,\"endTime\":6.2},{\"word\":\"on\",\"startTime\":6.2,\"endTime\":6.4},{\"word\":\"a\",\"startTime\":6.4,\"endTime\":6.6},{\"word\":\"airport.\",\"startTime\":6.6,\"endTime\":7.4},{\"word\":\"Gently\",\"startTime\":8.0,\"endTime\":8.6},{\"word\":\"served\",\"startTime\":8.6,\"endTime\":9.0},{\"word\":\"to\",\"startTime\":9.0,\"endTime\":9.2},{\"word\":\"remind\",\"startTime\":9.2,\"endTime\":9.6},{\"word\":\"me\",\"startTime\":9.6,\"endTime\":9.8},{\"word\":\"of\",\"startTime\":9.8,\"endTime\":10.0},{\"word\":\"another\",\"startTime\":10.0,\"endTime\":10.6},{\"word\":\"time,\",\"startTime\":10.6,\"endTime\":11.0},{\"word\":\"almost\",\"startTime\":11.0,\"endTime\":11.4},{\"word\":\"a\",\"startTime\":11.4,\"endTime\":11.6},{\"word\":\"third\",\"startTime\":11.6,\"endTime\":12.0},{\"word\":\"of\",\"startTime\":12.0,\"endTime\":12.2},{\"word\":\"a\",\"startTime\":12.2,\"endTime\":12.4},{\"word\":\"century\",\"startTime\":12.4,\"endTime\":13.0},{\"word\":\"earlier.\",\"startTime\":13.0,\"endTime\":13.8}]}]"));}),
"[project]/src/app/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Home
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$screens$2f$StoryReaderScreen$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/screens/StoryReaderScreen.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$stories$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/stories.json (json)");
"use client";
;
;
;
function Home() {
    // Get the first story from our mock data
    const story = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$stories$2e$json__$28$json$29$__["default"][0];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-4 right-4 z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                    href: "/admin",
                    className: "px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors text-sm",
                    children: "Admin Panel"
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 15,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 14,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$screens$2f$StoryReaderScreen$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StoryReaderScreen"], {
                story: story
            }, void 0, false, {
                fileName: "[project]/src/app/page.tsx",
                lineNumber: 22,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = Home;
var _c;
__turbopack_context__.k.register(_c, "Home");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_f29728a0._.js.map