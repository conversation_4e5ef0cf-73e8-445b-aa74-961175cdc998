{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Typography.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface TypographyProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport const Typography = {\n  Title: ({ children, className = '' }: TypographyProps) => (\n    <h1 className={`text-2xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </h1>\n  ),\n  \n  Subtitle: ({ children, className = '' }: TypographyProps) => (\n    <h2 className={`text-base text-gray-600 ${className}`}>\n      {children}\n    </h2>\n  ),\n  \n  Body: ({ children, className = '' }: TypographyProps) => (\n    <p className={`text-lg text-gray-800 leading-relaxed ${className}`}>\n      {children}\n    </p>\n  ),\n  \n  Caption: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-sm text-gray-500 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationWord: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationText: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-base text-gray-700 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  Micro: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xs text-gray-400 ${className}`}>\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,aAAa;IACxB,OAAO;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACnD,6LAAC;YAAG,WAAW,AAAC,oCAA6C,OAAV;sBAChD;;;;;;;IAIL,UAAU;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACtD,6LAAC;YAAG,WAAW,AAAC,2BAAoC,OAAV;sBACvC;;;;;;;IAIL,MAAM;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BAClD,6LAAC;YAAE,WAAW,AAAC,yCAAkD,OAAV;sBACpD;;;;;;;IAIL,SAAS;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACrD,6LAAC;YAAK,WAAW,AAAC,yBAAkC,OAAV;sBACvC;;;;;;;IAIL,iBAAiB;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BAC7D,6LAAC;YAAK,WAAW,AAAC,mCAA4C,OAAV;sBACjD;;;;;;;IAIL,iBAAiB;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BAC7D,6LAAC;YAAK,WAAW,AAAC,2BAAoC,OAAV;sBACzC;;;;;;;IAIL,OAAO;YAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB;6BACnD,6LAAC;YAAK,WAAW,AAAC,yBAAkC,OAAV;sBACvC;;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/IconButton.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface IconButtonProps {\n  icon: React.ReactNode\n  onClick: () => void\n  variant?: 'default' | 'ghost' | 'outline'\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  disabled?: boolean\n  'aria-label'?: string\n}\n\nexport function IconButton({\n  icon,\n  onClick,\n  variant = 'default',\n  size = 'md',\n  className = '',\n  disabled = false,\n  'aria-label': ariaLabel,\n}: IconButtonProps) {\n  const baseClasses = 'inline-flex items-center justify-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'\n  \n  const variantClasses = {\n    default: 'bg-gray-100 hover:bg-gray-200 text-gray-700',\n    ghost: 'hover:bg-gray-100 text-gray-600',\n    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'\n  }\n  \n  const sizeClasses = {\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-10 h-10 text-base',\n    lg: 'w-12 h-12 text-lg'\n  }\n  \n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      aria-label={ariaLabel}\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}\n    >\n      {icon}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAYO,SAAS,WAAW,KAQT;QARS,EACzB,IAAI,EACJ,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,cAAc,SAAS,EACP,GARS;IASzB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,cAAY;QACZ,WAAW,AAAC,GAAiB,OAAf,aAAY,KAA8B,OAA3B,cAAc,CAAC,QAAQ,EAAC,KAAwB,OAArB,WAAW,CAAC,KAAK,EAAC,KAAa,OAAV;kBAE5E;;;;;;AAGP;KAjCgB", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/StoryHeader.tsx"], "sourcesContent": ["import React from 'react'\nimport { BookmarkIcon, HamburgerMenuIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\n\ninterface StoryHeaderProps {\n  title: string\n  chapter?: string\n  isAudioPlaying?: boolean\n  onBookmarkClick: () => void\n  onMenuClick: () => void\n}\n\nexport function StoryHeader({ title, chapter, isAudioPlaying, onBookmarkClick, onMenuClick }: StoryHeaderProps) {\n  return (\n    <header className=\"flex items-center justify-between p-4 bg-white border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-2\">\n          <Typography.Title className=\"text-xl font-bold text-gray-900\">\n            {title}\n          </Typography.Title>\n          {isAudioPlaying && (\n            <div className=\"flex space-x-1\">\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\"></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n            </div>\n          )}\n        </div>\n        {chapter && (\n          <Typography.Subtitle className=\"text-sm text-gray-600 mt-1\">\n            {chapter}\n          </Typography.Subtitle>\n        )}\n      </div>\n      \n      <div className=\"flex items-center space-x-2\">\n        <IconButton\n          icon={<BookmarkIcon className=\"w-5 h-5\" />}\n          onClick={onBookmarkClick}\n          variant=\"ghost\"\n          aria-label=\"Bookmark story\"\n        />\n        <IconButton\n          icon={<HamburgerMenuIcon className=\"w-5 h-5\" />}\n          onClick={onMenuClick}\n          variant=\"ghost\"\n          aria-label=\"Open menu\"\n        />\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAUO,SAAS,YAAY,KAAkF;QAAlF,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAoB,GAAlF;IAC1B,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB;;;;;;4BAEF,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC3F,6LAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;oBAIhG,yBACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,QAAQ;wBAAC,WAAU;kCAC5B;;;;;;;;;;;;0BAKP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yIAAA,CAAA,aAAU;wBACT,oBAAM,6LAAC,mLAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;kCAEb,6LAAC,yIAAA,CAAA,aAAU;wBACT,oBAAM,6LAAC,mLAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;wBACnC,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;;;;;;;;;;;;;AAKrB;KAvCgB", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/WordHighlighter.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface WordHighlighterProps {\n  text: string\n  currentWordIndex?: number\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n}\n\nexport function WordHighlighter({ text, currentWordIndex, onWordClick }: WordHighlighterProps) {\n  const words = text.split(/(\\s+)/)\n  let wordIndex = 0\n\n  return (\n    <div className=\"text-lg leading-relaxed text-gray-800 select-none\">\n      {words.map((segment, segmentIndex) => {\n        // Skip whitespace segments\n        if (/^\\s+$/.test(segment)) {\n          return <span key={segmentIndex}>{segment}</span>\n        }\n\n        const isCurrentWord = wordIndex === currentWordIndex\n        const currentIndex = wordIndex\n        wordIndex++\n\n        return (\n          <span\n            key={segmentIndex}\n            className={`cursor-pointer transition-all duration-300 hover:bg-blue-100 px-1 py-0.5 rounded ${\n              isCurrentWord\n                ? 'bg-blue-300 text-blue-900 font-semibold shadow-sm scale-105'\n                : 'hover:scale-102'\n            }`}\n            onClick={(event) => onWordClick(segment, currentIndex, event)}\n          >\n            {segment}\n          </span>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,gBAAgB,KAA6D;QAA7D,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAwB,GAA7D;IAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,YAAY;IAEhB,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,SAAS;YACnB,2BAA2B;YAC3B,IAAI,QAAQ,IAAI,CAAC,UAAU;gBACzB,qBAAO,6LAAC;8BAAyB;mBAAf;;;;;YACpB;YAEA,MAAM,gBAAgB,cAAc;YACpC,MAAM,eAAe;YACrB;YAEA,qBACE,6LAAC;gBAEC,WAAW,AAAC,oFAIX,OAHC,gBACI,gEACA;gBAEN,SAAS,CAAC,QAAU,YAAY,SAAS,cAAc;0BAEtD;eARI;;;;;QAWX;;;;;;AAGN;KAhCgB", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/TranslationCard.tsx"], "sourcesContent": ["import React from 'react'\nimport { Cross2Icon, SpeakerLoudIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { TranslationResult } from '@/lib/types'\n\ninterface TranslationCardProps {\n  word: string\n  translation?: TranslationResult\n  isLoading?: boolean\n  onPlayAudio: () => void\n  onClose: () => void\n}\n\nexport function TranslationCard({\n  word,\n  translation,\n  isLoading = false,\n  onPlayAudio,\n  onClose\n}: TranslationCardProps) {\n  return (\n    <div className=\"bg-gray-50 rounded-xl p-4 mx-5 mb-6 relative shadow-sm border border-gray-200\">\n      <IconButton\n        icon={<Cross2Icon className=\"w-4 h-4\" />}\n        onClick={onClose}\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"absolute top-2 right-2\"\n        aria-label=\"Close translation\"\n      />\n      \n      <div className=\"flex items-center justify-between mb-3\">\n        <div>\n          <Typography.TranslationWord className=\"text-xl\">\n            {word}\n          </Typography.TranslationWord>\n          {translation?.pronunciation && (\n            <Typography.Caption className=\"block mt-1\">\n              /{translation.pronunciation}/\n            </Typography.Caption>\n          )}\n        </div>\n        <IconButton \n          icon={<SpeakerLoudIcon className=\"w-4 h-4\" />} \n          onClick={onPlayAudio}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Play pronunciation\"\n        />\n      </div>\n      \n      {isLoading ? (\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n          <Typography.Caption className=\"text-gray-400 mt-2\">\n            Translating...\n          </Typography.Caption>\n        </div>\n      ) : translation ? (\n        <>\n          <Typography.TranslationText className=\"block mb-3\">\n            {translation.translation}\n          </Typography.TranslationText>\n          \n          {translation.example && (\n            <div className=\"text-sm text-gray-600 border-t border-gray-200 pt-3\">\n              <Typography.Caption className=\"italic block mb-1\">\n                {translation.example.original}\n              </Typography.Caption>\n              <Typography.Caption className=\"block\">\n                {translation.example.translation}\n              </Typography.Caption>\n            </div>\n          )}\n        </>\n      ) : (\n        <Typography.Caption className=\"text-gray-500\">\n          Translation not available\n        </Typography.Caption>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAWO,SAAS,gBAAgB,KAMT;QANS,EAC9B,IAAI,EACJ,WAAW,EACX,YAAY,KAAK,EACjB,WAAW,EACX,OAAO,EACc,GANS;IAO9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,aAAU;gBACT,oBAAM,6LAAC,mLAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAC5B,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,cAAW;;;;;;0BAGb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC,yIAAA,CAAA,aAAU,CAAC,eAAe;gCAAC,WAAU;0CACnC;;;;;;4BAEF,CAAA,wBAAA,kCAAA,YAAa,aAAa,mBACzB,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;;oCAAa;oCACvC,YAAY,aAAa;oCAAC;;;;;;;;;;;;;kCAIlC,6LAAC,yIAAA,CAAA,aAAU;wBACT,oBAAM,6LAAC,mLAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;wBACjC,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;YAId,0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;wBAAC,WAAU;kCAAqB;;;;;;;;;;;uBAInD,4BACF;;kCACE,6LAAC,yIAAA,CAAA,aAAU,CAAC,eAAe;wBAAC,WAAU;kCACnC,YAAY,WAAW;;;;;;oBAGzB,YAAY,OAAO,kBAClB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,QAAQ;;;;;;0CAE/B,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,WAAW;;;;;;;;;;;;;6CAMxC,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;0BAAgB;;;;;;;;;;;;AAMtD;KAvEgB", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/ReadingArea.tsx"], "sourcesContent": ["import React from 'react'\nimport { WordHighlighter } from './WordHighlighter'\nimport { TranslationCard } from './TranslationCard'\nimport { TranslationCardState } from '@/lib/types'\n\ninterface ReadingAreaProps {\n  content: string\n  currentWordIndex?: number\n  translationCard: TranslationCardState\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n  onTranslationClose: () => void\n  onTranslationPlayAudio: () => void\n}\n\nexport function ReadingArea({\n  content,\n  currentWordIndex,\n  translationCard,\n  onWordClick,\n  onTranslationClose,\n  onTranslationPlayAudio\n}: ReadingAreaProps) {\n  return (\n    <div className=\"flex-1 overflow-y-auto\">\n      <div className=\"max-w-sm mx-auto p-6\">\n        <div className=\"mb-6\">\n          <WordHighlighter\n            text={content}\n            currentWordIndex={currentWordIndex}\n            onWordClick={onWordClick}\n          />\n        </div>\n        \n        {translationCard.isVisible && (\n          <TranslationCard\n            word={translationCard.word}\n            translation={translationCard.translation}\n            isLoading={translationCard.isLoading}\n            onPlayAudio={onTranslationPlayAudio}\n            onClose={onTranslationClose}\n          />\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAYO,SAAS,YAAY,KAOT;QAPS,EAC1B,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,sBAAsB,EACL,GAPS;IAQ1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,iJAAA,CAAA,kBAAe;wBACd,MAAM;wBACN,kBAAkB;wBAClB,aAAa;;;;;;;;;;;gBAIhB,gBAAgB,SAAS,kBACxB,6LAAC,iJAAA,CAAA,kBAAe;oBACd,MAAM,gBAAgB,IAAI;oBAC1B,aAAa,gBAAgB,WAAW;oBACxC,WAAW,gBAAgB,SAAS;oBACpC,aAAa;oBACb,SAAS;;;;;;;;;;;;;;;;;AAMrB;KA/BgB", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Slider.tsx"], "sourcesContent": ["import React from 'react'\nimport * as RadixSlider from '@radix-ui/react-slider'\n\ninterface SliderProps {\n  value: number[]\n  onValueChange: (value: number[]) => void\n  max?: number\n  min?: number\n  step?: number\n  className?: string\n  disabled?: boolean\n}\n\nexport function Slider({\n  value,\n  onValueChange,\n  max = 100,\n  min = 0,\n  step = 1,\n  className = '',\n  disabled = false,\n}: SliderProps) {\n  return (\n    <RadixSlider.Root\n      className={`relative flex items-center select-none touch-none w-full h-5 ${className}`}\n      value={value}\n      onValueChange={onValueChange}\n      max={max}\n      min={min}\n      step={step}\n      disabled={disabled}\n    >\n      <RadixSlider.Track className=\"bg-gray-200 relative grow rounded-full h-1\">\n        <RadixSlider.Range className=\"absolute bg-gray-900 rounded-full h-full\" />\n      </RadixSlider.Track>\n      <RadixSlider.Thumb\n        className=\"block w-4 h-4 bg-white border-2 border-gray-900 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n        aria-label=\"Volume\"\n      />\n    </RadixSlider.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAYO,SAAS,OAAO,KAQT;QARS,EACrB,KAAK,EACL,aAAa,EACb,MAAM,GAAG,EACT,MAAM,CAAC,EACP,OAAO,CAAC,EACR,YAAY,EAAE,EACd,WAAW,KAAK,EACJ,GARS;IASrB,qBACE,6LAAC,qKAAA,CAAA,OAAgB;QACf,WAAW,AAAC,gEAAyE,OAAV;QAC3E,OAAO;QACP,eAAe;QACf,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;;0BAEV,6LAAC,qKAAA,CAAA,QAAiB;gBAAC,WAAU;0BAC3B,cAAA,6LAAC,qKAAA,CAAA,QAAiB;oBAAC,WAAU;;;;;;;;;;;0BAE/B,6LAAC,qKAAA,CAAA,QAAiB;gBAChB,WAAU;gBACV,cAAW;;;;;;;;;;;;AAInB;KA5BgB", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/AudioPlayer.tsx"], "sourcesContent": ["import React from 'react'\nimport { PlayIcon, PauseIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Slider } from '../ui/Slider'\nimport { Typography } from '../ui/Typography'\nimport { AudioPlayerState } from '@/lib/types'\n\ninterface AudioPlayerProps {\n  state: AudioPlayerState\n  onPlayPause: () => void\n  onSeek: (time: number) => void\n  onVolumeChange: (volume: number) => void\n}\n\nfunction formatTime(seconds: number): string {\n  const minutes = Math.floor(seconds / 60)\n  const remainingSeconds = Math.floor(seconds % 60)\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n}\n\nexport function AudioPlayer({ state, onPlayPause, onSeek, onVolumeChange }: AudioPlayerProps) {\n  const { isPlaying, currentTime, duration, volume } = state\n\n  const handleProgressChange = (value: number[]) => {\n    onSeek(value[0])\n  }\n\n  const handleVolumeChange = (value: number[]) => {\n    onVolumeChange(value[0] / 100)\n  }\n\n  return (\n    <div className=\"bg-white border-t border-gray-200 p-4\">\n      {/* Progress Bar */}\n      <div className=\"mb-4\">\n        <Slider\n          value={[currentTime]}\n          onValueChange={handleProgressChange}\n          max={duration}\n          min={0}\n          step={0.1}\n          className=\"w-full\"\n        />\n        <div className=\"flex justify-between mt-2\">\n          <Typography.Micro>\n            {formatTime(currentTime)}\n          </Typography.Micro>\n          <Typography.Micro>\n            {formatTime(duration)}\n          </Typography.Micro>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex items-center justify-center\">\n        <IconButton\n          icon={isPlaying ? <PauseIcon className=\"w-6 h-6\" /> : <PlayIcon className=\"w-6 h-6\" />}\n          onClick={onPlayPause}\n          size=\"lg\"\n          className=\"bg-gray-900 text-white hover:bg-gray-800\"\n          aria-label={isPlaying ? 'Pause' : 'Play'}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAUA,SAAS,WAAW,OAAe;IACjC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;IAC9C,OAAO,AAAC,GAAa,OAAX,SAAQ,KAAgD,OAA7C,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG;AAC/D;AAEO,SAAS,YAAY,KAAgE;QAAhE,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAoB,GAAhE;IAC1B,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAErD,MAAM,uBAAuB,CAAC;QAC5B,OAAO,KAAK,CAAC,EAAE;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO;4BAAC;yBAAY;wBACpB,eAAe;wBACf,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;0CAEd,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;;;;;;;;;;;;;0BAMlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;oBACT,MAAM,0BAAY,6LAAC,mLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;+CAAe,6LAAC,mLAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1E,SAAS;oBACT,MAAK;oBACL,WAAU;oBACV,cAAY,YAAY,UAAU;;;;;;;;;;;;;;;;;AAK5C;KA7CgB", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/PageNavigation.tsx"], "sourcesContent": ["import React from 'react'\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Typography } from '../ui/Typography'\n\ninterface PageNavigationProps {\n  currentPage: number\n  totalPages: number\n  onPreviousPage: () => void\n  onNextPage: () => void\n}\n\nexport function PageNavigation({ \n  currentPage, \n  totalPages, \n  onPreviousPage, \n  onNextPage \n}: PageNavigationProps) {\n  return (\n    <div className=\"flex items-center justify-between p-4 bg-white border-t border-gray-100\">\n      <IconButton\n        icon={<ChevronLeftIcon className=\"w-5 h-5\" />}\n        onClick={onPreviousPage}\n        variant=\"ghost\"\n        disabled={currentPage <= 1}\n        aria-label=\"Previous page\"\n      />\n      \n      <Typography.Caption className=\"text-gray-500\">\n        Page {currentPage} of {totalPages}\n      </Typography.Caption>\n      \n      <IconButton\n        icon={<ChevronRightIcon className=\"w-5 h-5\" />}\n        onClick={onNextPage}\n        variant=\"ghost\"\n        disabled={currentPage >= totalPages}\n        aria-label=\"Next page\"\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,eAAe,KAKT;QALS,EAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACU,GALS;IAM7B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,aAAU;gBACT,oBAAM,6LAAC,mLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;gBACjC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;0BAGb,6LAAC,yIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;;oBAAgB;oBACtC;oBAAY;oBAAK;;;;;;;0BAGzB,6LAAC,yIAAA,CAAA,aAAU;gBACT,oBAAM,6LAAC,mLAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAClC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;;;;;;;AAInB;KA7BgB", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Typography } from './Typography'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>\n}\n\nexport class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback\n      return <FallbackComponent error={this.state.error} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-[200px] p-6 bg-red-50 rounded-lg border border-red-200\">\n      <Typography.Title className=\"text-red-800 mb-2\">\n        Something went wrong\n      </Typography.Title>\n      <Typography.Body className=\"text-red-600 text-center mb-4\">\n        {error?.message || 'An unexpected error occurred'}\n      </Typography.Body>\n      <button\n        onClick={resetError}\n        className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n      >\n        Try again\n      </button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAeO,MAAM,sBAAsB,6JAAA,CAAA,UAAK,CAAC,SAAS;IAMhD,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;IACzD;IAMA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACjD,qBAAO,6LAAC;gBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;QAChF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;IAxBA,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC,QAYR,+KAAA,cAAa;YACX,IAAI,CAAC,QAAQ,CAAC;gBAAE,UAAU;gBAAO,OAAO;YAAU;QACpD;QAbE,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;AAsBF;AAEA,SAAS,qBAAqB,KAAgE;QAAhE,EAAE,KAAK,EAAE,UAAU,EAA6C,GAAhE;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,aAAU,CAAC,KAAK;gBAAC,WAAU;0BAAoB;;;;;;0BAGhD,6LAAC,yIAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,WAAU;0BACxB,CAAA,kBAAA,4BAAA,MAAO,OAAO,KAAI;;;;;;0BAErB,6LAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP;KAjBS", "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useAudioPlayer.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { AudioPlayerState, WordTiming } from '../types'\n\ninterface UseAudioPlayerProps {\n  audioUrl?: string\n  wordTimings?: WordTiming[]\n  onWordHighlight?: (wordIndex: number) => void\n}\n\nexport function useAudioPlayer({ \n  audioUrl, \n  wordTimings = [], \n  onWordHighlight \n}: UseAudioPlayerProps) {\n  const audioRef = useRef<HTMLAudioElement | null>(null)\n  const [state, setState] = useState<AudioPlayerState>({\n    isPlaying: false,\n    currentTime: 0,\n    duration: 0,\n    volume: 1.0\n  })\n\n  // Initialize audio element\n  useEffect(() => {\n    if (!audioUrl) return\n\n    const audio = new Audio(audioUrl)\n    audioRef.current = audio\n\n    const handleLoadedMetadata = () => {\n      setState(prev => ({\n        ...prev,\n        duration: audio.duration || 0\n      }))\n    }\n\n    const handleTimeUpdate = () => {\n      setState(prev => ({\n        ...prev,\n        currentTime: audio.currentTime\n      }))\n\n      // Find current word based on time\n      if (wordTimings.length > 0 && onWordHighlight) {\n        const currentWordIndex = wordTimings.findIndex(\n          timing => audio.currentTime >= timing.startTime && audio.currentTime <= timing.endTime\n        )\n        if (currentWordIndex !== -1) {\n          onWordHighlight(currentWordIndex)\n        }\n      }\n    }\n\n    const handleEnded = () => {\n      setState(prev => ({\n        ...prev,\n        isPlaying: false,\n        currentTime: 0\n      }))\n    }\n\n    const handleError = (e: Event) => {\n      console.error('Audio error:', e)\n      setState(prev => ({\n        ...prev,\n        isPlaying: false\n      }))\n    }\n\n    audio.addEventListener('loadedmetadata', handleLoadedMetadata)\n    audio.addEventListener('timeupdate', handleTimeUpdate)\n    audio.addEventListener('ended', handleEnded)\n    audio.addEventListener('error', handleError)\n\n    return () => {\n      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)\n      audio.removeEventListener('timeupdate', handleTimeUpdate)\n      audio.removeEventListener('ended', handleEnded)\n      audio.removeEventListener('error', handleError)\n      audio.pause()\n    }\n  }, [audioUrl, wordTimings, onWordHighlight])\n\n  const play = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.play()\n      setState(prev => ({ ...prev, isPlaying: true }))\n    }\n  }, [])\n\n  const pause = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.pause()\n      setState(prev => ({ ...prev, isPlaying: false }))\n    }\n  }, [])\n\n  const togglePlayPause = useCallback(() => {\n    if (state.isPlaying) {\n      pause()\n    } else {\n      play()\n    }\n  }, [state.isPlaying, play, pause])\n\n  const seek = useCallback((time: number) => {\n    if (audioRef.current) {\n      audioRef.current.currentTime = time\n      setState(prev => ({ ...prev, currentTime: time }))\n    }\n  }, [])\n\n  const setVolume = useCallback((volume: number) => {\n    if (audioRef.current) {\n      audioRef.current.volume = volume\n      setState(prev => ({ ...prev, volume }))\n    }\n  }, [])\n\n  return {\n    state,\n    play,\n    pause,\n    togglePlayPause,\n    seek,\n    setVolume,\n    isReady: !!audioRef.current && state.duration > 0\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAWO,SAAS,eAAe,KAIT;QAJS,EAC7B,QAAQ,EACR,cAAc,EAAE,EAChB,eAAe,EACK,GAJS;;IAK7B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,WAAW;QACX,aAAa;QACb,UAAU;QACV,QAAQ;IACV;IAEA,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,UAAU;YAEf,MAAM,QAAQ,IAAI,MAAM;YACxB,SAAS,OAAO,GAAG;YAEnB,MAAM;iEAAuB;oBAC3B;yEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,UAAU,MAAM,QAAQ,IAAI;4BAC9B,CAAC;;gBACH;;YAEA,MAAM;6DAAmB;oBACvB;qEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,aAAa,MAAM,WAAW;4BAChC,CAAC;;oBAED,kCAAkC;oBAClC,IAAI,YAAY,MAAM,GAAG,KAAK,iBAAiB;wBAC7C,MAAM,mBAAmB,YAAY,SAAS;0FAC5C,CAAA,SAAU,MAAM,WAAW,IAAI,OAAO,SAAS,IAAI,MAAM,WAAW,IAAI,OAAO,OAAO;;wBAExF,IAAI,qBAAqB,CAAC,GAAG;4BAC3B,gBAAgB;wBAClB;oBACF;gBACF;;YAEA,MAAM;wDAAc;oBAClB;gEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,WAAW;gCACX,aAAa;4BACf,CAAC;;gBACH;;YAEA,MAAM;wDAAc,CAAC;oBACnB,QAAQ,KAAK,CAAC,gBAAgB;oBAC9B;gEAAS,CAAA,OAAQ,CAAC;gCAChB,GAAG,IAAI;gCACP,WAAW;4BACb,CAAC;;gBACH;;YAEA,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,SAAS;YAEhC;4CAAO;oBACL,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,KAAK;gBACb;;QACF;mCAAG;QAAC;QAAU;QAAa;KAAgB;IAE3C,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACvB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,IAAI;gBACrB;wDAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;wBAAK,CAAC;;YAChD;QACF;2CAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YACxB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,KAAK;gBACtB;yDAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,WAAW;wBAAM,CAAC;;YACjD;QACF;4CAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAClC,IAAI,MAAM,SAAS,EAAE;gBACnB;YACF,OAAO;gBACL;YACF;QACF;sDAAG;QAAC,MAAM,SAAS;QAAE;QAAM;KAAM;IAEjC,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE,CAAC;YACxB,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,WAAW,GAAG;gBAC/B;wDAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,aAAa;wBAAK,CAAC;;YAClD;QACF;2CAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YAC7B,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,MAAM,GAAG;gBAC1B;6DAAS,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE;wBAAO,CAAC;;YACvC;QACF;gDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,CAAC,CAAC,SAAS,OAAO,IAAI,MAAM,QAAQ,GAAG;IAClD;AACF;GAvHgB", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useTranslation.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { TranslationResult } from '../types'\n\n// Mock translation service - in a real app this would call an API\nconst mockTranslationService = {\n  async translateWord(word: string, fromLang: string, toLang: string): Promise<TranslationResult> {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 800))\n    \n    // Enhanced mock translations with more examples\n    const mockTranslations: Record<string, TranslationResult> = {\n      'plane': {\n        translation: 'avion',\n        pronunciation: 'a-vi-ɔ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'I like riding on planes.',\n          translation: 'J\\'aime voyager en avion.'\n        }\n      },\n      'aircraft': {\n        translation: 'aéronef',\n        pronunciation: 'a-e-ʁo-nɛf',\n        provider: 'mock-translate',\n        example: {\n          original: 'The aircraft landed safely.',\n          translation: 'L\\'aéronef a atterri en sécurité.'\n        }\n      },\n      'huge': {\n        translation: 'énorme',\n        pronunciation: 'e-nɔʁm',\n        provider: 'mock-translate',\n        example: {\n          original: 'That\\'s a huge building.',\n          translation: 'C\\'est un énorme bâtiment.'\n        }\n      },\n      'cloud': {\n        translation: 'nuage',\n        pronunciation: 'ny-aʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'The cloud is white.',\n          translation: 'Le nuage est blanc.'\n        }\n      },\n      'airport': {\n        translation: 'aéroport',\n        pronunciation: 'a-e-ʁo-pɔʁ',\n        provider: 'mock-translate',\n        example: {\n          original: 'We arrived at the airport.',\n          translation: 'Nous sommes arrivés à l\\'aéroport.'\n        }\n      },\n      'seat': {\n        translation: 'siège',\n        pronunciation: 'sjɛʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'Please take a seat.',\n          translation: 'Veuillez prendre un siège.'\n        }\n      },\n      'time': {\n        translation: 'temps',\n        pronunciation: 'tɑ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'What time is it?',\n          translation: 'Quelle heure est-il?'\n        }\n      },\n      'world': {\n        translation: 'monde',\n        pronunciation: 'mɔ̃d',\n        provider: 'mock-translate',\n        example: {\n          original: 'The world is beautiful.',\n          translation: 'Le monde est beau.'\n        }\n      }\n    }\n    \n    const cleanWord = word.toLowerCase().replace(/[.,!?;:]/, '')\n    const translation = mockTranslations[cleanWord]\n    \n    if (translation) {\n      return translation\n    }\n    \n    // Fallback for unknown words\n    return {\n      translation: `[Translation for \"${word}\" not available]`,\n      provider: 'mock-translate',\n      confidence: 0.5\n    }\n  }\n}\n\ninterface UseTranslationState {\n  isLoading: boolean\n  error: string | null\n  lastTranslation: TranslationResult | null\n}\n\nexport function useTranslation() {\n  const [state, setState] = useState<UseTranslationState>({\n    isLoading: false,\n    error: null,\n    lastTranslation: null\n  })\n\n  const translateWord = useCallback(async (\n    word: string, \n    fromLang: string = 'en', \n    toLang: string = 'fr'\n  ): Promise<TranslationResult | null> => {\n    setState(prev => ({\n      ...prev,\n      isLoading: true,\n      error: null\n    }))\n\n    try {\n      const result = await mockTranslationService.translateWord(word, fromLang, toLang)\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        lastTranslation: result\n      }))\n      \n      return result\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Translation failed'\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: errorMessage\n      }))\n      \n      return null\n    }\n  }, [])\n\n  const clearError = useCallback(() => {\n    setState(prev => ({ ...prev, error: null }))\n  }, [])\n\n  return {\n    ...state,\n    translateWord,\n    clearError\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKA,kEAAkE;AAClE,MAAM,yBAAyB;IAC7B,MAAM,eAAc,IAAY,EAAE,QAAgB,EAAE,MAAc;QAChE,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gDAAgD;QAChD,MAAM,mBAAsD;YAC1D,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,YAAY;gBACV,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,WAAW;gBACT,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;QACF;QAEA,MAAM,YAAY,KAAK,WAAW,GAAG,OAAO,CAAC,YAAY;QACzD,MAAM,cAAc,gBAAgB,CAAC,UAAU;QAE/C,IAAI,aAAa;YACf,OAAO;QACT;QAEA,6BAA6B;QAC7B,OAAO;YACL,aAAa,AAAC,qBAAyB,OAAL,MAAK;YACvC,UAAU;YACV,YAAY;QACd;IACF;AACF;AAQO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,WAAW;QACX,OAAO;QACP,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,eAChC;gBACA,4EAAmB,MACnB,0EAAiB;YAEjB;6DAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO;oBACT,CAAC;;YAED,IAAI;gBACF,MAAM,SAAS,MAAM,uBAAuB,aAAa,CAAC,MAAM,UAAU;gBAE1E;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,WAAW;4BACX,iBAAiB;wBACnB,CAAC;;gBAED,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAE9D;iEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,WAAW;4BACX,OAAO;wBACT,CAAC;;gBAED,OAAO;YACT;QACF;oDAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC7B;0DAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO;oBAAK,CAAC;;QAC5C;iDAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA;IACF;AACF;GAlDgB", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/StoryReaderScreen.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { StoryHeader } from '../story/StoryHeader'\nimport { ReadingArea } from '../story/ReadingArea'\nimport { AudioPlayer } from '../story/AudioPlayer'\nimport { PageNavigation } from '../story/PageNavigation'\nimport { ErrorBoundary } from '../ui/ErrorBoundary'\nimport { useAudioPlayer } from '@/lib/hooks/useAudioPlayer'\nimport { useTranslation } from '@/lib/hooks/useTranslation'\nimport { useKeyboardShortcuts } from '@/lib/hooks/useKeyboardShortcuts'\nimport { Story, StoryReaderState, TranslationResult } from '@/lib/types'\n\ninterface StoryReaderScreenProps {\n  story: Story\n}\n\n\n\nexport function StoryReaderScreen({ story }: StoryReaderScreenProps) {\n  const [state, setState] = useState<StoryReaderState>({\n    currentWordIndex: -1,\n    selectedWord: undefined,\n    translationCard: {\n      isVisible: false,\n      word: '',\n      position: { x: 0, y: 0 },\n      translation: undefined,\n      isLoading: false\n    },\n    audioPlayer: {\n      isPlaying: false,\n      currentTime: 0,\n      duration: 0,\n      volume: 1.0\n    }\n  })\n\n  // Handle word highlighting during audio playback\n  const handleWordHighlight = useCallback((wordIndex: number) => {\n    setState(prev => ({\n      ...prev,\n      currentWordIndex: wordIndex\n    }))\n  }, [])\n\n  // Use translation hook\n  const translation = useTranslation()\n\n  // Use audio player hook with real functionality\n  const audioPlayer = useAudioPlayer({\n    audioUrl: story.audioUrl,\n    wordTimings: story.wordTimings,\n    onWordHighlight: handleWordHighlight\n  })\n\n  // Update state when audio player state changes\n  React.useEffect(() => {\n    setState(prev => ({\n      ...prev,\n      audioPlayer: audioPlayer.state\n    }))\n  }, [audioPlayer.state])\n\n  const handleWordClick = useCallback(async (word: string, index: number, event: React.MouseEvent) => {\n    // Close existing translation card if clicking the same word\n    if (state.translationCard.isVisible && state.translationCard.word === word) {\n      setState(prev => ({\n        ...prev,\n        translationCard: { ...prev.translationCard, isVisible: false }\n      }))\n      return\n    }\n\n    // Show loading state\n    setState(prev => ({\n      ...prev,\n      selectedWord: word,\n      translationCard: {\n        isVisible: true,\n        word,\n        position: { x: event.clientX, y: event.clientY },\n        translation: undefined,\n        isLoading: true\n      }\n    }))\n\n    // Get translation using the hook\n    const translationResult = await translation.translateWord(word, 'en', 'fr')\n\n    setState(prev => ({\n      ...prev,\n      translationCard: {\n        ...prev.translationCard,\n        translation: translationResult || undefined,\n        isLoading: false\n      }\n    }))\n  }, [state.translationCard.isVisible, state.translationCard.word, translation])\n\n  const handleTranslationClose = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      translationCard: { ...prev.translationCard, isVisible: false }\n    }))\n  }, [])\n\n  const handlePlayPause = useCallback(() => {\n    audioPlayer.togglePlayPause()\n  }, [audioPlayer])\n\n  const handleSeek = useCallback((time: number) => {\n    audioPlayer.seek(time)\n  }, [audioPlayer])\n\n  const handleVolumeChange = useCallback((volume: number) => {\n    audioPlayer.setVolume(volume)\n  }, [audioPlayer])\n\n  return (\n    <ErrorBoundary>\n      <div className=\"flex flex-col h-screen bg-white\">\n        <StoryHeader\n          title={story.title}\n          chapter={story.chapter}\n          isAudioPlaying={state.audioPlayer.isPlaying}\n          onBookmarkClick={() => console.log('Bookmark clicked')}\n          onMenuClick={() => console.log('Menu clicked')}\n        />\n        \n        <ReadingArea\n          content={story.content}\n          currentWordIndex={state.currentWordIndex}\n          translationCard={state.translationCard}\n          onWordClick={handleWordClick}\n          onTranslationClose={handleTranslationClose}\n          onTranslationPlayAudio={() => console.log('Play translation audio')}\n        />\n        \n        <PageNavigation\n          currentPage={24}\n          totalPages={120}\n          onPreviousPage={() => console.log('Previous page')}\n          onNextPage={() => console.log('Next page')}\n        />\n        \n        <AudioPlayer\n          state={state.audioPlayer}\n          onPlayPause={handlePlayPause}\n          onSeek={handleSeek}\n          onVolumeChange={handleVolumeChange}\n        />\n      </div>\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAmBO,SAAS,kBAAkB,KAAiC;QAAjC,EAAE,KAAK,EAA0B,GAAjC;;IAChC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,kBAAkB,CAAC;QACnB,cAAc;QACd,iBAAiB;YACf,WAAW;YACX,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAG,GAAG;YAAE;YACvB,aAAa;YACb,WAAW;QACb;QACA,aAAa;YACX,WAAW;YACX,aAAa;YACb,UAAU;YACV,QAAQ;QACV;IACF;IAEA,iDAAiD;IACjD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACvC;sEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,kBAAkB;oBACpB,CAAC;;QACH;6DAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,iBAAc,AAAD;IAEjC,gDAAgD;IAChD,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,iBAAiB;IACnB;IAEA,+CAA+C;IAC/C,6JAAA,CAAA,UAAK,CAAC,SAAS;uCAAC;YACd;+CAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,aAAa,YAAY,KAAK;oBAChC,CAAC;;QACH;sCAAG;QAAC,YAAY,KAAK;KAAC;IAEtB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE,OAAO,MAAc,OAAe;YACtE,4DAA4D;YAC5D,IAAI,MAAM,eAAe,CAAC,SAAS,IAAI,MAAM,eAAe,CAAC,IAAI,KAAK,MAAM;gBAC1E;sEAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,iBAAiB;gCAAE,GAAG,KAAK,eAAe;gCAAE,WAAW;4BAAM;wBAC/D,CAAC;;gBACD;YACF;YAEA,qBAAqB;YACrB;kEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,cAAc;wBACd,iBAAiB;4BACf,WAAW;4BACX;4BACA,UAAU;gCAAE,GAAG,MAAM,OAAO;gCAAE,GAAG,MAAM,OAAO;4BAAC;4BAC/C,aAAa;4BACb,WAAW;wBACb;oBACF,CAAC;;YAED,iCAAiC;YACjC,MAAM,oBAAoB,MAAM,YAAY,aAAa,CAAC,MAAM,MAAM;YAEtE;kEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;4BACf,GAAG,KAAK,eAAe;4BACvB,aAAa,qBAAqB;4BAClC,WAAW;wBACb;oBACF,CAAC;;QACH;yDAAG;QAAC,MAAM,eAAe,CAAC,SAAS;QAAE,MAAM,eAAe,CAAC,IAAI;QAAE;KAAY;IAE7E,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE;YACzC;yEAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,iBAAiB;4BAAE,GAAG,KAAK,eAAe;4BAAE,WAAW;wBAAM;oBAC/D,CAAC;;QACH;gEAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YAClC,YAAY,eAAe;QAC7B;yDAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YAC9B,YAAY,IAAI,CAAC;QACnB;oDAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACtC,YAAY,SAAS,CAAC;QACxB;4DAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC,4IAAA,CAAA,gBAAa;kBACZ,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,KAAK;oBAClB,SAAS,MAAM,OAAO;oBACtB,gBAAgB,MAAM,WAAW,CAAC,SAAS;oBAC3C,iBAAiB,IAAM,QAAQ,GAAG,CAAC;oBACnC,aAAa,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGjC,6LAAC,6IAAA,CAAA,cAAW;oBACV,SAAS,MAAM,OAAO;oBACtB,kBAAkB,MAAM,gBAAgB;oBACxC,iBAAiB,MAAM,eAAe;oBACtC,aAAa;oBACb,oBAAoB;oBACpB,wBAAwB,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAG5C,6LAAC,gJAAA,CAAA,iBAAc;oBACb,aAAa;oBACb,YAAY;oBACZ,gBAAgB,IAAM,QAAQ,GAAG,CAAC;oBAClC,YAAY,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGhC,6LAAC,6IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,WAAW;oBACxB,aAAa;oBACb,QAAQ;oBACR,gBAAgB;;;;;;;;;;;;;;;;;AAK1B;GAxIgB;;QA4BM,wIAAA,CAAA,iBAAc;QAGd,wIAAA,CAAA,iBAAc;;;KA/BpB", "debugId": null}}]}