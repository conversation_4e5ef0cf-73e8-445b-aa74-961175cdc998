{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/types.ts"], "sourcesContent": ["import { z } from 'zod'\n\nexport const StorySchema = z.object({\n  id: z.string(),\n  title: z.string(),\n  chapter: z.string().optional(),\n  language: z.string(),\n  content: z.string(),\n  difficulty: z.enum(['beginner', 'intermediate', 'advanced']),\n  audioUrl: z.string().optional(),\n  wordTimings: z.array(z.object({\n    word: z.string(),\n    startTime: z.number(),\n    endTime: z.number(),\n    confidence: z.number().optional()\n  })).optional()\n})\n\nexport const TranslationResultSchema = z.object({\n  translation: z.string(),\n  pronunciation: z.string().optional(),\n  confidence: z.number().optional(),\n  provider: z.string(),\n  example: z.object({\n    original: z.string(),\n    translation: z.string()\n  }).optional()\n})\n\nexport type Story = z.infer<typeof StorySchema>\nexport type WordTiming = NonNullable<Story['wordTimings']>[0]\nexport type TranslationResult = z.infer<typeof TranslationResultSchema>\n\n// UI State Types\nexport interface AudioPlayerState {\n  isPlaying: boolean\n  currentTime: number\n  duration: number\n  volume: number\n}\n\nexport interface TranslationCardState {\n  isVisible: boolean\n  word: string\n  position: { x: number; y: number }\n  translation?: TranslationResult\n  isLoading: boolean\n}\n\nexport interface StoryReaderState {\n  currentWordIndex: number\n  selectedWord?: string\n  translationCard: TranslationCardState\n  audioPlayer: AudioPlayerState\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,cAAc,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,IAAI,+KAAA,CAAA,IAAC,CAAC,MAAM;IACZ,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM;IACf,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM;IAClB,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM;IACjB,YAAY,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAgB;KAAW;IAC3D,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,aAAa,+KAAA,CAAA,IAAC,CAAC,KAAK,CAAC,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAC5B,MAAM,+KAAA,CAAA,IAAC,CAAC,MAAM;QACd,WAAW,+KAAA,CAAA,IAAC,CAAC,MAAM;QACnB,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM;QACjB,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,IAAI,QAAQ;AACd;AAEO,MAAM,0BAA0B,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM;IACrB,eAAe,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,YAAY,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM;IAClB,SAAS,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QAChB,UAAU,+KAAA,CAAA,IAAC,CAAC,MAAM;QAClB,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM;IACvB,GAAG,QAAQ;AACb", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/services/tts-service.ts"], "sourcesContent": ["// Text-to-Speech Service\n// This service handles audio generation and word alignment\n\nexport interface TTSOptions {\n  voice?: string\n  speed?: number\n  pitch?: number\n  language?: string\n}\n\nexport interface AudioGenerationResult {\n  audioUrl: string\n  duration: number\n  wordTimings?: Array<{\n    word: string\n    startTime: number\n    endTime: number\n    confidence?: number\n  }>\n}\n\nclass TTSService {\n  private baseUrl = '/api/tts'\n\n  async generateAudio(\n    text: string, \n    options: TTSOptions = {}\n  ): Promise<AudioGenerationResult> {\n    const response = await fetch(`${this.baseUrl}/generate`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        text,\n        ...options\n      })\n    })\n\n    if (!response.ok) {\n      throw new Error(`TTS generation failed: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  async getAvailableVoices(language?: string): Promise<Array<{\n    id: string\n    name: string\n    language: string\n    gender: 'male' | 'female' | 'neutral'\n  }>> {\n    const params = language ? `?language=${language}` : ''\n    const response = await fetch(`${this.baseUrl}/voices${params}`)\n    \n    if (!response.ok) {\n      throw new Error(`Failed to fetch voices: ${response.statusText}`)\n    }\n\n    return response.json()\n  }\n\n  // For development: create a mock audio file with estimated timings\n  async generateMockAudio(text: string): Promise<AudioGenerationResult> {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 1000))\n\n    // Create estimated word timings (roughly 0.5 seconds per word)\n    const words = text.split(/\\s+/)\n    let currentTime = 0\n    const wordTimings = words.map(word => {\n      const duration = Math.max(0.3, word.length * 0.1) // Minimum 0.3s, scale with word length\n      const timing = {\n        word,\n        startTime: currentTime,\n        endTime: currentTime + duration,\n        confidence: 0.8\n      }\n      currentTime += duration + 0.1 // Small pause between words\n      return timing\n    })\n\n    // For now, return a mock result with Web Speech API synthesis\n    return {\n      audioUrl: await this.createWebSpeechAudio(text),\n      duration: currentTime,\n      wordTimings\n    }\n  }\n\n  private async createWebSpeechAudio(text: string): Promise<string> {\n    return new Promise((resolve, reject) => {\n      if (!('speechSynthesis' in window)) {\n        reject(new Error('Speech synthesis not supported'))\n        return\n      }\n\n      const utterance = new SpeechSynthesisUtterance(text)\n      utterance.rate = 0.8\n      utterance.pitch = 1.0\n      utterance.volume = 1.0\n\n      // Try to use a French voice if available\n      const voices = speechSynthesis.getVoices()\n      const frenchVoice = voices.find(voice => \n        voice.lang.startsWith('fr') || voice.name.toLowerCase().includes('french')\n      )\n      if (frenchVoice) {\n        utterance.voice = frenchVoice\n        utterance.lang = 'fr-FR'\n      }\n\n      // Create a data URL for the audio (this is a simplified approach)\n      // In a real implementation, you'd record the audio and create a blob\n      utterance.onend = () => {\n        // For now, return a placeholder URL\n        // In production, this would be a real audio file URL\n        resolve('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')\n      }\n\n      utterance.onerror = (event) => {\n        reject(new Error(`Speech synthesis failed: ${event.error}`))\n      }\n\n      speechSynthesis.speak(utterance)\n    })\n  }\n}\n\nexport const ttsService = new TTSService()\n"], "names": [], "mappings": "AAAA,yBAAyB;AACzB,2DAA2D;;;;AAoB3D,MAAM;IACI,UAAU,WAAU;IAE5B,MAAM,cACJ,IAAY,EACZ,UAAsB,CAAC,CAAC,EACQ;QAChC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,GAAG,OAAO;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAmB,QAAiB,EAKtC;QACF,MAAM,SAAS,WAAW,CAAC,UAAU,EAAE,UAAU,GAAG;QACpD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ;QAE9D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;QAClE;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,mEAAmE;IACnE,MAAM,kBAAkB,IAAY,EAAkC;QACpE,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,+DAA+D;QAC/D,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,IAAI,cAAc;QAClB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAA;YAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,MAAM,GAAG,KAAK,uCAAuC;;YACzF,MAAM,SAAS;gBACb;gBACA,WAAW;gBACX,SAAS,cAAc;gBACvB,YAAY;YACd;YACA,eAAe,WAAW,KAAI,4BAA4B;YAC1D,OAAO;QACT;QAEA,8DAA8D;QAC9D,OAAO;YACL,UAAU,MAAM,IAAI,CAAC,oBAAoB,CAAC;YAC1C,UAAU;YACV;QACF;IACF;IAEA,MAAc,qBAAqB,IAAY,EAAmB;QAChE,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,CAAC,qBAAqB,MAAM,GAAG;gBAClC,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,MAAM,YAAY,IAAI,yBAAyB;YAC/C,UAAU,IAAI,GAAG;YACjB,UAAU,KAAK,GAAG;YAClB,UAAU,MAAM,GAAG;YAEnB,yCAAyC;YACzC,MAAM,SAAS,gBAAgB,SAAS;YACxC,MAAM,cAAc,OAAO,IAAI,CAAC,CAAA,QAC9B,MAAM,IAAI,CAAC,UAAU,CAAC,SAAS,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;YAEnE,IAAI,aAAa;gBACf,UAAU,KAAK,GAAG;gBAClB,UAAU,IAAI,GAAG;YACnB;YAEA,kEAAkE;YAClE,qEAAqE;YACrE,UAAU,KAAK,GAAG;gBAChB,oCAAoC;gBACpC,qDAAqD;gBACrD,QAAQ;YACV;YAEA,UAAU,OAAO,GAAG,CAAC;gBACnB,OAAO,IAAI,MAAM,CAAC,yBAAyB,EAAE,MAAM,KAAK,EAAE;YAC5D;YAEA,gBAAgB,KAAK,CAAC;QACxB;IACF;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/services/story-processor.ts"], "sourcesContent": ["import { Story, WordTiming } from '../types'\nimport { ttsService, TTSOptions } from './tts-service'\n\nexport interface ProcessStoryOptions {\n  voice?: string\n  speed?: number\n  pitch?: number\n  language?: string\n  generateAudio?: boolean\n}\n\nexport interface ProcessedStory extends Story {\n  audioUrl: string\n  wordTimings: WordTiming[]\n  processingMetadata: {\n    processedAt: string\n    audioGenerated: boolean\n    voice?: string\n    duration: number\n  }\n}\n\nclass StoryProcessor {\n  async processStory(\n    story: Omit<Story, 'audioUrl' | 'wordTimings'>, \n    options: ProcessStoryOptions = {}\n  ): Promise<ProcessedStory> {\n    const {\n      voice,\n      speed = 1.0,\n      pitch = 1.0,\n      language = 'fr-FR', // Default to French for language learning\n      generateAudio = true\n    } = options\n\n    console.log(`Processing story: ${story.title}`)\n\n    let audioUrl = ''\n    let wordTimings: WordTiming[] = []\n    let duration = 0\n\n    if (generateAudio) {\n      try {\n        console.log('Generating audio...')\n        const audioResult = await ttsService.generateMockAudio(story.content)\n        \n        audioUrl = audioResult.audioUrl\n        wordTimings = audioResult.wordTimings || []\n        duration = audioResult.duration\n\n        console.log(`Audio generated: ${duration}s, ${wordTimings.length} word timings`)\n      } catch (error) {\n        console.error('Audio generation failed:', error)\n        // Continue without audio\n        audioUrl = ''\n        wordTimings = this.createFallbackTimings(story.content)\n        duration = this.estimateDuration(story.content)\n      }\n    } else {\n      // Create estimated timings without audio\n      wordTimings = this.createFallbackTimings(story.content)\n      duration = this.estimateDuration(story.content)\n    }\n\n    return {\n      ...story,\n      audioUrl,\n      wordTimings,\n      processingMetadata: {\n        processedAt: new Date().toISOString(),\n        audioGenerated: generateAudio && audioUrl !== '',\n        voice,\n        duration\n      }\n    }\n  }\n\n  private createFallbackTimings(text: string): WordTiming[] {\n    const words = text.split(/\\s+/)\n    let currentTime = 0\n    \n    return words.map(word => {\n      const cleanWord = word.replace(/[.,!?;:]/, '')\n      const duration = Math.max(0.3, cleanWord.length * 0.08)\n      \n      const timing: WordTiming = {\n        word: cleanWord,\n        startTime: parseFloat(currentTime.toFixed(2)),\n        endTime: parseFloat((currentTime + duration).toFixed(2)),\n        confidence: 0.7 // Lower confidence for estimated timings\n      }\n      \n      currentTime += duration + 0.1\n      return timing\n    })\n  }\n\n  private estimateDuration(text: string): number {\n    // Rough estimation: average reading speed is about 200 words per minute\n    // For language learning, we slow it down to about 120 words per minute\n    const words = text.split(/\\s+/).length\n    const wordsPerMinute = 120\n    return (words / wordsPerMinute) * 60\n  }\n\n  async reprocessStoryAudio(\n    story: Story, \n    options: TTSOptions = {}\n  ): Promise<ProcessedStory> {\n    console.log(`Reprocessing audio for story: ${story.title}`)\n    \n    try {\n      const audioResult = await ttsService.generateMockAudio(story.content)\n      \n      return {\n        ...story,\n        audioUrl: audioResult.audioUrl,\n        wordTimings: audioResult.wordTimings || story.wordTimings || [],\n        processingMetadata: {\n          processedAt: new Date().toISOString(),\n          audioGenerated: true,\n          voice: options.voice,\n          duration: audioResult.duration\n        }\n      }\n    } catch (error) {\n      console.error('Audio reprocessing failed:', error)\n      throw new Error(`Failed to reprocess audio: ${error instanceof Error ? error.message : 'Unknown error'}`)\n    }\n  }\n\n  validateStory(story: Partial<Story>): string[] {\n    const errors: string[] = []\n\n    if (!story.title || story.title.trim().length === 0) {\n      errors.push('Title is required')\n    }\n\n    if (!story.content || story.content.trim().length === 0) {\n      errors.push('Content is required')\n    }\n\n    if (story.content && story.content.length > 10000) {\n      errors.push('Content is too long (maximum 10,000 characters)')\n    }\n\n    if (!story.language || story.language.trim().length === 0) {\n      errors.push('Language is required')\n    }\n\n    if (!story.difficulty || !['beginner', 'intermediate', 'advanced'].includes(story.difficulty)) {\n      errors.push('Valid difficulty level is required (beginner, intermediate, or advanced)')\n    }\n\n    return errors\n  }\n}\n\nexport const storyProcessor = new StoryProcessor()\n"], "names": [], "mappings": ";;;AACA;;AAqBA,MAAM;IACJ,MAAM,aACJ,KAA8C,EAC9C,UAA+B,CAAC,CAAC,EACR;QACzB,MAAM,EACJ,KAAK,EACL,QAAQ,GAAG,EACX,QAAQ,GAAG,EACX,WAAW,OAAO,EAClB,gBAAgB,IAAI,EACrB,GAAG;QAEJ,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,MAAM,KAAK,EAAE;QAE9C,IAAI,WAAW;QACf,IAAI,cAA4B,EAAE;QAClC,IAAI,WAAW;QAEf,IAAI,eAAe;YACjB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc,MAAM,0IAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,MAAM,OAAO;gBAEpE,WAAW,YAAY,QAAQ;gBAC/B,cAAc,YAAY,WAAW,IAAI,EAAE;gBAC3C,WAAW,YAAY,QAAQ;gBAE/B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,GAAG,EAAE,YAAY,MAAM,CAAC,aAAa,CAAC;YACjF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,yBAAyB;gBACzB,WAAW;gBACX,cAAc,IAAI,CAAC,qBAAqB,CAAC,MAAM,OAAO;gBACtD,WAAW,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO;YAChD;QACF,OAAO;YACL,yCAAyC;YACzC,cAAc,IAAI,CAAC,qBAAqB,CAAC,MAAM,OAAO;YACtD,WAAW,IAAI,CAAC,gBAAgB,CAAC,MAAM,OAAO;QAChD;QAEA,OAAO;YACL,GAAG,KAAK;YACR;YACA;YACA,oBAAoB;gBAClB,aAAa,IAAI,OAAO,WAAW;gBACnC,gBAAgB,iBAAiB,aAAa;gBAC9C;gBACA;YACF;QACF;IACF;IAEQ,sBAAsB,IAAY,EAAgB;QACxD,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,IAAI,cAAc;QAElB,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,MAAM,YAAY,KAAK,OAAO,CAAC,YAAY;YAC3C,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,UAAU,MAAM,GAAG;YAElD,MAAM,SAAqB;gBACzB,MAAM;gBACN,WAAW,WAAW,YAAY,OAAO,CAAC;gBAC1C,SAAS,WAAW,CAAC,cAAc,QAAQ,EAAE,OAAO,CAAC;gBACrD,YAAY,IAAI,yCAAyC;YAC3D;YAEA,eAAe,WAAW;YAC1B,OAAO;QACT;IACF;IAEQ,iBAAiB,IAAY,EAAU;QAC7C,wEAAwE;QACxE,uEAAuE;QACvE,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,MAAM;QACtC,MAAM,iBAAiB;QACvB,OAAO,AAAC,QAAQ,iBAAkB;IACpC;IAEA,MAAM,oBACJ,KAAY,EACZ,UAAsB,CAAC,CAAC,EACC;QACzB,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,MAAM,KAAK,EAAE;QAE1D,IAAI;YACF,MAAM,cAAc,MAAM,0IAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,MAAM,OAAO;YAEpE,OAAO;gBACL,GAAG,KAAK;gBACR,UAAU,YAAY,QAAQ;gBAC9B,aAAa,YAAY,WAAW,IAAI,MAAM,WAAW,IAAI,EAAE;gBAC/D,oBAAoB;oBAClB,aAAa,IAAI,OAAO,WAAW;oBACnC,gBAAgB;oBAChB,OAAO,QAAQ,KAAK;oBACpB,UAAU,YAAY,QAAQ;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;QAC1G;IACF;IAEA,cAAc,KAAqB,EAAY;QAC7C,MAAM,SAAmB,EAAE;QAE3B,IAAI,CAAC,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACnD,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACvD,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO;YACjD,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;YACzD,OAAO,IAAI,CAAC;QACd;QAEA,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC;YAAC;YAAY;YAAgB;SAAW,CAAC,QAAQ,CAAC,MAAM,UAAU,GAAG;YAC7F,OAAO,IAAI,CAAC;QACd;QAEA,OAAO;IACT;AACF;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 330, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/app/api/stories/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { z } from 'zod'\nimport { StorySchema } from '@/lib/types'\nimport { storyProcessor } from '@/lib/services/story-processor'\nimport fs from 'fs/promises'\nimport path from 'path'\n\nconst CreateStorySchema = StorySchema.omit({ \n  id: true, \n  audioUrl: true, \n  wordTimings: true \n}).extend({\n  generateAudio: z.boolean().optional().default(true),\n  voice: z.string().optional(),\n  speed: z.number().min(0.1).max(2.0).optional(),\n  pitch: z.number().min(0.1).max(2.0).optional()\n})\n\n// Get all stories\nexport async function GET() {\n  try {\n    const storiesPath = path.join(process.cwd(), 'src/data/stories.json')\n    const storiesData = await fs.readFile(storiesPath, 'utf-8')\n    const stories = JSON.parse(storiesData)\n    \n    return NextResponse.json(stories)\n  } catch (error) {\n    console.error('Error reading stories:', error)\n    return NextResponse.json(\n      { error: 'Failed to load stories' }, \n      { status: 500 }\n    )\n  }\n}\n\n// Create new story\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const storyData = CreateStorySchema.parse(body)\n\n    // Generate unique ID\n    const id = `story-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n\n    // Validate story content\n    const validationErrors = storyProcessor.validateStory(storyData)\n    if (validationErrors.length > 0) {\n      return NextResponse.json(\n        { error: 'Validation failed', details: validationErrors }, \n        { status: 400 }\n      )\n    }\n\n    // Process the story (generate audio, word timings, etc.)\n    const processedStory = await storyProcessor.processStory(\n      { ...storyData, id },\n      {\n        generateAudio: storyData.generateAudio,\n        voice: storyData.voice,\n        speed: storyData.speed,\n        pitch: storyData.pitch,\n        language: storyData.language\n      }\n    )\n\n    // Load existing stories\n    const storiesPath = path.join(process.cwd(), 'src/data/stories.json')\n    let stories = []\n    \n    try {\n      const storiesData = await fs.readFile(storiesPath, 'utf-8')\n      stories = JSON.parse(storiesData)\n    } catch (error) {\n      // File doesn't exist or is empty, start with empty array\n      console.log('Creating new stories file')\n    }\n\n    // Add new story\n    stories.push(processedStory)\n\n    // Save back to file\n    await fs.writeFile(storiesPath, JSON.stringify(stories, null, 2))\n\n    return NextResponse.json(processedStory, { status: 201 })\n  } catch (error) {\n    console.error('Story creation error:', error)\n    \n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Invalid story data', details: error.errors }, \n        { status: 400 }\n      )\n    }\n    \n    return NextResponse.json(\n      { error: 'Failed to create story' }, \n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,oBAAoB,qHAAA,CAAA,cAAW,CAAC,IAAI,CAAC;IACzC,IAAI;IACJ,UAAU;IACV,aAAa;AACf,GAAG,MAAM,CAAC;IACR,eAAe,+KAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;IAC9C,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ;IAC5C,OAAO,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ;AAC9C;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC7C,MAAM,cAAc,MAAM,qHAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,aAAa;QACnD,MAAM,UAAU,KAAK,KAAK,CAAC;QAE3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,YAAY,kBAAkB,KAAK,CAAC;QAE1C,qBAAqB;QACrB,MAAM,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAE3E,yBAAyB;QACzB,MAAM,mBAAmB,8IAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;QACtD,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAqB,SAAS;YAAiB,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,yDAAyD;QACzD,MAAM,iBAAiB,MAAM,8IAAA,CAAA,iBAAc,CAAC,YAAY,CACtD;YAAE,GAAG,SAAS;YAAE;QAAG,GACnB;YACE,eAAe,UAAU,aAAa;YACtC,OAAO,UAAU,KAAK;YACtB,OAAO,UAAU,KAAK;YACtB,OAAO,UAAU,KAAK;YACtB,UAAU,UAAU,QAAQ;QAC9B;QAGF,wBAAwB;QACxB,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC7C,IAAI,UAAU,EAAE;QAEhB,IAAI;YACF,MAAM,cAAc,MAAM,qHAAA,CAAA,UAAE,CAAC,QAAQ,CAAC,aAAa;YACnD,UAAU,KAAK,KAAK,CAAC;QACvB,EAAE,OAAO,OAAO;YACd,yDAAyD;YACzD,QAAQ,GAAG,CAAC;QACd;QAEA,gBAAgB;QAChB,QAAQ,IAAI,CAAC;QAEb,oBAAoB;QACpB,MAAM,qHAAA,CAAA,UAAE,CAAC,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,SAAS,MAAM;QAE9D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,gBAAgB;YAAE,QAAQ;QAAI;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAsB,SAAS,MAAM,MAAM;YAAC,GACrD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}