{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Typography.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface TypographyProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport const Typography = {\n  Title: ({ children, className = '' }: TypographyProps) => (\n    <h1 className={`text-2xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </h1>\n  ),\n  \n  Subtitle: ({ children, className = '' }: TypographyProps) => (\n    <h2 className={`text-base text-gray-600 ${className}`}>\n      {children}\n    </h2>\n  ),\n  \n  Body: ({ children, className = '' }: TypographyProps) => (\n    <p className={`text-lg text-gray-800 leading-relaxed ${className}`}>\n      {children}\n    </p>\n  ),\n  \n  Caption: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-sm text-gray-500 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationWord: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationText: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-base text-gray-700 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  Micro: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xs text-gray-400 ${className}`}>\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,aAAa;IACxB,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACnD,8OAAC;YAAG,WAAW,CAAC,iCAAiC,EAAE,WAAW;sBAC3D;;;;;;IAIL,UAAU,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACtD,8OAAC;YAAG,WAAW,CAAC,wBAAwB,EAAE,WAAW;sBAClD;;;;;;IAIL,MAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAClD,8OAAC;YAAE,WAAW,CAAC,sCAAsC,EAAE,WAAW;sBAC/D;;;;;;IAIL,SAAS,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACrD,8OAAC;YAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW;sBAClD;;;;;;IAIL,iBAAiB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAC7D,8OAAC;YAAK,WAAW,CAAC,gCAAgC,EAAE,WAAW;sBAC5D;;;;;;IAIL,iBAAiB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAC7D,8OAAC;YAAK,WAAW,CAAC,wBAAwB,EAAE,WAAW;sBACpD;;;;;;IAIL,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACnD,8OAAC;YAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW;sBAClD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/IconButton.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface IconButtonProps {\n  icon: React.ReactNode\n  onClick: () => void\n  variant?: 'default' | 'ghost' | 'outline'\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  disabled?: boolean\n  'aria-label'?: string\n}\n\nexport function IconButton({\n  icon,\n  onClick,\n  variant = 'default',\n  size = 'md',\n  className = '',\n  disabled = false,\n  'aria-label': ariaLabel,\n}: IconButtonProps) {\n  const baseClasses = 'inline-flex items-center justify-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'\n  \n  const variantClasses = {\n    default: 'bg-gray-100 hover:bg-gray-200 text-gray-700',\n    ghost: 'hover:bg-gray-100 text-gray-600',\n    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'\n  }\n  \n  const sizeClasses = {\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-10 h-10 text-base',\n    lg: 'w-12 h-12 text-lg'\n  }\n  \n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      aria-label={ariaLabel}\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}\n    >\n      {icon}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAYO,SAAS,WAAW,EACzB,IAAI,EACJ,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,cAAc,SAAS,EACP;IAChB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,cAAY;QACZ,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;kBAEvF;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/StoryHeader.tsx"], "sourcesContent": ["import React from 'react'\nimport { BookmarkIcon, HamburgerMenuIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\n\ninterface StoryHeaderProps {\n  title: string\n  chapter?: string\n  isAudioPlaying?: boolean\n  onBookmarkClick: () => void\n  onMenuClick: () => void\n}\n\nexport function StoryHeader({ title, chapter, isAudioPlaying, onBookmarkClick, onMenuClick }: StoryHeaderProps) {\n  return (\n    <header className=\"flex items-center justify-between p-4 bg-white border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-2\">\n          <Typography.Title className=\"text-xl font-bold text-gray-900\">\n            {title}\n          </Typography.Title>\n          {isAudioPlaying && (\n            <div className=\"flex space-x-1\">\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\"></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n            </div>\n          )}\n        </div>\n        {chapter && (\n          <Typography.Subtitle className=\"text-sm text-gray-600 mt-1\">\n            {chapter}\n          </Typography.Subtitle>\n        )}\n      </div>\n      \n      <div className=\"flex items-center space-x-2\">\n        <IconButton\n          icon={<BookmarkIcon className=\"w-5 h-5\" />}\n          onClick={onBookmarkClick}\n          variant=\"ghost\"\n          aria-label=\"Bookmark story\"\n        />\n        <IconButton\n          icon={<HamburgerMenuIcon className=\"w-5 h-5\" />}\n          onClick={onMenuClick}\n          variant=\"ghost\"\n          aria-label=\"Open menu\"\n        />\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAUO,SAAS,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAoB;IAC5G,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB;;;;;;4BAEF,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC3F,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;oBAIhG,yBACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,QAAQ;wBAAC,WAAU;kCAC5B;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;kCAEb,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;wBACnC,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;;;;;;;;;;;;;AAKrB", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/WordHighlighter.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface WordHighlighterProps {\n  text: string\n  currentWordIndex?: number\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n}\n\nexport function WordHighlighter({ text, currentWordIndex, onWordClick }: WordHighlighterProps) {\n  const words = text.split(/(\\s+)/)\n  let wordIndex = 0\n\n  return (\n    <div className=\"text-lg leading-relaxed text-gray-800 select-none\">\n      {words.map((segment, segmentIndex) => {\n        // Skip whitespace segments\n        if (/^\\s+$/.test(segment)) {\n          return <span key={segmentIndex}>{segment}</span>\n        }\n\n        const isCurrentWord = wordIndex === currentWordIndex\n        const currentIndex = wordIndex\n        wordIndex++\n\n        return (\n          <span\n            key={segmentIndex}\n            className={`cursor-pointer transition-all duration-300 hover:bg-blue-100 px-1 py-0.5 rounded ${\n              isCurrentWord\n                ? 'bg-blue-300 text-blue-900 font-semibold shadow-sm scale-105'\n                : 'hover:scale-102'\n            }`}\n            onClick={(event) => onWordClick(segment, currentIndex, event)}\n          >\n            {segment}\n          </span>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,gBAAgB,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAwB;IAC3F,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,YAAY;IAEhB,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,SAAS;YACnB,2BAA2B;YAC3B,IAAI,QAAQ,IAAI,CAAC,UAAU;gBACzB,qBAAO,8OAAC;8BAAyB;mBAAf;;;;;YACpB;YAEA,MAAM,gBAAgB,cAAc;YACpC,MAAM,eAAe;YACrB;YAEA,qBACE,8OAAC;gBAEC,WAAW,CAAC,iFAAiF,EAC3F,gBACI,gEACA,mBACJ;gBACF,SAAS,CAAC,QAAU,YAAY,SAAS,cAAc;0BAEtD;eARI;;;;;QAWX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/TranslationCard.tsx"], "sourcesContent": ["import React from 'react'\nimport { Cross2Icon, SpeakerLoudIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { TranslationResult } from '@/lib/types'\n\ninterface TranslationCardProps {\n  word: string\n  translation?: TranslationResult\n  isLoading?: boolean\n  onPlayAudio: () => void\n  onClose: () => void\n}\n\nexport function TranslationCard({\n  word,\n  translation,\n  isLoading = false,\n  onPlayAudio,\n  onClose\n}: TranslationCardProps) {\n  return (\n    <div className=\"bg-gray-50 rounded-xl p-4 mx-5 mb-6 relative shadow-sm border border-gray-200\">\n      <IconButton\n        icon={<Cross2Icon className=\"w-4 h-4\" />}\n        onClick={onClose}\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"absolute top-2 right-2\"\n        aria-label=\"Close translation\"\n      />\n      \n      <div className=\"flex items-center justify-between mb-3\">\n        <div>\n          <Typography.TranslationWord className=\"text-xl\">\n            {word}\n          </Typography.TranslationWord>\n          {translation?.pronunciation && (\n            <Typography.Caption className=\"block mt-1\">\n              /{translation.pronunciation}/\n            </Typography.Caption>\n          )}\n        </div>\n        <IconButton \n          icon={<SpeakerLoudIcon className=\"w-4 h-4\" />} \n          onClick={onPlayAudio}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Play pronunciation\"\n        />\n      </div>\n      \n      {isLoading ? (\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n          <Typography.Caption className=\"text-gray-400 mt-2\">\n            Translating...\n          </Typography.Caption>\n        </div>\n      ) : translation ? (\n        <>\n          <Typography.TranslationText className=\"block mb-3\">\n            {translation.translation}\n          </Typography.TranslationText>\n          \n          {translation.example && (\n            <div className=\"text-sm text-gray-600 border-t border-gray-200 pt-3\">\n              <Typography.Caption className=\"italic block mb-1\">\n                {translation.example.original}\n              </Typography.Caption>\n              <Typography.Caption className=\"block\">\n                {translation.example.translation}\n              </Typography.Caption>\n            </div>\n          )}\n        </>\n      ) : (\n        <Typography.Caption className=\"text-gray-500\">\n          Translation not available\n        </Typography.Caption>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAWO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,WAAW,EACX,YAAY,KAAK,EACjB,WAAW,EACX,OAAO,EACc;IACrB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAC5B,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,cAAW;;;;;;0BAGb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,eAAe;gCAAC,WAAU;0CACnC;;;;;;4BAEF,aAAa,+BACZ,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;;oCAAa;oCACvC,YAAY,aAAa;oCAAC;;;;;;;;;;;;;kCAIlC,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;wBACjC,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;YAId,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wBAAC,WAAU;kCAAqB;;;;;;;;;;;uBAInD,4BACF;;kCACE,8OAAC,sIAAA,CAAA,aAAU,CAAC,eAAe;wBAAC,WAAU;kCACnC,YAAY,WAAW;;;;;;oBAGzB,YAAY,OAAO,kBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,QAAQ;;;;;;0CAE/B,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,WAAW;;;;;;;;;;;;;6CAMxC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;0BAAgB;;;;;;;;;;;;AAMtD", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/ReadingArea.tsx"], "sourcesContent": ["import React from 'react'\nimport { WordHighlighter } from './WordHighlighter'\nimport { TranslationCard } from './TranslationCard'\nimport { TranslationCardState } from '@/lib/types'\n\ninterface ReadingAreaProps {\n  content: string\n  currentWordIndex?: number\n  translationCard: TranslationCardState\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n  onTranslationClose: () => void\n  onTranslationPlayAudio: () => void\n}\n\nexport function ReadingArea({\n  content,\n  currentWordIndex,\n  translationCard,\n  onWordClick,\n  onTranslationClose,\n  onTranslationPlayAudio\n}: ReadingAreaProps) {\n  return (\n    <div className=\"flex-1 overflow-y-auto\">\n      <div className=\"max-w-sm mx-auto p-6\">\n        <div className=\"mb-6\">\n          <WordHighlighter\n            text={content}\n            currentWordIndex={currentWordIndex}\n            onWordClick={onWordClick}\n          />\n        </div>\n        \n        {translationCard.isVisible && (\n          <TranslationCard\n            word={translationCard.word}\n            translation={translationCard.translation}\n            isLoading={translationCard.isLoading}\n            onPlayAudio={onTranslationPlayAudio}\n            onClose={onTranslationClose}\n          />\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAYO,SAAS,YAAY,EAC1B,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,sBAAsB,EACL;IACjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,kBAAe;wBACd,MAAM;wBACN,kBAAkB;wBAClB,aAAa;;;;;;;;;;;gBAIhB,gBAAgB,SAAS,kBACxB,8OAAC,8IAAA,CAAA,kBAAe;oBACd,MAAM,gBAAgB,IAAI;oBAC1B,aAAa,gBAAgB,WAAW;oBACxC,WAAW,gBAAgB,SAAS;oBACpC,aAAa;oBACb,SAAS;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Slider.tsx"], "sourcesContent": ["import React from 'react'\nimport * as RadixSlider from '@radix-ui/react-slider'\n\ninterface SliderProps {\n  value: number[]\n  onValueChange: (value: number[]) => void\n  max?: number\n  min?: number\n  step?: number\n  className?: string\n  disabled?: boolean\n}\n\nexport function Slider({\n  value,\n  onValueChange,\n  max = 100,\n  min = 0,\n  step = 1,\n  className = '',\n  disabled = false,\n}: SliderProps) {\n  return (\n    <RadixSlider.Root\n      className={`relative flex items-center select-none touch-none w-full h-5 ${className}`}\n      value={value}\n      onValueChange={onValueChange}\n      max={max}\n      min={min}\n      step={step}\n      disabled={disabled}\n    >\n      <RadixSlider.Track className=\"bg-gray-200 relative grow rounded-full h-1\">\n        <RadixSlider.Range className=\"absolute bg-gray-900 rounded-full h-full\" />\n      </RadixSlider.Track>\n      <RadixSlider.Thumb\n        className=\"block w-4 h-4 bg-white border-2 border-gray-900 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n        aria-label=\"Volume\"\n      />\n    </RadixSlider.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAYO,SAAS,OAAO,EACrB,KAAK,EACL,aAAa,EACb,MAAM,GAAG,EACT,MAAM,CAAC,EACP,OAAO,CAAC,EACR,YAAY,EAAE,EACd,WAAW,KAAK,EACJ;IACZ,qBACE,8OAAC,kKAAA,CAAA,OAAgB;QACf,WAAW,CAAC,6DAA6D,EAAE,WAAW;QACtF,OAAO;QACP,eAAe;QACf,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;;0BAEV,8OAAC,kKAAA,CAAA,QAAiB;gBAAC,WAAU;0BAC3B,cAAA,8OAAC,kKAAA,CAAA,QAAiB;oBAAC,WAAU;;;;;;;;;;;0BAE/B,8OAAC,kKAAA,CAAA,QAAiB;gBAChB,WAAU;gBACV,cAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/AudioPlayer.tsx"], "sourcesContent": ["import React from 'react'\nimport { PlayIcon, PauseIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Slider } from '../ui/Slider'\nimport { Typography } from '../ui/Typography'\nimport { AudioPlayerState } from '@/lib/types'\n\ninterface AudioPlayerProps {\n  state: AudioPlayerState\n  onPlayPause: () => void\n  onSeek: (time: number) => void\n  onVolumeChange: (volume: number) => void\n}\n\nfunction formatTime(seconds: number): string {\n  const minutes = Math.floor(seconds / 60)\n  const remainingSeconds = Math.floor(seconds % 60)\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n}\n\nexport function AudioPlayer({ state, onPlayPause, onSeek, onVolumeChange }: AudioPlayerProps) {\n  const { isPlaying, currentTime, duration, volume } = state\n\n  const handleProgressChange = (value: number[]) => {\n    onSeek(value[0])\n  }\n\n  const handleVolumeChange = (value: number[]) => {\n    onVolumeChange(value[0] / 100)\n  }\n\n  return (\n    <div className=\"bg-white border-t border-gray-200 p-4\">\n      {/* Progress Bar */}\n      <div className=\"mb-4\">\n        <Slider\n          value={[currentTime]}\n          onValueChange={handleProgressChange}\n          max={duration}\n          min={0}\n          step={0.1}\n          className=\"w-full\"\n        />\n        <div className=\"flex justify-between mt-2\">\n          <Typography.Micro>\n            {formatTime(currentTime)}\n          </Typography.Micro>\n          <Typography.Micro>\n            {formatTime(duration)}\n          </Typography.Micro>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex items-center justify-center\">\n        <IconButton\n          icon={isPlaying ? <PauseIcon className=\"w-6 h-6\" /> : <PlayIcon className=\"w-6 h-6\" />}\n          onClick={onPlayPause}\n          size=\"lg\"\n          className=\"bg-gray-900 text-white hover:bg-gray-800\"\n          aria-label={isPlaying ? 'Pause' : 'Play'}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAUA,SAAS,WAAW,OAAe;IACjC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;IAC9C,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAoB;IAC1F,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAErD,MAAM,uBAAuB,CAAC;QAC5B,OAAO,KAAK,CAAC,EAAE;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO;4BAAC;yBAAY;wBACpB,eAAe;wBACf,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;0CAEd,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;oBACT,MAAM,0BAAY,8OAAC,gLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;+CAAe,8OAAC,gLAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1E,SAAS;oBACT,MAAK;oBACL,WAAU;oBACV,cAAY,YAAY,UAAU;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/PageNavigation.tsx"], "sourcesContent": ["import React from 'react'\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Typography } from '../ui/Typography'\n\ninterface PageNavigationProps {\n  currentPage: number\n  totalPages: number\n  onPreviousPage: () => void\n  onNextPage: () => void\n}\n\nexport function PageNavigation({ \n  currentPage, \n  totalPages, \n  onPreviousPage, \n  onNextPage \n}: PageNavigationProps) {\n  return (\n    <div className=\"flex items-center justify-between p-4 bg-white border-t border-gray-100\">\n      <IconButton\n        icon={<ChevronLeftIcon className=\"w-5 h-5\" />}\n        onClick={onPreviousPage}\n        variant=\"ghost\"\n        disabled={currentPage <= 1}\n        aria-label=\"Previous page\"\n      />\n      \n      <Typography.Caption className=\"text-gray-500\">\n        Page {currentPage} of {totalPages}\n      </Typography.Caption>\n      \n      <IconButton\n        icon={<ChevronRightIcon className=\"w-5 h-5\" />}\n        onClick={onNextPage}\n        variant=\"ghost\"\n        disabled={currentPage >= totalPages}\n        aria-label=\"Next page\"\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACU;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;gBACjC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;0BAGb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;;oBAAgB;oBACtC;oBAAY;oBAAK;;;;;;;0BAGzB,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAClC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Typography } from './Typography'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>\n}\n\nexport class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback\n      return <FallbackComponent error={this.state.error} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-[200px] p-6 bg-red-50 rounded-lg border border-red-200\">\n      <Typography.Title className=\"text-red-800 mb-2\">\n        Something went wrong\n      </Typography.Title>\n      <Typography.Body className=\"text-red-600 text-center mb-4\">\n        {error?.message || 'An unexpected error occurred'}\n      </Typography.Body>\n      <button\n        onClick={resetError}\n        className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n      >\n        Try again\n      </button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeO,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;IACzD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACjD,qBAAO,8OAAC;gBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;QAChF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAA6C;IAC5F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gBAAC,WAAU;0BAAoB;;;;;;0BAGhD,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,WAAU;0BACxB,OAAO,WAAW;;;;;;0BAErB,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/KeyboardShortcutsHelp.tsx"], "sourcesContent": ["import React, { useState } from 'react'\nimport { QuestionMarkCircledIcon, Cross2Icon } from '@radix-ui/react-icons'\nimport { IconButton } from './IconButton'\nimport { Typography } from './Typography'\n\nexport function KeyboardShortcutsHelp() {\n  const [isVisible, setIsVisible] = useState(false)\n\n  const shortcuts = [\n    { key: 'Space', description: 'Play/Pause audio' },\n    { key: 'Shift + →', description: 'Skip forward 10s' },\n    { key: 'Shift + ←', description: 'Skip backward 10s' },\n    { key: 'Esc', description: 'Close translation' },\n  ]\n\n  if (!isVisible) {\n    return (\n      <IconButton\n        icon={<QuestionMarkCircledIcon className=\"w-4 h-4\" />}\n        onClick={() => setIsVisible(true)}\n        variant=\"ghost\"\n        size=\"sm\"\n        aria-label=\"Show keyboard shortcuts\"\n        className=\"fixed bottom-20 right-4 bg-white shadow-lg border border-gray-200\"\n      />\n    )\n  }\n\n  return (\n    <div className=\"fixed bottom-20 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-xs z-50\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <Typography.Caption className=\"font-semibold text-gray-700\">\n          Keyboard Shortcuts\n        </Typography.Caption>\n        <IconButton\n          icon={<Cross2Icon className=\"w-3 h-3\" />}\n          onClick={() => setIsVisible(false)}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Close shortcuts help\"\n        />\n      </div>\n      \n      <div className=\"space-y-2\">\n        {shortcuts.map((shortcut, index) => (\n          <div key={index} className=\"flex items-center justify-between\">\n            <Typography.Micro className=\"font-mono bg-gray-100 px-2 py-1 rounded text-gray-700\">\n              {shortcut.key}\n            </Typography.Micro>\n            <Typography.Micro className=\"text-gray-600 ml-3\">\n              {shortcut.description}\n            </Typography.Micro>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY;QAChB;YAAE,KAAK;YAAS,aAAa;QAAmB;QAChD;YAAE,KAAK;YAAa,aAAa;QAAmB;QACpD;YAAE,KAAK;YAAa,aAAa;QAAoB;QACrD;YAAE,KAAK;YAAO,aAAa;QAAoB;KAChD;IAED,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC,sIAAA,CAAA,aAAU;YACT,oBAAM,8OAAC,gLAAA,CAAA,0BAAuB;gBAAC,WAAU;;;;;;YACzC,SAAS,IAAM,aAAa;YAC5B,SAAQ;YACR,MAAK;YACL,cAAW;YACX,WAAU;;;;;;IAGhB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wBAAC,WAAU;kCAA8B;;;;;;kCAG5D,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAC5B,SAAS,IAAM,aAAa;wBAC5B,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;0BAIf,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB,SAAS,GAAG;;;;;;0CAEf,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB,SAAS,WAAW;;;;;;;uBALf;;;;;;;;;;;;;;;;AAYpB", "debugId": null}}, {"offset": {"line": 990, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useAudioPlayer.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { AudioPlayerState, WordTiming } from '../types'\n\ninterface UseAudioPlayerProps {\n  audioUrl?: string\n  wordTimings?: WordTiming[]\n  onWordHighlight?: (wordIndex: number) => void\n}\n\nexport function useAudioPlayer({ \n  audioUrl, \n  wordTimings = [], \n  onWordHighlight \n}: UseAudioPlayerProps) {\n  const audioRef = useRef<HTMLAudioElement | null>(null)\n  const [state, setState] = useState<AudioPlayerState>({\n    isPlaying: false,\n    currentTime: 0,\n    duration: 0,\n    volume: 1.0\n  })\n\n  // Initialize audio element\n  useEffect(() => {\n    if (!audioUrl) return\n\n    const audio = new Audio(audioUrl)\n    audioRef.current = audio\n\n    const handleLoadedMetadata = () => {\n      setState(prev => ({\n        ...prev,\n        duration: audio.duration || 0\n      }))\n    }\n\n    const handleTimeUpdate = () => {\n      setState(prev => ({\n        ...prev,\n        currentTime: audio.currentTime\n      }))\n\n      // Find current word based on time\n      if (wordTimings.length > 0 && onWordHighlight) {\n        const currentWordIndex = wordTimings.findIndex(\n          timing => audio.currentTime >= timing.startTime && audio.currentTime <= timing.endTime\n        )\n        if (currentWordIndex !== -1) {\n          onWordHighlight(currentWordIndex)\n        }\n      }\n    }\n\n    const handleEnded = () => {\n      setState(prev => ({\n        ...prev,\n        isPlaying: false,\n        currentTime: 0\n      }))\n    }\n\n    const handleError = (e: Event) => {\n      console.error('Audio error:', e)\n      setState(prev => ({\n        ...prev,\n        isPlaying: false\n      }))\n    }\n\n    audio.addEventListener('loadedmetadata', handleLoadedMetadata)\n    audio.addEventListener('timeupdate', handleTimeUpdate)\n    audio.addEventListener('ended', handleEnded)\n    audio.addEventListener('error', handleError)\n\n    return () => {\n      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)\n      audio.removeEventListener('timeupdate', handleTimeUpdate)\n      audio.removeEventListener('ended', handleEnded)\n      audio.removeEventListener('error', handleError)\n      audio.pause()\n    }\n  }, [audioUrl, wordTimings, onWordHighlight])\n\n  const play = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.play()\n      setState(prev => ({ ...prev, isPlaying: true }))\n    }\n  }, [])\n\n  const pause = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.pause()\n      setState(prev => ({ ...prev, isPlaying: false }))\n    }\n  }, [])\n\n  const togglePlayPause = useCallback(() => {\n    if (state.isPlaying) {\n      pause()\n    } else {\n      play()\n    }\n  }, [state.isPlaying, play, pause])\n\n  const seek = useCallback((time: number) => {\n    if (audioRef.current) {\n      audioRef.current.currentTime = time\n      setState(prev => ({ ...prev, currentTime: time }))\n    }\n  }, [])\n\n  const setVolume = useCallback((volume: number) => {\n    if (audioRef.current) {\n      audioRef.current.volume = volume\n      setState(prev => ({ ...prev, volume }))\n    }\n  }, [])\n\n  return {\n    state,\n    play,\n    pause,\n    togglePlayPause,\n    seek,\n    setVolume,\n    isReady: !!audioRef.current && state.duration > 0\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAWO,SAAS,eAAe,EAC7B,QAAQ,EACR,cAAc,EAAE,EAChB,eAAe,EACK;IACpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,WAAW;QACX,aAAa;QACb,UAAU;QACV,QAAQ;IACV;IAEA,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,QAAQ,IAAI,MAAM;QACxB,SAAS,OAAO,GAAG;QAEnB,MAAM,uBAAuB;YAC3B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,UAAU,MAAM,QAAQ,IAAI;gBAC9B,CAAC;QACH;QAEA,MAAM,mBAAmB;YACvB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,aAAa,MAAM,WAAW;gBAChC,CAAC;YAED,kCAAkC;YAClC,IAAI,YAAY,MAAM,GAAG,KAAK,iBAAiB;gBAC7C,MAAM,mBAAmB,YAAY,SAAS,CAC5C,CAAA,SAAU,MAAM,WAAW,IAAI,OAAO,SAAS,IAAI,MAAM,WAAW,IAAI,OAAO,OAAO;gBAExF,IAAI,qBAAqB,CAAC,GAAG;oBAC3B,gBAAgB;gBAClB;YACF;QACF;QAEA,MAAM,cAAc;YAClB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,aAAa;gBACf,CAAC;QACH;QAEA,MAAM,cAAc,CAAC;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;gBACb,CAAC;QACH;QAEA,MAAM,gBAAgB,CAAC,kBAAkB;QACzC,MAAM,gBAAgB,CAAC,cAAc;QACrC,MAAM,gBAAgB,CAAC,SAAS;QAChC,MAAM,gBAAgB,CAAC,SAAS;QAEhC,OAAO;YACL,MAAM,mBAAmB,CAAC,kBAAkB;YAC5C,MAAM,mBAAmB,CAAC,cAAc;YACxC,MAAM,mBAAmB,CAAC,SAAS;YACnC,MAAM,mBAAmB,CAAC,SAAS;YACnC,MAAM,KAAK;QACb;IACF,GAAG;QAAC;QAAU;QAAa;KAAgB;IAE3C,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,IAAI;YACrB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAK,CAAC;QAChD;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;YACtB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QACjD;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,MAAM,SAAS,EAAE;YACnB;QACF,OAAO;YACL;QACF;IACF,GAAG;QAAC,MAAM,SAAS;QAAE;QAAM;KAAM;IAEjC,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,WAAW,GAAG;YAC/B,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,aAAa;gBAAK,CAAC;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,MAAM,GAAG;YAC1B,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE;gBAAO,CAAC;QACvC;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,CAAC,CAAC,SAAS,OAAO,IAAI,MAAM,QAAQ,GAAG;IAClD;AACF", "debugId": null}}, {"offset": {"line": 1119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useTranslation.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { TranslationResult } from '../types'\n\n// Mock translation service - in a real app this would call an API\nconst mockTranslationService = {\n  async translateWord(word: string, fromLang: string, toLang: string): Promise<TranslationResult> {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 800))\n    \n    // Enhanced mock translations with more examples\n    const mockTranslations: Record<string, TranslationResult> = {\n      'plane': {\n        translation: 'avion',\n        pronunciation: 'a-vi-ɔ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'I like riding on planes.',\n          translation: 'J\\'aime voyager en avion.'\n        }\n      },\n      'aircraft': {\n        translation: 'aéronef',\n        pronunciation: 'a-e-ʁo-nɛf',\n        provider: 'mock-translate',\n        example: {\n          original: 'The aircraft landed safely.',\n          translation: 'L\\'aéronef a atterri en sécurité.'\n        }\n      },\n      'huge': {\n        translation: 'énorme',\n        pronunciation: 'e-nɔʁm',\n        provider: 'mock-translate',\n        example: {\n          original: 'That\\'s a huge building.',\n          translation: 'C\\'est un énorme bâtiment.'\n        }\n      },\n      'cloud': {\n        translation: 'nuage',\n        pronunciation: 'ny-aʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'The cloud is white.',\n          translation: 'Le nuage est blanc.'\n        }\n      },\n      'airport': {\n        translation: 'aéroport',\n        pronunciation: 'a-e-ʁo-pɔʁ',\n        provider: 'mock-translate',\n        example: {\n          original: 'We arrived at the airport.',\n          translation: 'Nous sommes arrivés à l\\'aéroport.'\n        }\n      },\n      'seat': {\n        translation: 'siège',\n        pronunciation: 'sjɛʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'Please take a seat.',\n          translation: 'Veuillez prendre un siège.'\n        }\n      },\n      'time': {\n        translation: 'temps',\n        pronunciation: 'tɑ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'What time is it?',\n          translation: 'Quelle heure est-il?'\n        }\n      },\n      'world': {\n        translation: 'monde',\n        pronunciation: 'mɔ̃d',\n        provider: 'mock-translate',\n        example: {\n          original: 'The world is beautiful.',\n          translation: 'Le monde est beau.'\n        }\n      }\n    }\n    \n    const cleanWord = word.toLowerCase().replace(/[.,!?;:]/, '')\n    const translation = mockTranslations[cleanWord]\n    \n    if (translation) {\n      return translation\n    }\n    \n    // Fallback for unknown words\n    return {\n      translation: `[Translation for \"${word}\" not available]`,\n      provider: 'mock-translate',\n      confidence: 0.5\n    }\n  }\n}\n\ninterface UseTranslationState {\n  isLoading: boolean\n  error: string | null\n  lastTranslation: TranslationResult | null\n}\n\nexport function useTranslation() {\n  const [state, setState] = useState<UseTranslationState>({\n    isLoading: false,\n    error: null,\n    lastTranslation: null\n  })\n\n  const translateWord = useCallback(async (\n    word: string, \n    fromLang: string = 'en', \n    toLang: string = 'fr'\n  ): Promise<TranslationResult | null> => {\n    setState(prev => ({\n      ...prev,\n      isLoading: true,\n      error: null\n    }))\n\n    try {\n      const result = await mockTranslationService.translateWord(word, fromLang, toLang)\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        lastTranslation: result\n      }))\n      \n      return result\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Translation failed'\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: errorMessage\n      }))\n      \n      return null\n    }\n  }, [])\n\n  const clearError = useCallback(() => {\n    setState(prev => ({ ...prev, error: null }))\n  }, [])\n\n  return {\n    ...state,\n    translateWord,\n    clearError\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAKA,kEAAkE;AAClE,MAAM,yBAAyB;IAC7B,MAAM,eAAc,IAAY,EAAE,QAAgB,EAAE,MAAc;QAChE,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gDAAgD;QAChD,MAAM,mBAAsD;YAC1D,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,YAAY;gBACV,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,WAAW;gBACT,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;QACF;QAEA,MAAM,YAAY,KAAK,WAAW,GAAG,OAAO,CAAC,YAAY;QACzD,MAAM,cAAc,gBAAgB,CAAC,UAAU;QAE/C,IAAI,aAAa;YACf,OAAO;QACT;QAEA,6BAA6B;QAC7B,OAAO;YACL,aAAa,CAAC,kBAAkB,EAAE,KAAK,gBAAgB,CAAC;YACxD,UAAU;YACV,YAAY;QACd;IACF;AACF;AAQO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,WAAW;QACX,OAAO;QACP,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAChC,MACA,WAAmB,IAAI,EACvB,SAAiB,IAAI;QAErB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,WAAW;gBACX,OAAO;YACT,CAAC;QAED,IAAI;YACF,MAAM,SAAS,MAAM,uBAAuB,aAAa,CAAC,MAAM,UAAU;YAE1E,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,iBAAiB;gBACnB,CAAC;YAED,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAE9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;YAED,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO;YAAK,CAAC;IAC5C,GAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1264, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useKeyboardShortcuts.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\ninterface KeyboardShortcuts {\n  onPlayPause?: () => void\n  onSeekForward?: () => void\n  onSeekBackward?: () => void\n  onCloseTranslation?: () => void\n}\n\nexport function useKeyboardShortcuts({\n  onPlayPause,\n  onSeekForward,\n  onSeekBackward,\n  onCloseTranslation\n}: KeyboardShortcuts) {\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Don't trigger shortcuts if user is typing in an input\n      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {\n        return\n      }\n\n      switch (event.code) {\n        case 'Space':\n          event.preventDefault()\n          onPlayPause?.()\n          break\n        case 'ArrowRight':\n          if (event.shiftKey) {\n            event.preventDefault()\n            onSeekForward?.()\n          }\n          break\n        case 'ArrowLeft':\n          if (event.shiftKey) {\n            event.preventDefault()\n            onSeekBackward?.()\n          }\n          break\n        case 'Escape':\n          event.preventDefault()\n          onCloseTranslation?.()\n          break\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [onPlayPause, onSeekForward, onSeekBackward, onCloseTranslation])\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAWO,SAAS,qBAAqB,EACnC,WAAW,EACX,aAAa,EACb,cAAc,EACd,kBAAkB,EACA;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,wDAAwD;YACxD,IAAI,MAAM,MAAM,YAAY,oBAAoB,MAAM,MAAM,YAAY,qBAAqB;gBAC3F;YACF;YAEA,OAAQ,MAAM,IAAI;gBAChB,KAAK;oBACH,MAAM,cAAc;oBACpB;oBACA;gBACF,KAAK;oBACH,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,cAAc;wBACpB;oBACF;oBACA;gBACF,KAAK;oBACH,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,cAAc;wBACpB;oBACF;oBACA;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB;oBACA;YACJ;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAa;QAAe;QAAgB;KAAmB;AACrE", "debugId": null}}, {"offset": {"line": 1313, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/services/progress-service.ts"], "sourcesContent": ["// User Progress Tracking Service\n// This service handles user progress, statistics, and learning analytics\n\nexport interface UserProgress {\n  userId: string\n  storyId: string\n  wordsLearned: string[]\n  completionPercentage: number\n  timeSpent: number // in seconds\n  lastAccessedAt: string\n  startedAt: string\n  completedAt?: string\n  statistics: {\n    totalWords: number\n    uniqueWordsEncountered: number\n    translationsRequested: number\n    audioPlayTime: number\n    sessionsCount: number\n  }\n}\n\nexport interface LearningSession {\n  sessionId: string\n  storyId: string\n  startTime: string\n  endTime?: string\n  wordsEncountered: string[]\n  translationsRequested: string[]\n  audioPlayTime: number\n  completionPercentage: number\n}\n\nexport interface ProgressStats {\n  totalStoriesStarted: number\n  totalStoriesCompleted: number\n  totalWordsLearned: number\n  totalTimeSpent: number\n  averageCompletionRate: number\n  streakDays: number\n  lastActiveDate: string\n}\n\nclass ProgressService {\n  private storageKey = 'polistory-progress'\n  private sessionKey = 'polistory-current-session'\n\n  // Get user's overall progress statistics\n  getProgressStats(): ProgressStats {\n    const allProgress = this.getAllProgress()\n    const now = new Date()\n    const today = now.toDateString()\n\n    const totalStoriesStarted = allProgress.length\n    const totalStoriesCompleted = allProgress.filter(p => p.completedAt).length\n    const totalWordsLearned = [...new Set(allProgress.flatMap(p => p.wordsLearned))].length\n    const totalTimeSpent = allProgress.reduce((sum, p) => sum + p.timeSpent, 0)\n    const averageCompletionRate = totalStoriesStarted > 0 \n      ? allProgress.reduce((sum, p) => sum + p.completionPercentage, 0) / totalStoriesStarted \n      : 0\n\n    // Calculate streak (simplified - just check if user was active today)\n    const lastActiveDate = allProgress.length > 0 \n      ? allProgress.reduce((latest, p) => \n          new Date(p.lastAccessedAt) > new Date(latest) ? p.lastAccessedAt : latest\n        , allProgress[0].lastAccessedAt)\n      : today\n\n    const streakDays = new Date(lastActiveDate).toDateString() === today ? 1 : 0\n\n    return {\n      totalStoriesStarted,\n      totalStoriesCompleted,\n      totalWordsLearned,\n      totalTimeSpent,\n      averageCompletionRate,\n      streakDays,\n      lastActiveDate\n    }\n  }\n\n  // Get progress for a specific story\n  getStoryProgress(storyId: string): UserProgress | null {\n    const allProgress = this.getAllProgress()\n    return allProgress.find(p => p.storyId === storyId) || null\n  }\n\n  // Start a new learning session\n  startSession(storyId: string): LearningSession {\n    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`\n    const session: LearningSession = {\n      sessionId,\n      storyId,\n      startTime: new Date().toISOString(),\n      wordsEncountered: [],\n      translationsRequested: [],\n      audioPlayTime: 0,\n      completionPercentage: 0\n    }\n\n    localStorage.setItem(this.sessionKey, JSON.stringify(session))\n    return session\n  }\n\n  // Update current session\n  updateSession(updates: Partial<LearningSession>): void {\n    const currentSession = this.getCurrentSession()\n    if (!currentSession) return\n\n    const updatedSession = { ...currentSession, ...updates }\n    localStorage.setItem(this.sessionKey, JSON.stringify(updatedSession))\n  }\n\n  // End current session and update progress\n  endSession(): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    const endTime = new Date().toISOString()\n    const sessionDuration = new Date(endTime).getTime() - new Date(session.startTime).getTime()\n    const sessionDurationSeconds = Math.floor(sessionDuration / 1000)\n\n    // Update or create progress record\n    let progress = this.getStoryProgress(session.storyId)\n    \n    if (!progress) {\n      progress = {\n        userId: 'default-user', // In a real app, this would be the actual user ID\n        storyId: session.storyId,\n        wordsLearned: [],\n        completionPercentage: 0,\n        timeSpent: 0,\n        lastAccessedAt: endTime,\n        startedAt: session.startTime,\n        statistics: {\n          totalWords: 0,\n          uniqueWordsEncountered: 0,\n          translationsRequested: 0,\n          audioPlayTime: 0,\n          sessionsCount: 0\n        }\n      }\n    }\n\n    // Update progress with session data\n    const newWordsLearned = session.translationsRequested.filter(\n      word => !progress!.wordsLearned.includes(word)\n    )\n    \n    progress.wordsLearned = [...progress.wordsLearned, ...newWordsLearned]\n    progress.completionPercentage = Math.max(progress.completionPercentage, session.completionPercentage)\n    progress.timeSpent += sessionDurationSeconds\n    progress.lastAccessedAt = endTime\n    progress.statistics.uniqueWordsEncountered = [...new Set([\n      ...session.wordsEncountered,\n      ...progress.wordsLearned\n    ])].length\n    progress.statistics.translationsRequested += session.translationsRequested.length\n    progress.statistics.audioPlayTime += session.audioPlayTime\n    progress.statistics.sessionsCount += 1\n\n    // Mark as completed if 90% or more\n    if (progress.completionPercentage >= 90 && !progress.completedAt) {\n      progress.completedAt = endTime\n    }\n\n    this.saveStoryProgress(progress)\n    localStorage.removeItem(this.sessionKey)\n  }\n\n  // Track word encounter\n  trackWordEncounter(word: string): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    if (!session.wordsEncountered.includes(word)) {\n      session.wordsEncountered.push(word)\n      this.updateSession({ wordsEncountered: session.wordsEncountered })\n    }\n  }\n\n  // Track translation request\n  trackTranslationRequest(word: string): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    if (!session.translationsRequested.includes(word)) {\n      session.translationsRequested.push(word)\n      this.updateSession({ translationsRequested: session.translationsRequested })\n    }\n  }\n\n  // Track audio play time\n  trackAudioPlayTime(seconds: number): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    this.updateSession({ audioPlayTime: session.audioPlayTime + seconds })\n  }\n\n  // Update completion percentage\n  updateCompletionPercentage(percentage: number): void {\n    const session = this.getCurrentSession()\n    if (!session) return\n\n    this.updateSession({ completionPercentage: Math.max(session.completionPercentage, percentage) })\n  }\n\n  // Private helper methods\n  private getCurrentSession(): LearningSession | null {\n    const sessionData = localStorage.getItem(this.sessionKey)\n    return sessionData ? JSON.parse(sessionData) : null\n  }\n\n  private getAllProgress(): UserProgress[] {\n    const progressData = localStorage.getItem(this.storageKey)\n    return progressData ? JSON.parse(progressData) : []\n  }\n\n  private saveStoryProgress(progress: UserProgress): void {\n    const allProgress = this.getAllProgress()\n    const existingIndex = allProgress.findIndex(p => p.storyId === progress.storyId)\n    \n    if (existingIndex >= 0) {\n      allProgress[existingIndex] = progress\n    } else {\n      allProgress.push(progress)\n    }\n    \n    localStorage.setItem(this.storageKey, JSON.stringify(allProgress))\n  }\n}\n\nexport const progressService = new ProgressService()\n"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,yEAAyE;;;;AAyCzE,MAAM;IACI,aAAa,qBAAoB;IACjC,aAAa,4BAA2B;IAEhD,yCAAyC;IACzC,mBAAkC;QAChC,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,IAAI,YAAY;QAE9B,MAAM,sBAAsB,YAAY,MAAM;QAC9C,MAAM,wBAAwB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,MAAM;QAC3E,MAAM,oBAAoB;eAAI,IAAI,IAAI,YAAY,OAAO,CAAC,CAAA,IAAK,EAAE,YAAY;SAAG,CAAC,MAAM;QACvF,MAAM,iBAAiB,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,SAAS,EAAE;QACzE,MAAM,wBAAwB,sBAAsB,IAChD,YAAY,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,oBAAoB,EAAE,KAAK,sBAClE;QAEJ,sEAAsE;QACtE,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,MAAM,CAAC,CAAC,QAAQ,IAC1B,IAAI,KAAK,EAAE,cAAc,IAAI,IAAI,KAAK,UAAU,EAAE,cAAc,GAAG,QACnE,WAAW,CAAC,EAAE,CAAC,cAAc,IAC/B;QAEJ,MAAM,aAAa,IAAI,KAAK,gBAAgB,YAAY,OAAO,QAAQ,IAAI;QAE3E,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,oCAAoC;IACpC,iBAAiB,OAAe,EAAuB;QACrD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,YAAY;IACzD;IAEA,+BAA+B;IAC/B,aAAa,OAAe,EAAmB;QAC7C,MAAM,YAAY,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QACpF,MAAM,UAA2B;YAC/B;YACA;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,kBAAkB,EAAE;YACpB,uBAAuB,EAAE;YACzB,eAAe;YACf,sBAAsB;QACxB;QAEA,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;QACrD,OAAO;IACT;IAEA,yBAAyB;IACzB,cAAc,OAAiC,EAAQ;QACrD,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAC7C,IAAI,CAAC,gBAAgB;QAErB,MAAM,iBAAiB;YAAE,GAAG,cAAc;YAAE,GAAG,OAAO;QAAC;QACvD,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;IACvD;IAEA,0CAA0C;IAC1C,aAAmB;QACjB,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,MAAM,UAAU,IAAI,OAAO,WAAW;QACtC,MAAM,kBAAkB,IAAI,KAAK,SAAS,OAAO,KAAK,IAAI,KAAK,QAAQ,SAAS,EAAE,OAAO;QACzF,MAAM,yBAAyB,KAAK,KAAK,CAAC,kBAAkB;QAE5D,mCAAmC;QACnC,IAAI,WAAW,IAAI,CAAC,gBAAgB,CAAC,QAAQ,OAAO;QAEpD,IAAI,CAAC,UAAU;YACb,WAAW;gBACT,QAAQ;gBACR,SAAS,QAAQ,OAAO;gBACxB,cAAc,EAAE;gBAChB,sBAAsB;gBACtB,WAAW;gBACX,gBAAgB;gBAChB,WAAW,QAAQ,SAAS;gBAC5B,YAAY;oBACV,YAAY;oBACZ,wBAAwB;oBACxB,uBAAuB;oBACvB,eAAe;oBACf,eAAe;gBACjB;YACF;QACF;QAEA,oCAAoC;QACpC,MAAM,kBAAkB,QAAQ,qBAAqB,CAAC,MAAM,CAC1D,CAAA,OAAQ,CAAC,SAAU,YAAY,CAAC,QAAQ,CAAC;QAG3C,SAAS,YAAY,GAAG;eAAI,SAAS,YAAY;eAAK;SAAgB;QACtE,SAAS,oBAAoB,GAAG,KAAK,GAAG,CAAC,SAAS,oBAAoB,EAAE,QAAQ,oBAAoB;QACpG,SAAS,SAAS,IAAI;QACtB,SAAS,cAAc,GAAG;QAC1B,SAAS,UAAU,CAAC,sBAAsB,GAAG;eAAI,IAAI,IAAI;mBACpD,QAAQ,gBAAgB;mBACxB,SAAS,YAAY;aACzB;SAAE,CAAC,MAAM;QACV,SAAS,UAAU,CAAC,qBAAqB,IAAI,QAAQ,qBAAqB,CAAC,MAAM;QACjF,SAAS,UAAU,CAAC,aAAa,IAAI,QAAQ,aAAa;QAC1D,SAAS,UAAU,CAAC,aAAa,IAAI;QAErC,mCAAmC;QACnC,IAAI,SAAS,oBAAoB,IAAI,MAAM,CAAC,SAAS,WAAW,EAAE;YAChE,SAAS,WAAW,GAAG;QACzB;QAEA,IAAI,CAAC,iBAAiB,CAAC;QACvB,aAAa,UAAU,CAAC,IAAI,CAAC,UAAU;IACzC;IAEA,uBAAuB;IACvB,mBAAmB,IAAY,EAAQ;QACrC,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,QAAQ,gBAAgB,CAAC,QAAQ,CAAC,OAAO;YAC5C,QAAQ,gBAAgB,CAAC,IAAI,CAAC;YAC9B,IAAI,CAAC,aAAa,CAAC;gBAAE,kBAAkB,QAAQ,gBAAgB;YAAC;QAClE;IACF;IAEA,4BAA4B;IAC5B,wBAAwB,IAAY,EAAQ;QAC1C,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,QAAQ,qBAAqB,CAAC,QAAQ,CAAC,OAAO;YACjD,QAAQ,qBAAqB,CAAC,IAAI,CAAC;YACnC,IAAI,CAAC,aAAa,CAAC;gBAAE,uBAAuB,QAAQ,qBAAqB;YAAC;QAC5E;IACF;IAEA,wBAAwB;IACxB,mBAAmB,OAAe,EAAQ;QACxC,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,aAAa,CAAC;YAAE,eAAe,QAAQ,aAAa,GAAG;QAAQ;IACtE;IAEA,+BAA+B;IAC/B,2BAA2B,UAAkB,EAAQ;QACnD,MAAM,UAAU,IAAI,CAAC,iBAAiB;QACtC,IAAI,CAAC,SAAS;QAEd,IAAI,CAAC,aAAa,CAAC;YAAE,sBAAsB,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE;QAAY;IAChG;IAEA,yBAAyB;IACjB,oBAA4C;QAClD,MAAM,cAAc,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;QACxD,OAAO,cAAc,KAAK,KAAK,CAAC,eAAe;IACjD;IAEQ,iBAAiC;QACvC,MAAM,eAAe,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;QACzD,OAAO,eAAe,KAAK,KAAK,CAAC,gBAAgB,EAAE;IACrD;IAEQ,kBAAkB,QAAsB,EAAQ;QACtD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,MAAM,gBAAgB,YAAY,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK,SAAS,OAAO;QAE/E,IAAI,iBAAiB,GAAG;YACtB,WAAW,CAAC,cAAc,GAAG;QAC/B,OAAO;YACL,YAAY,IAAI,CAAC;QACnB;QAEA,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC;IACvD;AACF;AAEO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useProgress.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { progressService, UserProgress, ProgressStats, LearningSession } from '../services/progress-service'\n\nexport function useProgress(storyId?: string) {\n  const [storyProgress, setStoryProgress] = useState<UserProgress | null>(null)\n  const [overallStats, setOverallStats] = useState<ProgressStats | null>(null)\n  const [currentSession, setCurrentSession] = useState<LearningSession | null>(null)\n\n  // Load progress data\n  useEffect(() => {\n    if (storyId) {\n      const progress = progressService.getStoryProgress(storyId)\n      setStoryProgress(progress)\n    }\n    \n    const stats = progressService.getProgressStats()\n    setOverallStats(stats)\n  }, [storyId])\n\n  // Start a learning session\n  const startSession = useCallback((sessionStoryId: string) => {\n    const session = progressService.startSession(sessionStoryId)\n    setCurrentSession(session)\n    return session\n  }, [])\n\n  // End the current session\n  const endSession = useCallback(() => {\n    progressService.endSession()\n    setCurrentSession(null)\n    \n    // Refresh progress data\n    if (storyId) {\n      const progress = progressService.getStoryProgress(storyId)\n      setStoryProgress(progress)\n    }\n    \n    const stats = progressService.getProgressStats()\n    setOverallStats(stats)\n  }, [storyId])\n\n  // Track word encounter\n  const trackWordEncounter = useCallback((word: string) => {\n    progressService.trackWordEncounter(word)\n  }, [])\n\n  // Track translation request\n  const trackTranslationRequest = useCallback((word: string) => {\n    progressService.trackTranslationRequest(word)\n  }, [])\n\n  // Track audio play time\n  const trackAudioPlayTime = useCallback((seconds: number) => {\n    progressService.trackAudioPlayTime(seconds)\n  }, [])\n\n  // Update completion percentage\n  const updateCompletionPercentage = useCallback((percentage: number) => {\n    progressService.updateCompletionPercentage(percentage)\n  }, [])\n\n  // Calculate reading progress based on current word position\n  const calculateReadingProgress = useCallback((currentWordIndex: number, totalWords: number) => {\n    if (totalWords === 0) return 0\n    const percentage = Math.round((currentWordIndex / totalWords) * 100)\n    updateCompletionPercentage(percentage)\n    return percentage\n  }, [updateCompletionPercentage])\n\n  return {\n    storyProgress,\n    overallStats,\n    currentSession,\n    startSession,\n    endSession,\n    trackWordEncounter,\n    trackTranslationRequest,\n    trackAudioPlayTime,\n    updateCompletionPercentage,\n    calculateReadingProgress\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAHA;;;AAKO,SAAS,YAAY,OAAgB;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACxE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACvE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAE7E,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,MAAM,WAAW,6IAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC;YAClD,iBAAiB;QACnB;QAEA,MAAM,QAAQ,6IAAA,CAAA,kBAAe,CAAC,gBAAgB;QAC9C,gBAAgB;IAClB,GAAG;QAAC;KAAQ;IAEZ,2BAA2B;IAC3B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,MAAM,UAAU,6IAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;QAC7C,kBAAkB;QAClB,OAAO;IACT,GAAG,EAAE;IAEL,0BAA0B;IAC1B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,6IAAA,CAAA,kBAAe,CAAC,UAAU;QAC1B,kBAAkB;QAElB,wBAAwB;QACxB,IAAI,SAAS;YACX,MAAM,WAAW,6IAAA,CAAA,kBAAe,CAAC,gBAAgB,CAAC;YAClD,iBAAiB;QACnB;QAEA,MAAM,QAAQ,6IAAA,CAAA,kBAAe,CAAC,gBAAgB;QAC9C,gBAAgB;IAClB,GAAG;QAAC;KAAQ;IAEZ,uBAAuB;IACvB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,6IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;IACrC,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,6IAAA,CAAA,kBAAe,CAAC,uBAAuB,CAAC;IAC1C,GAAG,EAAE;IAEL,wBAAwB;IACxB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,6IAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC;IACrC,GAAG,EAAE;IAEL,+BAA+B;IAC/B,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9C,6IAAA,CAAA,kBAAe,CAAC,0BAA0B,CAAC;IAC7C,GAAG,EAAE;IAEL,4DAA4D;IAC5D,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,kBAA0B;QACtE,IAAI,eAAe,GAAG,OAAO;QAC7B,MAAM,aAAa,KAAK,KAAK,CAAC,AAAC,mBAAmB,aAAc;QAChE,2BAA2B;QAC3B,OAAO;IACT,GAAG;QAAC;KAA2B;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1576, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/StoryReaderScreen.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { StoryHeader } from '../story/StoryHeader'\nimport { ReadingArea } from '../story/ReadingArea'\nimport { AudioPlayer } from '../story/AudioPlayer'\nimport { PageNavigation } from '../story/PageNavigation'\nimport { ErrorBoundary } from '../ui/ErrorBoundary'\nimport { KeyboardShortcutsHelp } from '../ui/KeyboardShortcutsHelp'\nimport { ProgressDisplay } from '../ui/ProgressDisplay'\nimport { useAudioPlayer } from '@/lib/hooks/useAudioPlayer'\nimport { useTranslation } from '@/lib/hooks/useTranslation'\nimport { useKeyboardShortcuts } from '@/lib/hooks/useKeyboardShortcuts'\nimport { useProgress } from '@/lib/hooks/useProgress'\nimport { Story, StoryReaderState, TranslationResult } from '@/lib/types'\n\ninterface StoryReaderScreenProps {\n  story: Story\n}\n\n\n\nexport function StoryReaderScreen({ story }: StoryReaderScreenProps) {\n  const [state, setState] = useState<StoryReaderState>({\n    currentWordIndex: -1,\n    selectedWord: undefined,\n    translationCard: {\n      isVisible: false,\n      word: '',\n      position: { x: 0, y: 0 },\n      translation: undefined,\n      isLoading: false\n    },\n    audioPlayer: {\n      isPlaying: false,\n      currentTime: 0,\n      duration: 0,\n      volume: 1.0\n    }\n  })\n\n  // Handle word highlighting during audio playback\n  const handleWordHighlight = useCallback((wordIndex: number) => {\n    setState(prev => ({\n      ...prev,\n      currentWordIndex: wordIndex\n    }))\n\n    // Track reading progress\n    if (story.wordTimings) {\n      const totalWords = story.wordTimings.length\n      const progressPercentage = progress.calculateReadingProgress(wordIndex, totalWords)\n    }\n  }, [story.wordTimings, progress])\n\n  // Use translation hook\n  const translation = useTranslation()\n\n  // Use progress tracking\n  const progress = useProgress(story.id)\n\n  // Use audio player hook with real functionality\n  const audioPlayer = useAudioPlayer({\n    audioUrl: story.audioUrl,\n    wordTimings: story.wordTimings,\n    onWordHighlight: handleWordHighlight\n  })\n\n  // Start progress tracking session on mount\n  React.useEffect(() => {\n    const session = progress.startSession(story.id)\n\n    return () => {\n      progress.endSession()\n    }\n  }, [story.id, progress])\n\n  // Update state when audio player state changes\n  React.useEffect(() => {\n    setState(prev => ({\n      ...prev,\n      audioPlayer: audioPlayer.state\n    }))\n  }, [audioPlayer.state])\n\n  const handleWordClick = useCallback(async (word: string, index: number, event: React.MouseEvent) => {\n    // Close existing translation card if clicking the same word\n    if (state.translationCard.isVisible && state.translationCard.word === word) {\n      setState(prev => ({\n        ...prev,\n        translationCard: { ...prev.translationCard, isVisible: false }\n      }))\n      return\n    }\n\n    // Show loading state\n    setState(prev => ({\n      ...prev,\n      selectedWord: word,\n      translationCard: {\n        isVisible: true,\n        word,\n        position: { x: event.clientX, y: event.clientY },\n        translation: undefined,\n        isLoading: true\n      }\n    }))\n\n    // Track word encounter and translation request\n    progress.trackWordEncounter(word)\n    progress.trackTranslationRequest(word)\n\n    // Get translation using the hook\n    const translationResult = await translation.translateWord(word, 'en', 'fr')\n\n    setState(prev => ({\n      ...prev,\n      translationCard: {\n        ...prev.translationCard,\n        translation: translationResult || undefined,\n        isLoading: false\n      }\n    }))\n  }, [state.translationCard.isVisible, state.translationCard.word, translation])\n\n  const handleTranslationClose = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      translationCard: { ...prev.translationCard, isVisible: false }\n    }))\n  }, [])\n\n  const handlePlayPause = useCallback(() => {\n    audioPlayer.togglePlayPause()\n  }, [audioPlayer])\n\n  const handleSeek = useCallback((time: number) => {\n    audioPlayer.seek(time)\n  }, [audioPlayer])\n\n  const handleVolumeChange = useCallback((volume: number) => {\n    audioPlayer.setVolume(volume)\n  }, [audioPlayer])\n\n  const handleSeekForward = useCallback(() => {\n    const newTime = Math.min(state.audioPlayer.currentTime + 10, state.audioPlayer.duration)\n    audioPlayer.seek(newTime)\n  }, [audioPlayer, state.audioPlayer.currentTime, state.audioPlayer.duration])\n\n  const handleSeekBackward = useCallback(() => {\n    const newTime = Math.max(state.audioPlayer.currentTime - 10, 0)\n    audioPlayer.seek(newTime)\n  }, [audioPlayer, state.audioPlayer.currentTime])\n\n  // Setup keyboard shortcuts\n  useKeyboardShortcuts({\n    onPlayPause: handlePlayPause,\n    onSeekForward: handleSeekForward,\n    onSeekBackward: handleSeekBackward,\n    onCloseTranslation: handleTranslationClose\n  })\n\n  return (\n    <ErrorBoundary>\n      <div className=\"flex flex-col h-screen bg-white\">\n        <StoryHeader\n          title={story.title}\n          chapter={story.chapter}\n          isAudioPlaying={state.audioPlayer.isPlaying}\n          onBookmarkClick={() => console.log('Bookmark clicked')}\n          onMenuClick={() => console.log('Menu clicked')}\n        />\n        \n        <ReadingArea\n          content={story.content}\n          currentWordIndex={state.currentWordIndex}\n          translationCard={state.translationCard}\n          onWordClick={handleWordClick}\n          onTranslationClose={handleTranslationClose}\n          onTranslationPlayAudio={() => console.log('Play translation audio')}\n        />\n        \n        <PageNavigation\n          currentPage={24}\n          totalPages={120}\n          onPreviousPage={() => console.log('Previous page')}\n          onNextPage={() => console.log('Next page')}\n        />\n        \n        <AudioPlayer\n          state={state.audioPlayer}\n          onPlayPause={handlePlayPause}\n          onSeek={handleSeek}\n          onVolumeChange={handleVolumeChange}\n        />\n\n        <KeyboardShortcutsHelp />\n      </div>\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAbA;;;;;;;;;;;;;AAsBO,SAAS,kBAAkB,EAAE,KAAK,EAA0B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,kBAAkB,CAAC;QACnB,cAAc;QACd,iBAAiB;YACf,WAAW;YACX,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAG,GAAG;YAAE;YACvB,aAAa;YACb,WAAW;QACb;QACA,aAAa;YACX,WAAW;YACX,aAAa;YACb,UAAU;YACV,QAAQ;QACV;IACF;IAEA,iDAAiD;IACjD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,kBAAkB;YACpB,CAAC;QAED,yBAAyB;QACzB,IAAI,MAAM,WAAW,EAAE;YACrB,MAAM,aAAa,MAAM,WAAW,CAAC,MAAM;YAC3C,MAAM,qBAAqB,SAAS,wBAAwB,CAAC,WAAW;QAC1E;IACF,GAAG;QAAC,MAAM,WAAW;QAAE;KAAS;IAEhC,uBAAuB;IACvB,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IAEjC,wBAAwB;IACxB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,MAAM,EAAE;IAErC,gDAAgD;IAChD,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,iBAAiB;IACnB;IAEA,2CAA2C;IAC3C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,MAAM,UAAU,SAAS,YAAY,CAAC,MAAM,EAAE;QAE9C,OAAO;YACL,SAAS,UAAU;QACrB;IACF,GAAG;QAAC,MAAM,EAAE;QAAE;KAAS;IAEvB,+CAA+C;IAC/C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa,YAAY,KAAK;YAChC,CAAC;IACH,GAAG;QAAC,YAAY,KAAK;KAAC;IAEtB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAc,OAAe;QACtE,4DAA4D;QAC5D,IAAI,MAAM,eAAe,CAAC,SAAS,IAAI,MAAM,eAAe,CAAC,IAAI,KAAK,MAAM;YAC1E,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;wBAAE,GAAG,KAAK,eAAe;wBAAE,WAAW;oBAAM;gBAC/D,CAAC;YACD;QACF;QAEA,qBAAqB;QACrB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,iBAAiB;oBACf,WAAW;oBACX;oBACA,UAAU;wBAAE,GAAG,MAAM,OAAO;wBAAE,GAAG,MAAM,OAAO;oBAAC;oBAC/C,aAAa;oBACb,WAAW;gBACb;YACF,CAAC;QAED,+CAA+C;QAC/C,SAAS,kBAAkB,CAAC;QAC5B,SAAS,uBAAuB,CAAC;QAEjC,iCAAiC;QACjC,MAAM,oBAAoB,MAAM,YAAY,aAAa,CAAC,MAAM,MAAM;QAEtE,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,iBAAiB;oBACf,GAAG,KAAK,eAAe;oBACvB,aAAa,qBAAqB;oBAClC,WAAW;gBACb;YACF,CAAC;IACH,GAAG;QAAC,MAAM,eAAe,CAAC,SAAS;QAAE,MAAM,eAAe,CAAC,IAAI;QAAE;KAAY;IAE7E,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,iBAAiB;oBAAE,GAAG,KAAK,eAAe;oBAAE,WAAW;gBAAM;YAC/D,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,eAAe;IAC7B,GAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,YAAY,IAAI,CAAC;IACnB,GAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,YAAY,SAAS,CAAC;IACxB,GAAG;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW,GAAG,IAAI,MAAM,WAAW,CAAC,QAAQ;QACvF,YAAY,IAAI,CAAC;IACnB,GAAG;QAAC;QAAa,MAAM,WAAW,CAAC,WAAW;QAAE,MAAM,WAAW,CAAC,QAAQ;KAAC;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW,GAAG,IAAI;QAC7D,YAAY,IAAI,CAAC;IACnB,GAAG;QAAC;QAAa,MAAM,WAAW,CAAC,WAAW;KAAC;IAE/C,2BAA2B;IAC3B,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE;QACnB,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,qBACE,8OAAC,yIAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,KAAK;oBAClB,SAAS,MAAM,OAAO;oBACtB,gBAAgB,MAAM,WAAW,CAAC,SAAS;oBAC3C,iBAAiB,IAAM,QAAQ,GAAG,CAAC;oBACnC,aAAa,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGjC,8OAAC,0IAAA,CAAA,cAAW;oBACV,SAAS,MAAM,OAAO;oBACtB,kBAAkB,MAAM,gBAAgB;oBACxC,iBAAiB,MAAM,eAAe;oBACtC,aAAa;oBACb,oBAAoB;oBACpB,wBAAwB,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAG5C,8OAAC,6IAAA,CAAA,iBAAc;oBACb,aAAa;oBACb,YAAY;oBACZ,gBAAgB,IAAM,QAAQ,GAAG,CAAC;oBAClC,YAAY,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGhC,8OAAC,0IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,WAAW;oBACxB,aAAa;oBACb,QAAQ;oBACR,gBAAgB;;;;;;8BAGlB,8OAAC,iJAAA,CAAA,wBAAqB;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}, {"offset": {"line": 1831, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { StoryReaderScreen } from \"@/components/screens/StoryReaderScreen\";\nimport storiesData from \"@/data/stories.json\";\nimport { Story } from \"@/lib/types\";\n\nexport default function Home() {\n  // Get the first story from our mock data\n  const story = storiesData[0] as Story;\n\n  return (\n    <div className=\"relative\">\n      <div className=\"absolute top-4 right-4 z-50\">\n        <a\n          href=\"/admin\"\n          className=\"px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors text-sm\"\n        >\n          Admin Panel\n        </a>\n      </div>\n      <StoryReaderScreen story={story} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAOe,SAAS;IACtB,yCAAyC;IACzC,MAAM,QAAQ,8FAAA,CAAA,UAAW,CAAC,EAAE;IAE5B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;0BAIH,8OAAC,kJAAA,CAAA,oBAAiB;gBAAC,OAAO;;;;;;;;;;;;AAGhC", "debugId": null}}]}