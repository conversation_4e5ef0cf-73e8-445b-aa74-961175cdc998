{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/StoryReaderScreen.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StoryReaderScreen = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoryReaderScreen() from the server but StoryReaderScreen is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/screens/StoryReaderScreen.tsx <module evaluation>\",\n    \"StoryReaderScreen\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/StoryReaderScreen.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const StoryReaderScreen = registerClientReference(\n    function() { throw new Error(\"Attempted to call StoryReaderScreen() from the server but StoryReaderScreen is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/screens/StoryReaderScreen.tsx\",\n    \"StoryReaderScreen\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/app/page.tsx"], "sourcesContent": ["import { StoryReaderScreen } from \"@/components/screens/StoryReaderScreen\";\nimport storiesData from \"@/data/stories.json\";\nimport { Story } from \"@/lib/types\";\n\nexport default function Home() {\n  // Get the first story from our mock data\n  const story = storiesData[0] as Story;\n\n  return <StoryReaderScreen story={story} />;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAGe,SAAS;IACtB,yCAAyC;IACzC,MAAM,QAAQ,8FAAA,CAAA,UAAW,CAAC,EAAE;IAE5B,qBAAO,8OAAC,kJAAA,CAAA,oBAAiB;QAAC,OAAO;;;;;;AACnC", "debugId": null}}]}