{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Typography.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface TypographyProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport const Typography = {\n  Title: ({ children, className = '' }: TypographyProps) => (\n    <h1 className={`text-2xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </h1>\n  ),\n  \n  Subtitle: ({ children, className = '' }: TypographyProps) => (\n    <h2 className={`text-base text-gray-600 ${className}`}>\n      {children}\n    </h2>\n  ),\n  \n  Body: ({ children, className = '' }: TypographyProps) => (\n    <p className={`text-lg text-gray-800 leading-relaxed ${className}`}>\n      {children}\n    </p>\n  ),\n  \n  Caption: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-sm text-gray-500 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationWord: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationText: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-base text-gray-700 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  Micro: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xs text-gray-400 ${className}`}>\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,aAAa;IACxB,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACnD,8OAAC;YAAG,WAAW,CAAC,iCAAiC,EAAE,WAAW;sBAC3D;;;;;;IAIL,UAAU,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACtD,8OAAC;YAAG,WAAW,CAAC,wBAAwB,EAAE,WAAW;sBAClD;;;;;;IAIL,MAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAClD,8OAAC;YAAE,WAAW,CAAC,sCAAsC,EAAE,WAAW;sBAC/D;;;;;;IAIL,SAAS,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACrD,8OAAC;YAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW;sBAClD;;;;;;IAIL,iBAAiB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAC7D,8OAAC;YAAK,WAAW,CAAC,gCAAgC,EAAE,WAAW;sBAC5D;;;;;;IAIL,iBAAiB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAC7D,8OAAC;YAAK,WAAW,CAAC,wBAAwB,EAAE,WAAW;sBACpD;;;;;;IAIL,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACnD,8OAAC;YAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW;sBAClD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/IconButton.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface IconButtonProps {\n  icon: React.ReactNode\n  onClick: () => void\n  variant?: 'default' | 'ghost' | 'outline'\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  disabled?: boolean\n  'aria-label'?: string\n}\n\nexport function IconButton({\n  icon,\n  onClick,\n  variant = 'default',\n  size = 'md',\n  className = '',\n  disabled = false,\n  'aria-label': ariaLabel,\n}: IconButtonProps) {\n  const baseClasses = 'inline-flex items-center justify-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'\n  \n  const variantClasses = {\n    default: 'bg-gray-100 hover:bg-gray-200 text-gray-700',\n    ghost: 'hover:bg-gray-100 text-gray-600',\n    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'\n  }\n  \n  const sizeClasses = {\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-10 h-10 text-base',\n    lg: 'w-12 h-12 text-lg'\n  }\n  \n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      aria-label={ariaLabel}\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}\n    >\n      {icon}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAYO,SAAS,WAAW,EACzB,IAAI,EACJ,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,cAAc,SAAS,EACP;IAChB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,cAAY;QACZ,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;kBAEvF;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Typography } from './Typography'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>\n}\n\nexport class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback\n      return <FallbackComponent error={this.state.error} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-[200px] p-6 bg-red-50 rounded-lg border border-red-200\">\n      <Typography.Title className=\"text-red-800 mb-2\">\n        Something went wrong\n      </Typography.Title>\n      <Typography.Body className=\"text-red-600 text-center mb-4\">\n        {error?.message || 'An unexpected error occurred'}\n      </Typography.Body>\n      <button\n        onClick={resetError}\n        className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n      >\n        Try again\n      </button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeO,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;IACzD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACjD,qBAAO,8OAAC;gBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;QAChF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAA6C;IAC5F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gBAAC,WAAU;0BAAoB;;;;;;0BAGhD,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,WAAU;0BACxB,OAAO,WAAW;;;;;;0BAErB,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/AdminScreen.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { ErrorBoundary } from '../ui/ErrorBoundary'\nimport { StoryForm } from '../story/StoryForm'\nimport { PlusIcon, PlayIcon, TrashIcon } from '@radix-ui/react-icons'\nimport { Story } from '@/lib/types'\n\ninterface AdminScreenProps {\n  stories: Story[]\n  onCreateStory: () => void\n  onEditStory: (story: Story) => void\n  onDeleteStory: (storyId: string) => void\n  onPreviewStory: (story: Story) => void\n}\n\nexport function AdminScreen({ \n  stories, \n  onCreateStory, \n  onEditStory, \n  onDeleteStory, \n  onPreviewStory \n}: AdminScreenProps) {\n  const [selectedStory, setSelectedStory] = useState<Story | null>(null)\n\n  const handleStoryClick = useCallback((story: Story) => {\n    setSelectedStory(story)\n  }, [])\n\n  const getDifficultyColor = (difficulty: string) => {\n    switch (difficulty) {\n      case 'beginner': return 'bg-green-100 text-green-800'\n      case 'intermediate': return 'bg-yellow-100 text-yellow-800'\n      case 'advanced': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  return (\n    <ErrorBoundary>\n      <div className=\"min-h-screen bg-gray-50\">\n        {/* Header */}\n        <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <Typography.Title className=\"text-2xl\">\n                Story Management\n              </Typography.Title>\n              <Typography.Subtitle className=\"mt-1\">\n                Manage your language learning stories\n              </Typography.Subtitle>\n            </div>\n            <IconButton\n              icon={<PlusIcon className=\"w-5 h-5\" />}\n              onClick={onCreateStory}\n              className=\"bg-blue-600 text-white hover:bg-blue-700\"\n              aria-label=\"Create new story\"\n            />\n          </div>\n        </header>\n\n        <div className=\"flex h-[calc(100vh-80px)]\">\n          {/* Stories List */}\n          <div className=\"w-1/2 border-r border-gray-200 overflow-y-auto\">\n            <div className=\"p-6\">\n              <Typography.Body className=\"text-lg font-semibold mb-4\">\n                Stories ({stories.length})\n              </Typography.Body>\n              \n              {stories.length === 0 ? (\n                <div className=\"text-center py-12\">\n                  <Typography.Body className=\"text-gray-500 mb-4\">\n                    No stories yet\n                  </Typography.Body>\n                  <button\n                    onClick={onCreateStory}\n                    className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n                  >\n                    Create your first story\n                  </button>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {stories.map((story) => (\n                    <div\n                      key={story.id}\n                      className={`p-4 rounded-lg border cursor-pointer transition-colors ${\n                        selectedStory?.id === story.id\n                          ? 'border-blue-500 bg-blue-50'\n                          : 'border-gray-200 bg-white hover:border-gray-300'\n                      }`}\n                      onClick={() => handleStoryClick(story)}\n                    >\n                      <div className=\"flex items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <Typography.Body className=\"font-semibold text-gray-900 mb-1\">\n                            {story.title}\n                          </Typography.Body>\n                          {story.chapter && (\n                            <Typography.Caption className=\"text-gray-600 mb-2\">\n                              {story.chapter}\n                            </Typography.Caption>\n                          )}\n                          <div className=\"flex items-center space-x-2\">\n                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(story.difficulty)}`}>\n                              {story.difficulty}\n                            </span>\n                            <Typography.Micro className=\"text-gray-500\">\n                              {story.language.toUpperCase()}\n                            </Typography.Micro>\n                            {story.audioUrl && (\n                              <Typography.Micro className=\"text-green-600\">\n                                ♪ Audio\n                              </Typography.Micro>\n                            )}\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-1 ml-4\">\n                          <IconButton\n                            icon={<PlayIcon className=\"w-4 h-4\" />}\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              onPreviewStory(story)\n                            }}\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            aria-label=\"Preview story\"\n                          />\n                          <IconButton\n                            icon={<TrashIcon className=\"w-4 h-4\" />}\n                            onClick={(e) => {\n                              e.stopPropagation()\n                              onDeleteStory(story.id)\n                            }}\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            className=\"text-red-600 hover:bg-red-50\"\n                            aria-label=\"Delete story\"\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Story Details */}\n          <div className=\"w-1/2 overflow-y-auto\">\n            {selectedStory ? (\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-6\">\n                  <Typography.Title className=\"text-xl\">\n                    Story Details\n                  </Typography.Title>\n                  <button\n                    onClick={() => onEditStory(selectedStory)}\n                    className=\"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors\"\n                  >\n                    Edit Story\n                  </button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Title\n                    </Typography.Caption>\n                    <Typography.Body>{selectedStory.title}</Typography.Body>\n                  </div>\n\n                  {selectedStory.chapter && (\n                    <div>\n                      <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                        Chapter\n                      </Typography.Caption>\n                      <Typography.Body>{selectedStory.chapter}</Typography.Body>\n                    </div>\n                  )}\n\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Language & Difficulty\n                    </Typography.Caption>\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(selectedStory.difficulty)}`}>\n                        {selectedStory.difficulty}\n                      </span>\n                      <Typography.Caption>{selectedStory.language.toUpperCase()}</Typography.Caption>\n                    </div>\n                  </div>\n\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Content Preview\n                    </Typography.Caption>\n                    <div className=\"bg-gray-50 rounded-lg p-4 max-h-40 overflow-y-auto\">\n                      <Typography.Body className=\"text-sm\">\n                        {selectedStory.content.substring(0, 300)}\n                        {selectedStory.content.length > 300 && '...'}\n                      </Typography.Body>\n                    </div>\n                  </div>\n\n                  <div>\n                    <Typography.Caption className=\"font-semibold text-gray-700 mb-1\">\n                      Audio & Timing\n                    </Typography.Caption>\n                    <div className=\"space-y-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Typography.Micro>Audio:</Typography.Micro>\n                        <Typography.Micro className={selectedStory.audioUrl ? 'text-green-600' : 'text-red-600'}>\n                          {selectedStory.audioUrl ? '✓ Available' : '✗ Not generated'}\n                        </Typography.Micro>\n                      </div>\n                      <div className=\"flex items-center space-x-2\">\n                        <Typography.Micro>Word Timings:</Typography.Micro>\n                        <Typography.Micro className={selectedStory.wordTimings?.length ? 'text-green-600' : 'text-red-600'}>\n                          {selectedStory.wordTimings?.length ? `✓ ${selectedStory.wordTimings.length} words` : '✗ Not available'}\n                        </Typography.Micro>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"flex items-center justify-center h-full\">\n                <Typography.Body className=\"text-gray-500\">\n                  Select a story to view details\n                </Typography.Body>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAkBO,SAAS,YAAY,EAC1B,OAAO,EACP,aAAa,EACb,WAAW,EACX,aAAa,EACb,cAAc,EACG;IACjB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjE,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,iBAAiB;IACnB,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC,yIAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;8BAChB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;wCAAC,WAAU;kDAAW;;;;;;kDAGvC,8OAAC,sIAAA,CAAA,aAAU,CAAC,QAAQ;wCAAC,WAAU;kDAAO;;;;;;;;;;;;0CAIxC,8OAAC,sIAAA,CAAA,aAAU;gCACT,oBAAM,8OAAC,gLAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAC1B,SAAS;gCACT,WAAU;gCACV,cAAW;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;wCAAC,WAAU;;4CAA6B;4CAC5C,QAAQ,MAAM;4CAAC;;;;;;;oCAG1B,QAAQ,MAAM,KAAK,kBAClB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;gDAAC,WAAU;0DAAqB;;;;;;0DAGhD,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;6DAKH,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAC,sBACZ,8OAAC;gDAEC,WAAW,CAAC,uDAAuD,EACjE,eAAe,OAAO,MAAM,EAAE,GAC1B,+BACA,kDACJ;gDACF,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;oEAAC,WAAU;8EACxB,MAAM,KAAK;;;;;;gEAEb,MAAM,OAAO,kBACZ,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;oEAAC,WAAU;8EAC3B,MAAM,OAAO;;;;;;8EAGlB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,MAAM,UAAU,GAAG;sFAClG,MAAM,UAAU;;;;;;sFAEnB,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;4EAAC,WAAU;sFACzB,MAAM,QAAQ,CAAC,WAAW;;;;;;wEAE5B,MAAM,QAAQ,kBACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;4EAAC,WAAU;sFAAiB;;;;;;;;;;;;;;;;;;sEAMnD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sIAAA,CAAA,aAAU;oEACT,oBAAM,8OAAC,gLAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAC1B,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,eAAe;oEACjB;oEACA,SAAQ;oEACR,MAAK;oEACL,cAAW;;;;;;8EAEb,8OAAC,sIAAA,CAAA,aAAU;oEACT,oBAAM,8OAAC,gLAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;oEAC3B,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,cAAc,MAAM,EAAE;oEACxB;oEACA,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,cAAW;;;;;;;;;;;;;;;;;;+CApDZ,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;sCAgEzB,8OAAC;4BAAI,WAAU;sCACZ,8BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gDAAC,WAAU;0DAAU;;;;;;0DAGtC,8OAAC;gDACC,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DACX;;;;;;;;;;;;kDAKH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;kEAAE,cAAc,KAAK;;;;;;;;;;;;4CAGtC,cAAc,OAAO,kBACpB,8OAAC;;kEACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;kEAAE,cAAc,OAAO;;;;;;;;;;;;0DAI3C,8OAAC;;kEACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,cAAc,UAAU,GAAG;0EAC1G,cAAc,UAAU;;;;;;0EAE3B,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;0EAAE,cAAc,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;0DAI3D,8OAAC;;kEACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;4DAAC,WAAU;;gEACxB,cAAc,OAAO,CAAC,SAAS,CAAC,GAAG;gEACnC,cAAc,OAAO,CAAC,MAAM,GAAG,OAAO;;;;;;;;;;;;;;;;;;0DAK7C,8OAAC;;kEACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wDAAC,WAAU;kEAAmC;;;;;;kEAGjE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;kFAAC;;;;;;kFAClB,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;wEAAC,WAAW,cAAc,QAAQ,GAAG,mBAAmB;kFACtE,cAAc,QAAQ,GAAG,gBAAgB;;;;;;;;;;;;0EAG9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;kFAAC;;;;;;kFAClB,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;wEAAC,WAAW,cAAc,WAAW,EAAE,SAAS,mBAAmB;kFACjF,cAAc,WAAW,EAAE,SAAS,CAAC,EAAE,EAAE,cAAc,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAQjG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;oCAAC,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/StoryForm.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { Cross2Icon, PlayIcon } from '@radix-ui/react-icons'\nimport { Story } from '@/lib/types'\n\ninterface StoryFormData {\n  title: string\n  chapter?: string\n  content: string\n  language: string\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  voice?: string\n  speed: number\n  generateAudio: boolean\n}\n\ninterface StoryFormProps {\n  story?: Story\n  isOpen: boolean\n  onClose: () => void\n  onSave: (data: StoryFormData) => Promise<void>\n  onPreview?: (data: StoryFormData) => Promise<void>\n}\n\nconst LANGUAGES = [\n  { code: 'en', name: 'English' },\n  { code: 'fr', name: 'French' },\n  { code: 'es', name: 'Spanish' },\n  { code: 'de', name: 'German' },\n  { code: 'it', name: 'Italian' },\n  { code: 'pt', name: 'Portuguese' }\n]\n\nconst DIFFICULTIES = [\n  { value: 'beginner', label: 'Beginner', description: 'Simple vocabulary and grammar' },\n  { value: 'intermediate', label: 'Intermediate', description: 'Moderate complexity' },\n  { value: 'advanced', label: 'Advanced', description: 'Complex vocabulary and structures' }\n] as const\n\nexport function StoryForm({ story, isOpen, onClose, onSave, onPreview }: StoryFormProps) {\n  const [formData, setFormData] = useState<StoryFormData>({\n    title: story?.title || '',\n    chapter: story?.chapter || '',\n    content: story?.content || '',\n    language: story?.language || 'fr',\n    difficulty: story?.difficulty || 'beginner',\n    voice: '',\n    speed: 1.0,\n    generateAudio: true\n  })\n\n  const [isLoading, setIsLoading] = useState(false)\n  const [isPreviewing, setIsPreviewing] = useState(false)\n\n  const handleInputChange = useCallback((field: keyof StoryFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }))\n  }, [])\n\n  const handleSave = useCallback(async () => {\n    if (!formData.title.trim() || !formData.content.trim()) {\n      alert('Please fill in title and content')\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      await onSave(formData)\n      onClose()\n    } catch (error) {\n      console.error('Save failed:', error)\n      alert('Failed to save story')\n    } finally {\n      setIsLoading(false)\n    }\n  }, [formData, onSave, onClose])\n\n  const handlePreview = useCallback(async () => {\n    if (!formData.title.trim() || !formData.content.trim()) {\n      alert('Please fill in title and content')\n      return\n    }\n\n    if (!onPreview) return\n\n    setIsPreviewing(true)\n    try {\n      await onPreview(formData)\n    } catch (error) {\n      console.error('Preview failed:', error)\n      alert('Failed to preview story')\n    } finally {\n      setIsPreviewing(false)\n    }\n  }, [formData, onPreview])\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <Typography.Title className=\"text-xl\">\n            {story ? 'Edit Story' : 'Create New Story'}\n          </Typography.Title>\n          <IconButton\n            icon={<Cross2Icon className=\"w-5 h-5\" />}\n            onClick={onClose}\n            variant=\"ghost\"\n            aria-label=\"Close form\"\n          />\n        </div>\n\n        {/* Form */}\n        <div className=\"p-6 space-y-6\">\n          {/* Title */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Title *\n            </label>\n            <input\n              type=\"text\"\n              value={formData.title}\n              onChange={(e) => handleInputChange('title', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"Enter story title\"\n            />\n          </div>\n\n          {/* Chapter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Chapter (optional)\n            </label>\n            <input\n              type=\"text\"\n              value={formData.chapter}\n              onChange={(e) => handleInputChange('chapter', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"e.g., Chapter 1, Part A\"\n            />\n          </div>\n\n          {/* Language and Difficulty */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Language *\n              </label>\n              <select\n                value={formData.language}\n                onChange={(e) => handleInputChange('language', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {LANGUAGES.map(lang => (\n                  <option key={lang.code} value={lang.code}>\n                    {lang.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Difficulty *\n              </label>\n              <select\n                value={formData.difficulty}\n                onChange={(e) => handleInputChange('difficulty', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              >\n                {DIFFICULTIES.map(diff => (\n                  <option key={diff.value} value={diff.value}>\n                    {diff.label}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Story Content *\n            </label>\n            <textarea\n              value={formData.content}\n              onChange={(e) => handleInputChange('content', e.target.value)}\n              rows={8}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n              placeholder=\"Enter the story content...\"\n            />\n            <Typography.Micro className=\"text-gray-500 mt-1\">\n              {formData.content.length} characters\n            </Typography.Micro>\n          </div>\n\n          {/* Audio Settings */}\n          <div className=\"border-t border-gray-200 pt-6\">\n            <Typography.Body className=\"font-semibold mb-4\">\n              Audio Settings\n            </Typography.Body>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"generateAudio\"\n                  checked={formData.generateAudio}\n                  onChange={(e) => handleInputChange('generateAudio', e.target.checked)}\n                  className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                />\n                <label htmlFor=\"generateAudio\" className=\"ml-2 text-sm text-gray-700\">\n                  Generate audio automatically\n                </label>\n              </div>\n\n              {formData.generateAudio && (\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Speech Speed\n                  </label>\n                  <input\n                    type=\"range\"\n                    min=\"0.5\"\n                    max=\"2.0\"\n                    step=\"0.1\"\n                    value={formData.speed}\n                    onChange={(e) => handleInputChange('speed', parseFloat(e.target.value))}\n                    className=\"w-full\"\n                  />\n                  <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                    <span>Slow (0.5x)</span>\n                    <span>Normal ({formData.speed}x)</span>\n                    <span>Fast (2.0x)</span>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"flex items-center justify-between p-6 border-t border-gray-200\">\n          <button\n            onClick={onClose}\n            className=\"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors\"\n          >\n            Cancel\n          </button>\n          \n          <div className=\"flex items-center space-x-3\">\n            {onPreview && (\n              <button\n                onClick={handlePreview}\n                disabled={isPreviewing}\n                className=\"px-4 py-2 text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors disabled:opacity-50 flex items-center space-x-2\"\n              >\n                <PlayIcon className=\"w-4 h-4\" />\n                <span>{isPreviewing ? 'Processing...' : 'Preview'}</span>\n              </button>\n            )}\n            \n            <button\n              onClick={handleSave}\n              disabled={isLoading}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50\"\n            >\n              {isLoading ? 'Saving...' : 'Save Story'}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AA2BA,MAAM,YAAY;IAChB;QAAE,MAAM;QAAM,MAAM;IAAU;IAC9B;QAAE,MAAM;QAAM,MAAM;IAAS;IAC7B;QAAE,MAAM;QAAM,MAAM;IAAU;IAC9B;QAAE,MAAM;QAAM,MAAM;IAAS;IAC7B;QAAE,MAAM;QAAM,MAAM;IAAU;IAC9B;QAAE,MAAM;QAAM,MAAM;IAAa;CAClC;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAgC;IACrF;QAAE,OAAO;QAAgB,OAAO;QAAgB,aAAa;IAAsB;IACnF;QAAE,OAAO;QAAY,OAAO;QAAY,aAAa;IAAoC;CAC1F;AAEM,SAAS,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAkB;IACrF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QACtD,OAAO,OAAO,SAAS;QACvB,SAAS,OAAO,WAAW;QAC3B,SAAS,OAAO,WAAW;QAC3B,UAAU,OAAO,YAAY;QAC7B,YAAY,OAAO,cAAc;QACjC,OAAO;QACP,OAAO;QACP,eAAe;IACjB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAA4B;QACjE,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YACtD,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,OAAO;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAU;QAAQ;KAAQ;IAE9B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YACtD,MAAM;YACN;QACF;QAEA,IAAI,CAAC,WAAW;QAEhB,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAU;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;4BAAC,WAAU;sCACzB,QAAQ,eAAe;;;;;;sCAE1B,8OAAC,sIAAA,CAAA,aAAU;4BACT,oBAAM,8OAAC,gLAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAC5B,SAAS;4BACT,SAAQ;4BACR,cAAW;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,KAAK;oCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAKhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC7D,WAAU;sDAET,UAAU,GAAG,CAAC,CAAA,qBACb,8OAAC;oDAAuB,OAAO,KAAK,IAAI;8DACrC,KAAK,IAAI;mDADC,KAAK,IAAI;;;;;;;;;;;;;;;;8CAO5B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC/D,WAAU;sDAET,aAAa,GAAG,CAAC,CAAA,qBAChB,8OAAC;oDAAwB,OAAO,KAAK,KAAK;8DACvC,KAAK,KAAK;mDADA,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAS/B,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,SAAS,OAAO;oCACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC5D,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;8CAEd,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;oCAAC,WAAU;;wCACzB,SAAS,OAAO,CAAC,MAAM;wCAAC;;;;;;;;;;;;;sCAK7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;oCAAC,WAAU;8CAAqB;;;;;;8CAIhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,SAAS,SAAS,aAAa;oDAC/B,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,OAAO;oDACpE,WAAU;;;;;;8DAEZ,8OAAC;oDAAM,SAAQ;oDAAgB,WAAU;8DAA6B;;;;;;;;;;;;wCAKvE,SAAS,aAAa,kBACrB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,MAAK;oDACL,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK;oDACrE,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;;gEAAK;gEAAS,SAAS,KAAK;gEAAC;;;;;;;sEAC9B,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,8OAAC;4BAAI,WAAU;;gCACZ,2BACC,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC,gLAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;sDAAM,eAAe,kBAAkB;;;;;;;;;;;;8CAI5C,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/StoryHeader.tsx"], "sourcesContent": ["import React from 'react'\nimport { BookmarkIcon, HamburgerMenuIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\n\ninterface StoryHeaderProps {\n  title: string\n  chapter?: string\n  isAudioPlaying?: boolean\n  onBookmarkClick: () => void\n  onMenuClick: () => void\n}\n\nexport function StoryHeader({ title, chapter, isAudioPlaying, onBookmarkClick, onMenuClick }: StoryHeaderProps) {\n  return (\n    <header className=\"flex items-center justify-between p-4 bg-white border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <div className=\"flex items-center space-x-2\">\n          <Typography.Title className=\"text-xl font-bold text-gray-900\">\n            {title}\n          </Typography.Title>\n          {isAudioPlaying && (\n            <div className=\"flex space-x-1\">\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\"></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n              <div className=\"w-1 h-4 bg-blue-500 rounded animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n            </div>\n          )}\n        </div>\n        {chapter && (\n          <Typography.Subtitle className=\"text-sm text-gray-600 mt-1\">\n            {chapter}\n          </Typography.Subtitle>\n        )}\n      </div>\n      \n      <div className=\"flex items-center space-x-2\">\n        <IconButton\n          icon={<BookmarkIcon className=\"w-5 h-5\" />}\n          onClick={onBookmarkClick}\n          variant=\"ghost\"\n          aria-label=\"Bookmark story\"\n        />\n        <IconButton\n          icon={<HamburgerMenuIcon className=\"w-5 h-5\" />}\n          onClick={onMenuClick}\n          variant=\"ghost\"\n          aria-label=\"Open menu\"\n        />\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAUO,SAAS,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,WAAW,EAAoB;IAC5G,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB;;;;;;4BAEF,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;kDAC3F,8OAAC;wCAAI,WAAU;wCAA4C,OAAO;4CAAE,gBAAgB;wCAAO;;;;;;;;;;;;;;;;;;oBAIhG,yBACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,QAAQ;wBAAC,WAAU;kCAC5B;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;kCAEb,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;wBACnC,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;;;;;;;;;;;;;AAKrB", "debugId": null}}, {"offset": {"line": 1424, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/WordHighlighter.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface WordHighlighterProps {\n  text: string\n  currentWordIndex?: number\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n}\n\nexport function WordHighlighter({ text, currentWordIndex, onWordClick }: WordHighlighterProps) {\n  const words = text.split(/(\\s+)/)\n  let wordIndex = 0\n\n  return (\n    <div className=\"text-lg leading-relaxed text-gray-800 select-none\">\n      {words.map((segment, segmentIndex) => {\n        // Skip whitespace segments\n        if (/^\\s+$/.test(segment)) {\n          return <span key={segmentIndex}>{segment}</span>\n        }\n\n        const isCurrentWord = wordIndex === currentWordIndex\n        const currentIndex = wordIndex\n        wordIndex++\n\n        return (\n          <span\n            key={segmentIndex}\n            className={`cursor-pointer transition-all duration-300 hover:bg-blue-100 px-1 py-0.5 rounded ${\n              isCurrentWord\n                ? 'bg-blue-300 text-blue-900 font-semibold shadow-sm scale-105'\n                : 'hover:scale-102'\n            }`}\n            onClick={(event) => onWordClick(segment, currentIndex, event)}\n          >\n            {segment}\n          </span>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,gBAAgB,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAwB;IAC3F,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,YAAY;IAEhB,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,SAAS;YACnB,2BAA2B;YAC3B,IAAI,QAAQ,IAAI,CAAC,UAAU;gBACzB,qBAAO,8OAAC;8BAAyB;mBAAf;;;;;YACpB;YAEA,MAAM,gBAAgB,cAAc;YACpC,MAAM,eAAe;YACrB;YAEA,qBACE,8OAAC;gBAEC,WAAW,CAAC,iFAAiF,EAC3F,gBACI,gEACA,mBACJ;gBACF,SAAS,CAAC,QAAU,YAAY,SAAS,cAAc;0BAEtD;eARI;;;;;QAWX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1468, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/TranslationCard.tsx"], "sourcesContent": ["import React from 'react'\nimport { Cross2Icon, SpeakerLoudIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { TranslationResult } from '@/lib/types'\n\ninterface TranslationCardProps {\n  word: string\n  translation?: TranslationResult\n  isLoading?: boolean\n  onPlayAudio: () => void\n  onClose: () => void\n}\n\nexport function TranslationCard({\n  word,\n  translation,\n  isLoading = false,\n  onPlayAudio,\n  onClose\n}: TranslationCardProps) {\n  return (\n    <div className=\"bg-gray-50 rounded-xl p-4 mx-5 mb-6 relative shadow-sm border border-gray-200\">\n      <IconButton\n        icon={<Cross2Icon className=\"w-4 h-4\" />}\n        onClick={onClose}\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"absolute top-2 right-2\"\n        aria-label=\"Close translation\"\n      />\n      \n      <div className=\"flex items-center justify-between mb-3\">\n        <div>\n          <Typography.TranslationWord className=\"text-xl\">\n            {word}\n          </Typography.TranslationWord>\n          {translation?.pronunciation && (\n            <Typography.Caption className=\"block mt-1\">\n              /{translation.pronunciation}/\n            </Typography.Caption>\n          )}\n        </div>\n        <IconButton \n          icon={<SpeakerLoudIcon className=\"w-4 h-4\" />} \n          onClick={onPlayAudio}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Play pronunciation\"\n        />\n      </div>\n      \n      {isLoading ? (\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/2 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n          <Typography.Caption className=\"text-gray-400 mt-2\">\n            Translating...\n          </Typography.Caption>\n        </div>\n      ) : translation ? (\n        <>\n          <Typography.TranslationText className=\"block mb-3\">\n            {translation.translation}\n          </Typography.TranslationText>\n          \n          {translation.example && (\n            <div className=\"text-sm text-gray-600 border-t border-gray-200 pt-3\">\n              <Typography.Caption className=\"italic block mb-1\">\n                {translation.example.original}\n              </Typography.Caption>\n              <Typography.Caption className=\"block\">\n                {translation.example.translation}\n              </Typography.Caption>\n            </div>\n          )}\n        </>\n      ) : (\n        <Typography.Caption className=\"text-gray-500\">\n          Translation not available\n        </Typography.Caption>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAWO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,WAAW,EACX,YAAY,KAAK,EACjB,WAAW,EACX,OAAO,EACc;IACrB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAC5B,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,cAAW;;;;;;0BAGb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,eAAe;gCAAC,WAAU;0CACnC;;;;;;4BAEF,aAAa,+BACZ,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;;oCAAa;oCACvC,YAAY,aAAa;oCAAC;;;;;;;;;;;;;kCAIlC,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;wBACjC,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;YAId,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wBAAC,WAAU;kCAAqB;;;;;;;;;;;uBAInD,4BACF;;kCACE,8OAAC,sIAAA,CAAA,aAAU,CAAC,eAAe;wBAAC,WAAU;kCACnC,YAAY,WAAW;;;;;;oBAGzB,YAAY,OAAO,kBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,QAAQ;;;;;;0CAE/B,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,WAAW;;;;;;;;;;;;;6CAMxC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;0BAAgB;;;;;;;;;;;;AAMtD", "debugId": null}}, {"offset": {"line": 1647, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/ReadingArea.tsx"], "sourcesContent": ["import React from 'react'\nimport { WordHighlighter } from './WordHighlighter'\nimport { TranslationCard } from './TranslationCard'\nimport { TranslationCardState } from '@/lib/types'\n\ninterface ReadingAreaProps {\n  content: string\n  currentWordIndex?: number\n  translationCard: TranslationCardState\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n  onTranslationClose: () => void\n  onTranslationPlayAudio: () => void\n}\n\nexport function ReadingArea({\n  content,\n  currentWordIndex,\n  translationCard,\n  onWordClick,\n  onTranslationClose,\n  onTranslationPlayAudio\n}: ReadingAreaProps) {\n  return (\n    <div className=\"flex-1 overflow-y-auto\">\n      <div className=\"max-w-sm mx-auto p-6\">\n        <div className=\"mb-6\">\n          <WordHighlighter\n            text={content}\n            currentWordIndex={currentWordIndex}\n            onWordClick={onWordClick}\n          />\n        </div>\n        \n        {translationCard.isVisible && (\n          <TranslationCard\n            word={translationCard.word}\n            translation={translationCard.translation}\n            isLoading={translationCard.isLoading}\n            onPlayAudio={onTranslationPlayAudio}\n            onClose={onTranslationClose}\n          />\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAYO,SAAS,YAAY,EAC1B,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,sBAAsB,EACL;IACjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,kBAAe;wBACd,MAAM;wBACN,kBAAkB;wBAClB,aAAa;;;;;;;;;;;gBAIhB,gBAAgB,SAAS,kBACxB,8OAAC,8IAAA,CAAA,kBAAe;oBACd,MAAM,gBAAgB,IAAI;oBAC1B,aAAa,gBAAgB,WAAW;oBACxC,WAAW,gBAAgB,SAAS;oBACpC,aAAa;oBACb,SAAS;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Slider.tsx"], "sourcesContent": ["import React from 'react'\nimport * as RadixSlider from '@radix-ui/react-slider'\n\ninterface SliderProps {\n  value: number[]\n  onValueChange: (value: number[]) => void\n  max?: number\n  min?: number\n  step?: number\n  className?: string\n  disabled?: boolean\n}\n\nexport function Slider({\n  value,\n  onValueChange,\n  max = 100,\n  min = 0,\n  step = 1,\n  className = '',\n  disabled = false,\n}: SliderProps) {\n  return (\n    <RadixSlider.Root\n      className={`relative flex items-center select-none touch-none w-full h-5 ${className}`}\n      value={value}\n      onValueChange={onValueChange}\n      max={max}\n      min={min}\n      step={step}\n      disabled={disabled}\n    >\n      <RadixSlider.Track className=\"bg-gray-200 relative grow rounded-full h-1\">\n        <RadixSlider.Range className=\"absolute bg-gray-900 rounded-full h-full\" />\n      </RadixSlider.Track>\n      <RadixSlider.Thumb\n        className=\"block w-4 h-4 bg-white border-2 border-gray-900 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n        aria-label=\"Volume\"\n      />\n    </RadixSlider.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAYO,SAAS,OAAO,EACrB,KAAK,EACL,aAAa,EACb,MAAM,GAAG,EACT,MAAM,CAAC,EACP,OAAO,CAAC,EACR,YAAY,EAAE,EACd,WAAW,KAAK,EACJ;IACZ,qBACE,8OAAC,kKAAA,CAAA,OAAgB;QACf,WAAW,CAAC,6DAA6D,EAAE,WAAW;QACtF,OAAO;QACP,eAAe;QACf,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;;0BAEV,8OAAC,kKAAA,CAAA,QAAiB;gBAAC,WAAU;0BAC3B,cAAA,8OAAC,kKAAA,CAAA,QAAiB;oBAAC,WAAU;;;;;;;;;;;0BAE/B,8OAAC,kKAAA,CAAA,QAAiB;gBAChB,WAAU;gBACV,cAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 1755, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/AudioPlayer.tsx"], "sourcesContent": ["import React from 'react'\nimport { PlayIcon, PauseIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Slider } from '../ui/Slider'\nimport { Typography } from '../ui/Typography'\nimport { AudioPlayerState } from '@/lib/types'\n\ninterface AudioPlayerProps {\n  state: AudioPlayerState\n  onPlayPause: () => void\n  onSeek: (time: number) => void\n  onVolumeChange: (volume: number) => void\n}\n\nfunction formatTime(seconds: number): string {\n  const minutes = Math.floor(seconds / 60)\n  const remainingSeconds = Math.floor(seconds % 60)\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n}\n\nexport function AudioPlayer({ state, onPlayPause, onSeek, onVolumeChange }: AudioPlayerProps) {\n  const { isPlaying, currentTime, duration, volume } = state\n\n  const handleProgressChange = (value: number[]) => {\n    onSeek(value[0])\n  }\n\n  const handleVolumeChange = (value: number[]) => {\n    onVolumeChange(value[0] / 100)\n  }\n\n  return (\n    <div className=\"bg-white border-t border-gray-200 p-4\">\n      {/* Progress Bar */}\n      <div className=\"mb-4\">\n        <Slider\n          value={[currentTime]}\n          onValueChange={handleProgressChange}\n          max={duration}\n          min={0}\n          step={0.1}\n          className=\"w-full\"\n        />\n        <div className=\"flex justify-between mt-2\">\n          <Typography.Micro>\n            {formatTime(currentTime)}\n          </Typography.Micro>\n          <Typography.Micro>\n            {formatTime(duration)}\n          </Typography.Micro>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex items-center justify-center\">\n        <IconButton\n          icon={isPlaying ? <PauseIcon className=\"w-6 h-6\" /> : <PlayIcon className=\"w-6 h-6\" />}\n          onClick={onPlayPause}\n          size=\"lg\"\n          className=\"bg-gray-900 text-white hover:bg-gray-800\"\n          aria-label={isPlaying ? 'Pause' : 'Play'}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAUA,SAAS,WAAW,OAAe;IACjC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;IAC9C,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAoB;IAC1F,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAErD,MAAM,uBAAuB,CAAC;QAC5B,OAAO,KAAK,CAAC,EAAE;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO;4BAAC;yBAAY;wBACpB,eAAe;wBACf,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;0CAEd,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;oBACT,MAAM,0BAAY,8OAAC,gLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;+CAAe,8OAAC,gLAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1E,SAAS;oBACT,MAAK;oBACL,WAAU;oBACV,cAAY,YAAY,UAAU;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 1871, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/PageNavigation.tsx"], "sourcesContent": ["import React from 'react'\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Typography } from '../ui/Typography'\n\ninterface PageNavigationProps {\n  currentPage: number\n  totalPages: number\n  onPreviousPage: () => void\n  onNextPage: () => void\n}\n\nexport function PageNavigation({ \n  currentPage, \n  totalPages, \n  onPreviousPage, \n  onNextPage \n}: PageNavigationProps) {\n  return (\n    <div className=\"flex items-center justify-between p-4 bg-white border-t border-gray-100\">\n      <IconButton\n        icon={<ChevronLeftIcon className=\"w-5 h-5\" />}\n        onClick={onPreviousPage}\n        variant=\"ghost\"\n        disabled={currentPage <= 1}\n        aria-label=\"Previous page\"\n      />\n      \n      <Typography.Caption className=\"text-gray-500\">\n        Page {currentPage} of {totalPages}\n      </Typography.Caption>\n      \n      <IconButton\n        icon={<ChevronRightIcon className=\"w-5 h-5\" />}\n        onClick={onNextPage}\n        variant=\"ghost\"\n        disabled={currentPage >= totalPages}\n        aria-label=\"Next page\"\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACU;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;gBACjC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;0BAGb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;;oBAAgB;oBACtC;oBAAY;oBAAK;;;;;;;0BAGzB,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAClC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 1944, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/KeyboardShortcutsHelp.tsx"], "sourcesContent": ["import React, { useState } from 'react'\nimport { QuestionMarkCircledIcon, Cross2Icon } from '@radix-ui/react-icons'\nimport { IconButton } from './IconButton'\nimport { Typography } from './Typography'\n\nexport function KeyboardShortcutsHelp() {\n  const [isVisible, setIsVisible] = useState(false)\n\n  const shortcuts = [\n    { key: 'Space', description: 'Play/Pause audio' },\n    { key: 'Shift + →', description: 'Skip forward 10s' },\n    { key: 'Shift + ←', description: 'Skip backward 10s' },\n    { key: 'Esc', description: 'Close translation' },\n  ]\n\n  if (!isVisible) {\n    return (\n      <IconButton\n        icon={<QuestionMarkCircledIcon className=\"w-4 h-4\" />}\n        onClick={() => setIsVisible(true)}\n        variant=\"ghost\"\n        size=\"sm\"\n        aria-label=\"Show keyboard shortcuts\"\n        className=\"fixed bottom-20 right-4 bg-white shadow-lg border border-gray-200\"\n      />\n    )\n  }\n\n  return (\n    <div className=\"fixed bottom-20 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 max-w-xs z-50\">\n      <div className=\"flex items-center justify-between mb-3\">\n        <Typography.Caption className=\"font-semibold text-gray-700\">\n          Keyboard Shortcuts\n        </Typography.Caption>\n        <IconButton\n          icon={<Cross2Icon className=\"w-3 h-3\" />}\n          onClick={() => setIsVisible(false)}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Close shortcuts help\"\n        />\n      </div>\n      \n      <div className=\"space-y-2\">\n        {shortcuts.map((shortcut, index) => (\n          <div key={index} className=\"flex items-center justify-between\">\n            <Typography.Micro className=\"font-mono bg-gray-100 px-2 py-1 rounded text-gray-700\">\n              {shortcut.key}\n            </Typography.Micro>\n            <Typography.Micro className=\"text-gray-600 ml-3\">\n              {shortcut.description}\n            </Typography.Micro>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY;QAChB;YAAE,KAAK;YAAS,aAAa;QAAmB;QAChD;YAAE,KAAK;YAAa,aAAa;QAAmB;QACpD;YAAE,KAAK;YAAa,aAAa;QAAoB;QACrD;YAAE,KAAK;YAAO,aAAa;QAAoB;KAChD;IAED,IAAI,CAAC,WAAW;QACd,qBACE,8OAAC,sIAAA,CAAA,aAAU;YACT,oBAAM,8OAAC,gLAAA,CAAA,0BAAuB;gBAAC,WAAU;;;;;;YACzC,SAAS,IAAM,aAAa;YAC5B,SAAQ;YACR,MAAK;YACL,cAAW;YACX,WAAU;;;;;;IAGhB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;wBAAC,WAAU;kCAA8B;;;;;;kCAG5D,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;wBAC5B,SAAS,IAAM,aAAa;wBAC5B,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;0BAIf,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB,SAAS,GAAG;;;;;;0CAEf,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gCAAC,WAAU;0CACzB,SAAS,WAAW;;;;;;;uBALf;;;;;;;;;;;;;;;;AAYpB", "debugId": null}}, {"offset": {"line": 2077, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useAudioPlayer.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect, useCallback } from 'react'\nimport { AudioPlayerState, WordTiming } from '../types'\n\ninterface UseAudioPlayerProps {\n  audioUrl?: string\n  wordTimings?: WordTiming[]\n  onWordHighlight?: (wordIndex: number) => void\n}\n\nexport function useAudioPlayer({ \n  audioUrl, \n  wordTimings = [], \n  onWordHighlight \n}: UseAudioPlayerProps) {\n  const audioRef = useRef<HTMLAudioElement | null>(null)\n  const [state, setState] = useState<AudioPlayerState>({\n    isPlaying: false,\n    currentTime: 0,\n    duration: 0,\n    volume: 1.0\n  })\n\n  // Initialize audio element\n  useEffect(() => {\n    if (!audioUrl) return\n\n    const audio = new Audio(audioUrl)\n    audioRef.current = audio\n\n    const handleLoadedMetadata = () => {\n      setState(prev => ({\n        ...prev,\n        duration: audio.duration || 0\n      }))\n    }\n\n    const handleTimeUpdate = () => {\n      setState(prev => ({\n        ...prev,\n        currentTime: audio.currentTime\n      }))\n\n      // Find current word based on time\n      if (wordTimings.length > 0 && onWordHighlight) {\n        const currentWordIndex = wordTimings.findIndex(\n          timing => audio.currentTime >= timing.startTime && audio.currentTime <= timing.endTime\n        )\n        if (currentWordIndex !== -1) {\n          onWordHighlight(currentWordIndex)\n        }\n      }\n    }\n\n    const handleEnded = () => {\n      setState(prev => ({\n        ...prev,\n        isPlaying: false,\n        currentTime: 0\n      }))\n    }\n\n    const handleError = (e: Event) => {\n      console.error('Audio error:', e)\n      setState(prev => ({\n        ...prev,\n        isPlaying: false\n      }))\n    }\n\n    audio.addEventListener('loadedmetadata', handleLoadedMetadata)\n    audio.addEventListener('timeupdate', handleTimeUpdate)\n    audio.addEventListener('ended', handleEnded)\n    audio.addEventListener('error', handleError)\n\n    return () => {\n      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)\n      audio.removeEventListener('timeupdate', handleTimeUpdate)\n      audio.removeEventListener('ended', handleEnded)\n      audio.removeEventListener('error', handleError)\n      audio.pause()\n    }\n  }, [audioUrl, wordTimings, onWordHighlight])\n\n  const play = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.play()\n      setState(prev => ({ ...prev, isPlaying: true }))\n    }\n  }, [])\n\n  const pause = useCallback(() => {\n    if (audioRef.current) {\n      audioRef.current.pause()\n      setState(prev => ({ ...prev, isPlaying: false }))\n    }\n  }, [])\n\n  const togglePlayPause = useCallback(() => {\n    if (state.isPlaying) {\n      pause()\n    } else {\n      play()\n    }\n  }, [state.isPlaying, play, pause])\n\n  const seek = useCallback((time: number) => {\n    if (audioRef.current) {\n      audioRef.current.currentTime = time\n      setState(prev => ({ ...prev, currentTime: time }))\n    }\n  }, [])\n\n  const setVolume = useCallback((volume: number) => {\n    if (audioRef.current) {\n      audioRef.current.volume = volume\n      setState(prev => ({ ...prev, volume }))\n    }\n  }, [])\n\n  return {\n    state,\n    play,\n    pause,\n    togglePlayPause,\n    seek,\n    setVolume,\n    isReady: !!audioRef.current && state.duration > 0\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAWO,SAAS,eAAe,EAC7B,QAAQ,EACR,cAAc,EAAE,EAChB,eAAe,EACK;IACpB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,WAAW;QACX,aAAa;QACb,UAAU;QACV,QAAQ;IACV;IAEA,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,QAAQ,IAAI,MAAM;QACxB,SAAS,OAAO,GAAG;QAEnB,MAAM,uBAAuB;YAC3B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,UAAU,MAAM,QAAQ,IAAI;gBAC9B,CAAC;QACH;QAEA,MAAM,mBAAmB;YACvB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,aAAa,MAAM,WAAW;gBAChC,CAAC;YAED,kCAAkC;YAClC,IAAI,YAAY,MAAM,GAAG,KAAK,iBAAiB;gBAC7C,MAAM,mBAAmB,YAAY,SAAS,CAC5C,CAAA,SAAU,MAAM,WAAW,IAAI,OAAO,SAAS,IAAI,MAAM,WAAW,IAAI,OAAO,OAAO;gBAExF,IAAI,qBAAqB,CAAC,GAAG;oBAC3B,gBAAgB;gBAClB;YACF;QACF;QAEA,MAAM,cAAc;YAClB,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,aAAa;gBACf,CAAC;QACH;QAEA,MAAM,cAAc,CAAC;YACnB,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;gBACb,CAAC;QACH;QAEA,MAAM,gBAAgB,CAAC,kBAAkB;QACzC,MAAM,gBAAgB,CAAC,cAAc;QACrC,MAAM,gBAAgB,CAAC,SAAS;QAChC,MAAM,gBAAgB,CAAC,SAAS;QAEhC,OAAO;YACL,MAAM,mBAAmB,CAAC,kBAAkB;YAC5C,MAAM,mBAAmB,CAAC,cAAc;YACxC,MAAM,mBAAmB,CAAC,SAAS;YACnC,MAAM,mBAAmB,CAAC,SAAS;YACnC,MAAM,KAAK;QACb;IACF,GAAG;QAAC;QAAU;QAAa;KAAgB;IAE3C,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,IAAI;YACrB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAK,CAAC;QAChD;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACxB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;YACtB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;gBAAM,CAAC;QACjD;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,IAAI,MAAM,SAAS,EAAE;YACnB;QACF,OAAO;YACL;QACF;IACF,GAAG;QAAC,MAAM,SAAS;QAAE;QAAM;KAAM;IAEjC,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,WAAW,GAAG;YAC/B,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,aAAa;gBAAK,CAAC;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7B,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,MAAM,GAAG;YAC1B,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE;gBAAO,CAAC;QACvC;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,SAAS,CAAC,CAAC,SAAS,OAAO,IAAI,MAAM,QAAQ,GAAG;IAClD;AACF", "debugId": null}}, {"offset": {"line": 2206, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useTranslation.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { TranslationResult } from '../types'\n\n// Mock translation service - in a real app this would call an API\nconst mockTranslationService = {\n  async translateWord(word: string, fromLang: string, toLang: string): Promise<TranslationResult> {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 800))\n    \n    // Enhanced mock translations with more examples\n    const mockTranslations: Record<string, TranslationResult> = {\n      'plane': {\n        translation: 'avion',\n        pronunciation: 'a-vi-ɔ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'I like riding on planes.',\n          translation: 'J\\'aime voyager en avion.'\n        }\n      },\n      'aircraft': {\n        translation: 'aéronef',\n        pronunciation: 'a-e-ʁo-nɛf',\n        provider: 'mock-translate',\n        example: {\n          original: 'The aircraft landed safely.',\n          translation: 'L\\'aéronef a atterri en sécurité.'\n        }\n      },\n      'huge': {\n        translation: 'énorme',\n        pronunciation: 'e-nɔʁm',\n        provider: 'mock-translate',\n        example: {\n          original: 'That\\'s a huge building.',\n          translation: 'C\\'est un énorme bâtiment.'\n        }\n      },\n      'cloud': {\n        translation: 'nuage',\n        pronunciation: 'ny-aʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'The cloud is white.',\n          translation: 'Le nuage est blanc.'\n        }\n      },\n      'airport': {\n        translation: 'aéroport',\n        pronunciation: 'a-e-ʁo-pɔʁ',\n        provider: 'mock-translate',\n        example: {\n          original: 'We arrived at the airport.',\n          translation: 'Nous sommes arrivés à l\\'aéroport.'\n        }\n      },\n      'seat': {\n        translation: 'siège',\n        pronunciation: 'sjɛʒ',\n        provider: 'mock-translate',\n        example: {\n          original: 'Please take a seat.',\n          translation: 'Veuillez prendre un siège.'\n        }\n      },\n      'time': {\n        translation: 'temps',\n        pronunciation: 'tɑ̃',\n        provider: 'mock-translate',\n        example: {\n          original: 'What time is it?',\n          translation: 'Quelle heure est-il?'\n        }\n      },\n      'world': {\n        translation: 'monde',\n        pronunciation: 'mɔ̃d',\n        provider: 'mock-translate',\n        example: {\n          original: 'The world is beautiful.',\n          translation: 'Le monde est beau.'\n        }\n      }\n    }\n    \n    const cleanWord = word.toLowerCase().replace(/[.,!?;:]/, '')\n    const translation = mockTranslations[cleanWord]\n    \n    if (translation) {\n      return translation\n    }\n    \n    // Fallback for unknown words\n    return {\n      translation: `[Translation for \"${word}\" not available]`,\n      provider: 'mock-translate',\n      confidence: 0.5\n    }\n  }\n}\n\ninterface UseTranslationState {\n  isLoading: boolean\n  error: string | null\n  lastTranslation: TranslationResult | null\n}\n\nexport function useTranslation() {\n  const [state, setState] = useState<UseTranslationState>({\n    isLoading: false,\n    error: null,\n    lastTranslation: null\n  })\n\n  const translateWord = useCallback(async (\n    word: string, \n    fromLang: string = 'en', \n    toLang: string = 'fr'\n  ): Promise<TranslationResult | null> => {\n    setState(prev => ({\n      ...prev,\n      isLoading: true,\n      error: null\n    }))\n\n    try {\n      const result = await mockTranslationService.translateWord(word, fromLang, toLang)\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        lastTranslation: result\n      }))\n      \n      return result\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Translation failed'\n      \n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        error: errorMessage\n      }))\n      \n      return null\n    }\n  }, [])\n\n  const clearError = useCallback(() => {\n    setState(prev => ({ ...prev, error: null }))\n  }, [])\n\n  return {\n    ...state,\n    translateWord,\n    clearError\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAKA,kEAAkE;AAClE,MAAM,yBAAyB;IAC7B,MAAM,eAAc,IAAY,EAAE,QAAgB,EAAE,MAAc;QAChE,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gDAAgD;QAChD,MAAM,mBAAsD;YAC1D,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,YAAY;gBACV,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,WAAW;gBACT,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,QAAQ;gBACN,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;YACA,SAAS;gBACP,aAAa;gBACb,eAAe;gBACf,UAAU;gBACV,SAAS;oBACP,UAAU;oBACV,aAAa;gBACf;YACF;QACF;QAEA,MAAM,YAAY,KAAK,WAAW,GAAG,OAAO,CAAC,YAAY;QACzD,MAAM,cAAc,gBAAgB,CAAC,UAAU;QAE/C,IAAI,aAAa;YACf,OAAO;QACT;QAEA,6BAA6B;QAC7B,OAAO;YACL,aAAa,CAAC,kBAAkB,EAAE,KAAK,gBAAgB,CAAC;YACxD,UAAU;YACV,YAAY;QACd;IACF;AACF;AAQO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,WAAW;QACX,OAAO;QACP,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAChC,MACA,WAAmB,IAAI,EACvB,SAAiB,IAAI;QAErB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,WAAW;gBACX,OAAO;YACT,CAAC;QAED,IAAI;YACF,MAAM,SAAS,MAAM,uBAAuB,aAAa,CAAC,MAAM,UAAU;YAE1E,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,iBAAiB;gBACnB,CAAC;YAED,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAE9D,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,CAAC;YAED,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC7B,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO;YAAK,CAAC;IAC5C,GAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/lib/hooks/useKeyboardShortcuts.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\n\ninterface KeyboardShortcuts {\n  onPlayPause?: () => void\n  onSeekForward?: () => void\n  onSeekBackward?: () => void\n  onCloseTranslation?: () => void\n}\n\nexport function useKeyboardShortcuts({\n  onPlayPause,\n  onSeekForward,\n  onSeekBackward,\n  onCloseTranslation\n}: KeyboardShortcuts) {\n  useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      // Don't trigger shortcuts if user is typing in an input\n      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {\n        return\n      }\n\n      switch (event.code) {\n        case 'Space':\n          event.preventDefault()\n          onPlayPause?.()\n          break\n        case 'ArrowRight':\n          if (event.shiftKey) {\n            event.preventDefault()\n            onSeekForward?.()\n          }\n          break\n        case 'ArrowLeft':\n          if (event.shiftKey) {\n            event.preventDefault()\n            onSeekBackward?.()\n          }\n          break\n        case 'Escape':\n          event.preventDefault()\n          onCloseTranslation?.()\n          break\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [onPlayPause, onSeekForward, onSeekBackward, onCloseTranslation])\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAWO,SAAS,qBAAqB,EACnC,WAAW,EACX,aAAa,EACb,cAAc,EACd,kBAAkB,EACA;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,wDAAwD;YACxD,IAAI,MAAM,MAAM,YAAY,oBAAoB,MAAM,MAAM,YAAY,qBAAqB;gBAC3F;YACF;YAEA,OAAQ,MAAM,IAAI;gBAChB,KAAK;oBACH,MAAM,cAAc;oBACpB;oBACA;gBACF,KAAK;oBACH,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,cAAc;wBACpB;oBACF;oBACA;gBACF,KAAK;oBACH,IAAI,MAAM,QAAQ,EAAE;wBAClB,MAAM,cAAc;wBACpB;oBACF;oBACA;gBACF,KAAK;oBACH,MAAM,cAAc;oBACpB;oBACA;YACJ;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAa;QAAe;QAAgB;KAAmB;AACrE", "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/StoryReaderScreen.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { StoryHeader } from '../story/StoryHeader'\nimport { ReadingArea } from '../story/ReadingArea'\nimport { AudioPlayer } from '../story/AudioPlayer'\nimport { PageNavigation } from '../story/PageNavigation'\nimport { ErrorBoundary } from '../ui/ErrorBoundary'\nimport { KeyboardShortcutsHelp } from '../ui/KeyboardShortcutsHelp'\nimport { useAudioPlayer } from '@/lib/hooks/useAudioPlayer'\nimport { useTranslation } from '@/lib/hooks/useTranslation'\nimport { useKeyboardShortcuts } from '@/lib/hooks/useKeyboardShortcuts'\nimport { Story, StoryReaderState, TranslationResult } from '@/lib/types'\n\ninterface StoryReaderScreenProps {\n  story: Story\n}\n\n\n\nexport function StoryReaderScreen({ story }: StoryReaderScreenProps) {\n  const [state, setState] = useState<StoryReaderState>({\n    currentWordIndex: -1,\n    selectedWord: undefined,\n    translationCard: {\n      isVisible: false,\n      word: '',\n      position: { x: 0, y: 0 },\n      translation: undefined,\n      isLoading: false\n    },\n    audioPlayer: {\n      isPlaying: false,\n      currentTime: 0,\n      duration: 0,\n      volume: 1.0\n    }\n  })\n\n  // Handle word highlighting during audio playback\n  const handleWordHighlight = useCallback((wordIndex: number) => {\n    setState(prev => ({\n      ...prev,\n      currentWordIndex: wordIndex\n    }))\n  }, [])\n\n  // Use translation hook\n  const translation = useTranslation()\n\n  // Use audio player hook with real functionality\n  const audioPlayer = useAudioPlayer({\n    audioUrl: story.audioUrl,\n    wordTimings: story.wordTimings,\n    onWordHighlight: handleWordHighlight\n  })\n\n  // Update state when audio player state changes\n  React.useEffect(() => {\n    setState(prev => ({\n      ...prev,\n      audioPlayer: audioPlayer.state\n    }))\n  }, [audioPlayer.state])\n\n  const handleWordClick = useCallback(async (word: string, index: number, event: React.MouseEvent) => {\n    // Close existing translation card if clicking the same word\n    if (state.translationCard.isVisible && state.translationCard.word === word) {\n      setState(prev => ({\n        ...prev,\n        translationCard: { ...prev.translationCard, isVisible: false }\n      }))\n      return\n    }\n\n    // Show loading state\n    setState(prev => ({\n      ...prev,\n      selectedWord: word,\n      translationCard: {\n        isVisible: true,\n        word,\n        position: { x: event.clientX, y: event.clientY },\n        translation: undefined,\n        isLoading: true\n      }\n    }))\n\n    // Get translation using the hook\n    const translationResult = await translation.translateWord(word, 'en', 'fr')\n\n    setState(prev => ({\n      ...prev,\n      translationCard: {\n        ...prev.translationCard,\n        translation: translationResult || undefined,\n        isLoading: false\n      }\n    }))\n  }, [state.translationCard.isVisible, state.translationCard.word, translation])\n\n  const handleTranslationClose = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      translationCard: { ...prev.translationCard, isVisible: false }\n    }))\n  }, [])\n\n  const handlePlayPause = useCallback(() => {\n    audioPlayer.togglePlayPause()\n  }, [audioPlayer])\n\n  const handleSeek = useCallback((time: number) => {\n    audioPlayer.seek(time)\n  }, [audioPlayer])\n\n  const handleVolumeChange = useCallback((volume: number) => {\n    audioPlayer.setVolume(volume)\n  }, [audioPlayer])\n\n  const handleSeekForward = useCallback(() => {\n    const newTime = Math.min(state.audioPlayer.currentTime + 10, state.audioPlayer.duration)\n    audioPlayer.seek(newTime)\n  }, [audioPlayer, state.audioPlayer.currentTime, state.audioPlayer.duration])\n\n  const handleSeekBackward = useCallback(() => {\n    const newTime = Math.max(state.audioPlayer.currentTime - 10, 0)\n    audioPlayer.seek(newTime)\n  }, [audioPlayer, state.audioPlayer.currentTime])\n\n  // Setup keyboard shortcuts\n  useKeyboardShortcuts({\n    onPlayPause: handlePlayPause,\n    onSeekForward: handleSeekForward,\n    onSeekBackward: handleSeekBackward,\n    onCloseTranslation: handleTranslationClose\n  })\n\n  return (\n    <ErrorBoundary>\n      <div className=\"flex flex-col h-screen bg-white\">\n        <StoryHeader\n          title={story.title}\n          chapter={story.chapter}\n          isAudioPlaying={state.audioPlayer.isPlaying}\n          onBookmarkClick={() => console.log('Bookmark clicked')}\n          onMenuClick={() => console.log('Menu clicked')}\n        />\n        \n        <ReadingArea\n          content={story.content}\n          currentWordIndex={state.currentWordIndex}\n          translationCard={state.translationCard}\n          onWordClick={handleWordClick}\n          onTranslationClose={handleTranslationClose}\n          onTranslationPlayAudio={() => console.log('Play translation audio')}\n        />\n        \n        <PageNavigation\n          currentPage={24}\n          totalPages={120}\n          onPreviousPage={() => console.log('Previous page')}\n          onNextPage={() => console.log('Next page')}\n        />\n        \n        <AudioPlayer\n          state={state.audioPlayer}\n          onPlayPause={handlePlayPause}\n          onSeek={handleSeek}\n          onVolumeChange={handleVolumeChange}\n        />\n\n        <KeyboardShortcutsHelp />\n      </div>\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAoBO,SAAS,kBAAkB,EAAE,KAAK,EAA0B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,kBAAkB,CAAC;QACnB,cAAc;QACd,iBAAiB;YACf,WAAW;YACX,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAG,GAAG;YAAE;YACvB,aAAa;YACb,WAAW;QACb;QACA,aAAa;YACX,WAAW;YACX,aAAa;YACb,UAAU;YACV,QAAQ;QACV;IACF;IAEA,iDAAiD;IACjD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,kBAAkB;YACpB,CAAC;IACH,GAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IAEjC,gDAAgD;IAChD,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;QACjC,UAAU,MAAM,QAAQ;QACxB,aAAa,MAAM,WAAW;QAC9B,iBAAiB;IACnB;IAEA,+CAA+C;IAC/C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa,YAAY,KAAK;YAChC,CAAC;IACH,GAAG;QAAC,YAAY,KAAK;KAAC;IAEtB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAc,OAAe;QACtE,4DAA4D;QAC5D,IAAI,MAAM,eAAe,CAAC,SAAS,IAAI,MAAM,eAAe,CAAC,IAAI,KAAK,MAAM;YAC1E,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;wBAAE,GAAG,KAAK,eAAe;wBAAE,WAAW;oBAAM;gBAC/D,CAAC;YACD;QACF;QAEA,qBAAqB;QACrB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,iBAAiB;oBACf,WAAW;oBACX;oBACA,UAAU;wBAAE,GAAG,MAAM,OAAO;wBAAE,GAAG,MAAM,OAAO;oBAAC;oBAC/C,aAAa;oBACb,WAAW;gBACb;YACF,CAAC;QAED,iCAAiC;QACjC,MAAM,oBAAoB,MAAM,YAAY,aAAa,CAAC,MAAM,MAAM;QAEtE,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,iBAAiB;oBACf,GAAG,KAAK,eAAe;oBACvB,aAAa,qBAAqB;oBAClC,WAAW;gBACb;YACF,CAAC;IACH,GAAG;QAAC,MAAM,eAAe,CAAC,SAAS;QAAE,MAAM,eAAe,CAAC,IAAI;QAAE;KAAY;IAE7E,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,iBAAiB;oBAAE,GAAG,KAAK,eAAe;oBAAE,WAAW;gBAAM;YAC/D,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY,eAAe;IAC7B,GAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,YAAY,IAAI,CAAC;IACnB,GAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,YAAY,SAAS,CAAC;IACxB,GAAG;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW,GAAG,IAAI,MAAM,WAAW,CAAC,QAAQ;QACvF,YAAY,IAAI,CAAC;IACnB,GAAG;QAAC;QAAa,MAAM,WAAW,CAAC,WAAW;QAAE,MAAM,WAAW,CAAC,QAAQ;KAAC;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW,GAAG,IAAI;QAC7D,YAAY,IAAI,CAAC;IACnB,GAAG;QAAC;QAAa,MAAM,WAAW,CAAC,WAAW;KAAC;IAE/C,2BAA2B;IAC3B,CAAA,GAAA,2IAAA,CAAA,uBAAoB,AAAD,EAAE;QACnB,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,qBACE,8OAAC,yIAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,KAAK;oBAClB,SAAS,MAAM,OAAO;oBACtB,gBAAgB,MAAM,WAAW,CAAC,SAAS;oBAC3C,iBAAiB,IAAM,QAAQ,GAAG,CAAC;oBACnC,aAAa,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGjC,8OAAC,0IAAA,CAAA,cAAW;oBACV,SAAS,MAAM,OAAO;oBACtB,kBAAkB,MAAM,gBAAgB;oBACxC,iBAAiB,MAAM,eAAe;oBACtC,aAAa;oBACb,oBAAoB;oBACpB,wBAAwB,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAG5C,8OAAC,6IAAA,CAAA,iBAAc;oBACb,aAAa;oBACb,YAAY;oBACZ,gBAAgB,IAAM,QAAQ,GAAG,CAAC;oBAClC,YAAY,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGhC,8OAAC,0IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,WAAW;oBACxB,aAAa;oBACb,QAAQ;oBACR,gBAAgB;;;;;;8BAGlB,8OAAC,iJAAA,CAAA,wBAAqB;;;;;;;;;;;;;;;;AAI9B", "debugId": null}}, {"offset": {"line": 2627, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useCallback } from 'react'\nimport { AdminScreen } from '@/components/screens/AdminScreen'\nimport { StoryForm } from '@/components/story/StoryForm'\nimport { StoryReaderScreen } from '@/components/screens/StoryReaderScreen'\nimport { Story } from '@/lib/types'\n\ninterface StoryFormData {\n  title: string\n  chapter?: string\n  content: string\n  language: string\n  difficulty: 'beginner' | 'intermediate' | 'advanced'\n  voice?: string\n  speed: number\n  generateAudio: boolean\n}\n\nexport default function AdminPage() {\n  const [stories, setStories] = useState<Story[]>([])\n  const [isLoading, setIsLoading] = useState(true)\n  const [showForm, setShowForm] = useState(false)\n  const [editingStory, setEditingStory] = useState<Story | null>(null)\n  const [previewStory, setPreviewStory] = useState<Story | null>(null)\n\n  // Load stories on mount\n  useEffect(() => {\n    loadStories()\n  }, [])\n\n  const loadStories = async () => {\n    try {\n      const response = await fetch('/api/stories')\n      if (response.ok) {\n        const storiesData = await response.json()\n        setStories(storiesData)\n      } else {\n        console.error('Failed to load stories')\n      }\n    } catch (error) {\n      console.error('Error loading stories:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleCreateStory = useCallback(() => {\n    setEditingStory(null)\n    setShowForm(true)\n  }, [])\n\n  const handleEditStory = useCallback((story: Story) => {\n    setEditingStory(story)\n    setShowForm(true)\n  }, [])\n\n  const handleDeleteStory = useCallback(async (storyId: string) => {\n    if (!confirm('Are you sure you want to delete this story?')) {\n      return\n    }\n\n    try {\n      const response = await fetch(`/api/stories/${storyId}`, {\n        method: 'DELETE'\n      })\n\n      if (response.ok) {\n        setStories(prev => prev.filter(story => story.id !== storyId))\n      } else {\n        alert('Failed to delete story')\n      }\n    } catch (error) {\n      console.error('Error deleting story:', error)\n      alert('Failed to delete story')\n    }\n  }, [])\n\n  const handlePreviewStory = useCallback((story: Story) => {\n    setPreviewStory(story)\n  }, [])\n\n  const handleSaveStory = useCallback(async (data: StoryFormData) => {\n    try {\n      const response = await fetch('/api/stories', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data)\n      })\n\n      if (response.ok) {\n        const newStory = await response.json()\n        setStories(prev => [...prev, newStory])\n        setShowForm(false)\n        setEditingStory(null)\n      } else {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to save story')\n      }\n    } catch (error) {\n      console.error('Error saving story:', error)\n      throw error\n    }\n  }, [])\n\n  const handlePreviewFormStory = useCallback(async (data: StoryFormData) => {\n    try {\n      const response = await fetch('/api/stories/process', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(data)\n      })\n\n      if (response.ok) {\n        const result = await response.json()\n        setPreviewStory(result.story)\n        setShowForm(false)\n      } else {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to process story')\n      }\n    } catch (error) {\n      console.error('Error processing story:', error)\n      throw error\n    }\n  }, [])\n\n  const handleCloseForm = useCallback(() => {\n    setShowForm(false)\n    setEditingStory(null)\n  }, [])\n\n  const handleClosePreview = useCallback(() => {\n    setPreviewStory(null)\n  }, [])\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading stories...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show preview mode\n  if (previewStory) {\n    return (\n      <div className=\"relative\">\n        <button\n          onClick={handleClosePreview}\n          className=\"absolute top-4 left-4 z-50 px-4 py-2 bg-gray-800 text-white rounded-lg hover:bg-gray-900 transition-colors\"\n        >\n          ← Back to Admin\n        </button>\n        <StoryReaderScreen story={previewStory} />\n      </div>\n    )\n  }\n\n  return (\n    <>\n      <AdminScreen\n        stories={stories}\n        onCreateStory={handleCreateStory}\n        onEditStory={handleEditStory}\n        onDeleteStory={handleDeleteStory}\n        onPreviewStory={handlePreviewStory}\n      />\n\n      <StoryForm\n        story={editingStory}\n        isOpen={showForm}\n        onClose={handleCloseForm}\n        onSave={handleSaveStory}\n        onPreview={handlePreviewFormStory}\n      />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAE/D,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,cAAc,MAAM,SAAS,IAAI;gBACvC,WAAW;YACb,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,gBAAgB;QAChB,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,gBAAgB;QAChB,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC3C,IAAI,CAAC,QAAQ,gDAAgD;YAC3D;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,SAAS,EAAE;gBACtD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACvD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,gBAAgB;IAClB,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACzC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,WAAW,CAAA,OAAQ;2BAAI;wBAAM;qBAAS;gBACtC,YAAY;gBACZ,gBAAgB;YAClB,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,gBAAgB,OAAO,KAAK;gBAC5B,YAAY;YACd,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,YAAY;QACZ,gBAAgB;IAClB,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,gBAAgB;IAClB,GAAG,EAAE;IAEL,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,oBAAoB;IACpB,IAAI,cAAc;QAChB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;8BAGD,8OAAC,kJAAA,CAAA,oBAAiB;oBAAC,OAAO;;;;;;;;;;;;IAGhC;IAEA,qBACE;;0BACE,8OAAC,4IAAA,CAAA,cAAW;gBACV,SAAS;gBACT,eAAe;gBACf,aAAa;gBACb,eAAe;gBACf,gBAAgB;;;;;;0BAGlB,8OAAC,wIAAA,CAAA,YAAS;gBACR,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,WAAW;;;;;;;;AAInB", "debugId": null}}]}