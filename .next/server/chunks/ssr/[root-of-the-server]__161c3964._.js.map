{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Typography.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface TypographyProps {\n  children: React.ReactNode\n  className?: string\n}\n\nexport const Typography = {\n  Title: ({ children, className = '' }: TypographyProps) => (\n    <h1 className={`text-2xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </h1>\n  ),\n  \n  Subtitle: ({ children, className = '' }: TypographyProps) => (\n    <h2 className={`text-base text-gray-600 ${className}`}>\n      {children}\n    </h2>\n  ),\n  \n  Body: ({ children, className = '' }: TypographyProps) => (\n    <p className={`text-lg text-gray-800 leading-relaxed ${className}`}>\n      {children}\n    </p>\n  ),\n  \n  Caption: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-sm text-gray-500 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationWord: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xl font-bold text-gray-900 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  TranslationText: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-base text-gray-700 ${className}`}>\n      {children}\n    </span>\n  ),\n\n  Micro: ({ children, className = '' }: TypographyProps) => (\n    <span className={`text-xs text-gray-400 ${className}`}>\n      {children}\n    </span>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAOO,MAAM,aAAa;IACxB,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACnD,8OAAC;YAAG,WAAW,CAAC,iCAAiC,EAAE,WAAW;sBAC3D;;;;;;IAIL,UAAU,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACtD,8OAAC;YAAG,WAAW,CAAC,wBAAwB,EAAE,WAAW;sBAClD;;;;;;IAIL,MAAM,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAClD,8OAAC;YAAE,WAAW,CAAC,sCAAsC,EAAE,WAAW;sBAC/D;;;;;;IAIL,SAAS,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACrD,8OAAC;YAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW;sBAClD;;;;;;IAIL,iBAAiB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAC7D,8OAAC;YAAK,WAAW,CAAC,gCAAgC,EAAE,WAAW;sBAC5D;;;;;;IAIL,iBAAiB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBAC7D,8OAAC;YAAK,WAAW,CAAC,wBAAwB,EAAE,WAAW;sBACpD;;;;;;IAIL,OAAO,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAmB,iBACnD,8OAAC;YAAK,WAAW,CAAC,sBAAsB,EAAE,WAAW;sBAClD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/IconButton.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface IconButtonProps {\n  icon: React.ReactNode\n  onClick: () => void\n  variant?: 'default' | 'ghost' | 'outline'\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n  disabled?: boolean\n  'aria-label'?: string\n}\n\nexport function IconButton({\n  icon,\n  onClick,\n  variant = 'default',\n  size = 'md',\n  className = '',\n  disabled = false,\n  'aria-label': ariaLabel,\n}: IconButtonProps) {\n  const baseClasses = 'inline-flex items-center justify-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'\n  \n  const variantClasses = {\n    default: 'bg-gray-100 hover:bg-gray-200 text-gray-700',\n    ghost: 'hover:bg-gray-100 text-gray-600',\n    outline: 'border border-gray-300 hover:bg-gray-50 text-gray-700'\n  }\n  \n  const sizeClasses = {\n    sm: 'w-8 h-8 text-sm',\n    md: 'w-10 h-10 text-base',\n    lg: 'w-12 h-12 text-lg'\n  }\n  \n  return (\n    <button\n      onClick={onClick}\n      disabled={disabled}\n      aria-label={ariaLabel}\n      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}\n    >\n      {icon}\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAYO,SAAS,WAAW,EACzB,IAAI,EACJ,OAAO,EACP,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,cAAc,SAAS,EACP;IAChB,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,OAAO;QACP,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,cAAY;QACZ,WAAW,GAAG,YAAY,CAAC,EAAE,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;kBAEvF;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/StoryHeader.tsx"], "sourcesContent": ["import React from 'react'\nimport { BookmarkIcon, HamburgerMenuIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\n\ninterface StoryHeaderProps {\n  title: string\n  chapter?: string\n  onBookmarkClick: () => void\n  onMenuClick: () => void\n}\n\nexport function StoryHeader({ title, chapter, onBookmarkClick, onMenuClick }: StoryHeaderProps) {\n  return (\n    <header className=\"flex items-center justify-between p-4 bg-white border-b border-gray-100\">\n      <div className=\"flex-1\">\n        <Typography.Title className=\"text-xl font-bold text-gray-900\">\n          {title}\n        </Typography.Title>\n        {chapter && (\n          <Typography.Subtitle className=\"text-sm text-gray-600 mt-1\">\n            {chapter}\n          </Typography.Subtitle>\n        )}\n      </div>\n      \n      <div className=\"flex items-center space-x-2\">\n        <IconButton\n          icon={<BookmarkIcon className=\"w-5 h-5\" />}\n          onClick={onBookmarkClick}\n          variant=\"ghost\"\n          aria-label=\"Bookmark story\"\n        />\n        <IconButton\n          icon={<HamburgerMenuIcon className=\"w-5 h-5\" />}\n          onClick={onMenuClick}\n          variant=\"ghost\"\n          aria-label=\"Open menu\"\n        />\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,WAAW,EAAoB;IAC5F,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;wBAAC,WAAU;kCACzB;;;;;;oBAEF,yBACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,QAAQ;wBAAC,WAAU;kCAC5B;;;;;;;;;;;;0BAKP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAC9B,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;kCAEb,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;wBACnC,SAAS;wBACT,SAAQ;wBACR,cAAW;;;;;;;;;;;;;;;;;;AAKrB", "debugId": null}}, {"offset": {"line": 205, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/WordHighlighter.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface WordHighlighterProps {\n  text: string\n  currentWordIndex?: number\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n}\n\nexport function WordHighlighter({ text, currentWordIndex, onWordClick }: WordHighlighterProps) {\n  const words = text.split(/(\\s+)/)\n  let wordIndex = 0\n\n  return (\n    <div className=\"text-lg leading-relaxed text-gray-800 select-none\">\n      {words.map((segment, segmentIndex) => {\n        // Skip whitespace segments\n        if (/^\\s+$/.test(segment)) {\n          return <span key={segmentIndex}>{segment}</span>\n        }\n\n        const isCurrentWord = wordIndex === currentWordIndex\n        const currentIndex = wordIndex\n        wordIndex++\n\n        return (\n          <span\n            key={segmentIndex}\n            className={`cursor-pointer transition-colors duration-200 hover:bg-blue-100 px-1 py-0.5 rounded ${\n              isCurrentWord ? 'bg-blue-200 text-blue-900' : ''\n            }`}\n            onClick={(event) => onWordClick(segment, currentIndex, event)}\n          >\n            {segment}\n          </span>\n        )\n      })}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,gBAAgB,EAAE,IAAI,EAAE,gBAAgB,EAAE,WAAW,EAAwB;IAC3F,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,YAAY;IAEhB,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,SAAS;YACnB,2BAA2B;YAC3B,IAAI,QAAQ,IAAI,CAAC,UAAU;gBACzB,qBAAO,8OAAC;8BAAyB;mBAAf;;;;;YACpB;YAEA,MAAM,gBAAgB,cAAc;YACpC,MAAM,eAAe;YACrB;YAEA,qBACE,8OAAC;gBAEC,WAAW,CAAC,oFAAoF,EAC9F,gBAAgB,8BAA8B,IAC9C;gBACF,SAAS,CAAC,QAAU,YAAY,SAAS,cAAc;0BAEtD;eANI;;;;;QASX;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/TranslationCard.tsx"], "sourcesContent": ["import React from 'react'\nimport { Cross2Icon, SpeakerLoudIcon } from '@radix-ui/react-icons'\nimport { Typography } from '../ui/Typography'\nimport { IconButton } from '../ui/IconButton'\nimport { TranslationResult } from '@/lib/types'\n\ninterface TranslationCardProps {\n  word: string\n  translation?: TranslationResult\n  isLoading?: boolean\n  onPlayAudio: () => void\n  onClose: () => void\n}\n\nexport function TranslationCard({\n  word,\n  translation,\n  isLoading = false,\n  onPlayAudio,\n  onClose\n}: TranslationCardProps) {\n  return (\n    <div className=\"bg-gray-50 rounded-xl p-4 mx-5 mb-6 relative shadow-sm border border-gray-200\">\n      <IconButton\n        icon={<Cross2Icon className=\"w-4 h-4\" />}\n        onClick={onClose}\n        variant=\"ghost\"\n        size=\"sm\"\n        className=\"absolute top-2 right-2\"\n        aria-label=\"Close translation\"\n      />\n      \n      <div className=\"flex items-center justify-between mb-3\">\n        <div>\n          <Typography.TranslationWord className=\"text-xl\">\n            {word}\n          </Typography.TranslationWord>\n          {translation?.pronunciation && (\n            <Typography.Caption className=\"block mt-1\">\n              /{translation.pronunciation}/\n            </Typography.Caption>\n          )}\n        </div>\n        <IconButton \n          icon={<SpeakerLoudIcon className=\"w-4 h-4\" />} \n          onClick={onPlayAudio}\n          variant=\"ghost\"\n          size=\"sm\"\n          aria-label=\"Play pronunciation\"\n        />\n      </div>\n      \n      {isLoading ? (\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n        </div>\n      ) : translation ? (\n        <>\n          <Typography.TranslationText className=\"block mb-3\">\n            {translation.translation}\n          </Typography.TranslationText>\n          \n          {translation.example && (\n            <div className=\"text-sm text-gray-600 border-t border-gray-200 pt-3\">\n              <Typography.Caption className=\"italic block mb-1\">\n                {translation.example.original}\n              </Typography.Caption>\n              <Typography.Caption className=\"block\">\n                {translation.example.translation}\n              </Typography.Caption>\n            </div>\n          )}\n        </>\n      ) : (\n        <Typography.Caption className=\"text-gray-500\">\n          Translation not available\n        </Typography.Caption>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAWO,SAAS,gBAAgB,EAC9B,IAAI,EACJ,WAAW,EACX,YAAY,KAAK,EACjB,WAAW,EACX,OAAO,EACc;IACrB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;gBAC5B,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,WAAU;gBACV,cAAW;;;;;;0BAGb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC,sIAAA,CAAA,aAAU,CAAC,eAAe;gCAAC,WAAU;0CACnC;;;;;;4BAEF,aAAa,+BACZ,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;;oCAAa;oCACvC,YAAY,aAAa;oCAAC;;;;;;;;;;;;;kCAIlC,8OAAC,sIAAA,CAAA,aAAU;wBACT,oBAAM,8OAAC,gLAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;wBACjC,SAAS;wBACT,SAAQ;wBACR,MAAK;wBACL,cAAW;;;;;;;;;;;;YAId,0BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;uBAEf,4BACF;;kCACE,8OAAC,sIAAA,CAAA,aAAU,CAAC,eAAe;wBAAC,WAAU;kCACnC,YAAY,WAAW;;;;;;oBAGzB,YAAY,OAAO,kBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,QAAQ;;;;;;0CAE/B,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gCAAC,WAAU;0CAC3B,YAAY,OAAO,CAAC,WAAW;;;;;;;;;;;;;6CAMxC,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;0BAAgB;;;;;;;;;;;;AAMtD", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/ReadingArea.tsx"], "sourcesContent": ["import React from 'react'\nimport { WordHighlighter } from './WordHighlighter'\nimport { TranslationCard } from './TranslationCard'\nimport { TranslationCardState } from '@/lib/types'\n\ninterface ReadingAreaProps {\n  content: string\n  currentWordIndex?: number\n  translationCard: TranslationCardState\n  onWordClick: (word: string, index: number, event: React.MouseEvent) => void\n  onTranslationClose: () => void\n  onTranslationPlayAudio: () => void\n}\n\nexport function ReadingArea({\n  content,\n  currentWordIndex,\n  translationCard,\n  onWordClick,\n  onTranslationClose,\n  onTranslationPlayAudio\n}: ReadingAreaProps) {\n  return (\n    <div className=\"flex-1 overflow-y-auto\">\n      <div className=\"max-w-sm mx-auto p-6\">\n        <div className=\"mb-6\">\n          <WordHighlighter\n            text={content}\n            currentWordIndex={currentWordIndex}\n            onWordClick={onWordClick}\n          />\n        </div>\n        \n        {translationCard.isVisible && (\n          <TranslationCard\n            word={translationCard.word}\n            translation={translationCard.translation}\n            isLoading={translationCard.isLoading}\n            onPlayAudio={onTranslationPlayAudio}\n            onClose={onTranslationClose}\n          />\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAYO,SAAS,YAAY,EAC1B,OAAO,EACP,gBAAgB,EAChB,eAAe,EACf,WAAW,EACX,kBAAkB,EAClB,sBAAsB,EACL;IACjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,kBAAe;wBACd,MAAM;wBACN,kBAAkB;wBAClB,aAAa;;;;;;;;;;;gBAIhB,gBAAgB,SAAS,kBACxB,8OAAC,8IAAA,CAAA,kBAAe;oBACd,MAAM,gBAAgB,IAAI;oBAC1B,aAAa,gBAAgB,WAAW;oBACxC,WAAW,gBAAgB,SAAS;oBACpC,aAAa;oBACb,SAAS;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/Slider.tsx"], "sourcesContent": ["import React from 'react'\nimport * as RadixSlider from '@radix-ui/react-slider'\n\ninterface SliderProps {\n  value: number[]\n  onValueChange: (value: number[]) => void\n  max?: number\n  min?: number\n  step?: number\n  className?: string\n  disabled?: boolean\n}\n\nexport function Slider({\n  value,\n  onValueChange,\n  max = 100,\n  min = 0,\n  step = 1,\n  className = '',\n  disabled = false,\n}: SliderProps) {\n  return (\n    <RadixSlider.Root\n      className={`relative flex items-center select-none touch-none w-full h-5 ${className}`}\n      value={value}\n      onValueChange={onValueChange}\n      max={max}\n      min={min}\n      step={step}\n      disabled={disabled}\n    >\n      <RadixSlider.Track className=\"bg-gray-200 relative grow rounded-full h-1\">\n        <RadixSlider.Range className=\"absolute bg-gray-900 rounded-full h-full\" />\n      </RadixSlider.Track>\n      <RadixSlider.Thumb\n        className=\"block w-4 h-4 bg-white border-2 border-gray-900 rounded-full hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50\"\n        aria-label=\"Volume\"\n      />\n    </RadixSlider.Root>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAYO,SAAS,OAAO,EACrB,KAAK,EACL,aAAa,EACb,MAAM,GAAG,EACT,MAAM,CAAC,EACP,OAAO,CAAC,EACR,YAAY,EAAE,EACd,WAAW,KAAK,EACJ;IACZ,qBACE,8OAAC,kKAAA,CAAA,OAAgB;QACf,WAAW,CAAC,6DAA6D,EAAE,WAAW;QACtF,OAAO;QACP,eAAe;QACf,KAAK;QACL,KAAK;QACL,MAAM;QACN,UAAU;;0BAEV,8OAAC,kKAAA,CAAA,QAAiB;gBAAC,WAAU;0BAC3B,cAAA,8OAAC,kKAAA,CAAA,QAAiB;oBAAC,WAAU;;;;;;;;;;;0BAE/B,8OAAC,kKAAA,CAAA,QAAiB;gBAChB,WAAU;gBACV,cAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/AudioPlayer.tsx"], "sourcesContent": ["import React from 'react'\nimport { PlayIcon, PauseIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Slider } from '../ui/Slider'\nimport { Typography } from '../ui/Typography'\nimport { AudioPlayerState } from '@/lib/types'\n\ninterface AudioPlayerProps {\n  state: AudioPlayerState\n  onPlayPause: () => void\n  onSeek: (time: number) => void\n  onVolumeChange: (volume: number) => void\n}\n\nfunction formatTime(seconds: number): string {\n  const minutes = Math.floor(seconds / 60)\n  const remainingSeconds = Math.floor(seconds % 60)\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`\n}\n\nexport function AudioPlayer({ state, onPlayPause, onSeek, onVolumeChange }: AudioPlayerProps) {\n  const { isPlaying, currentTime, duration, volume } = state\n\n  const handleProgressChange = (value: number[]) => {\n    onSeek(value[0])\n  }\n\n  const handleVolumeChange = (value: number[]) => {\n    onVolumeChange(value[0] / 100)\n  }\n\n  return (\n    <div className=\"bg-white border-t border-gray-200 p-4\">\n      {/* Progress Bar */}\n      <div className=\"mb-4\">\n        <Slider\n          value={[currentTime]}\n          onValueChange={handleProgressChange}\n          max={duration}\n          min={0}\n          step={0.1}\n          className=\"w-full\"\n        />\n        <div className=\"flex justify-between mt-2\">\n          <Typography.Micro>\n            {formatTime(currentTime)}\n          </Typography.Micro>\n          <Typography.Micro>\n            {formatTime(duration)}\n          </Typography.Micro>\n        </div>\n      </div>\n\n      {/* Controls */}\n      <div className=\"flex items-center justify-center\">\n        <IconButton\n          icon={isPlaying ? <PauseIcon className=\"w-6 h-6\" /> : <PlayIcon className=\"w-6 h-6\" />}\n          onClick={onPlayPause}\n          size=\"lg\"\n          className=\"bg-gray-900 text-white hover:bg-gray-800\"\n          aria-label={isPlaying ? 'Pause' : 'Play'}\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAUA,SAAS,WAAW,OAAe;IACjC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;IACrC,MAAM,mBAAmB,KAAK,KAAK,CAAC,UAAU;IAC9C,OAAO,GAAG,QAAQ,CAAC,EAAE,iBAAiB,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AACrE;AAEO,SAAS,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAoB;IAC1F,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAErD,MAAM,uBAAuB,CAAC;QAC5B,OAAO,KAAK,CAAC,EAAE;IACjB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO;4BAAC;yBAAY;wBACpB,eAAe;wBACf,KAAK;wBACL,KAAK;wBACL,MAAM;wBACN,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;0CAEd,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;0CACd,WAAW;;;;;;;;;;;;;;;;;;0BAMlB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;oBACT,MAAM,0BAAY,8OAAC,gLAAA,CAAA,YAAS;wBAAC,WAAU;;;;;+CAAe,8OAAC,gLAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAC1E,SAAS;oBACT,MAAK;oBACL,WAAU;oBACV,cAAY,YAAY,UAAU;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/story/PageNavigation.tsx"], "sourcesContent": ["import React from 'react'\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons'\nimport { IconButton } from '../ui/IconButton'\nimport { Typography } from '../ui/Typography'\n\ninterface PageNavigationProps {\n  currentPage: number\n  totalPages: number\n  onPreviousPage: () => void\n  onNextPage: () => void\n}\n\nexport function PageNavigation({ \n  currentPage, \n  totalPages, \n  onPreviousPage, \n  onNextPage \n}: PageNavigationProps) {\n  return (\n    <div className=\"flex items-center justify-between p-4 bg-white border-t border-gray-100\">\n      <IconButton\n        icon={<ChevronLeftIcon className=\"w-5 h-5\" />}\n        onClick={onPreviousPage}\n        variant=\"ghost\"\n        disabled={currentPage <= 1}\n        aria-label=\"Previous page\"\n      />\n      \n      <Typography.Caption className=\"text-gray-500\">\n        Page {currentPage} of {totalPages}\n      </Typography.Caption>\n      \n      <IconButton\n        icon={<ChevronRightIcon className=\"w-5 h-5\" />}\n        onClick={onNextPage}\n        variant=\"ghost\"\n        disabled={currentPage >= totalPages}\n        aria-label=\"Next page\"\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AASO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACU;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;gBACjC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;0BAGb,8OAAC,sIAAA,CAAA,aAAU,CAAC,OAAO;gBAAC,WAAU;;oBAAgB;oBACtC;oBAAY;oBAAK;;;;;;;0BAGzB,8OAAC,sIAAA,CAAA,aAAU;gBACT,oBAAM,8OAAC,gLAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;gBAClC,SAAS;gBACT,SAAQ;gBACR,UAAU,eAAe;gBACzB,cAAW;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 710, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/ui/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Typography } from './Typography'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>\n}\n\nexport class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback\n      return <FallbackComponent error={this.state.error} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-[200px] p-6 bg-red-50 rounded-lg border border-red-200\">\n      <Typography.Title className=\"text-red-800 mb-2\">\n        Something went wrong\n      </Typography.Title>\n      <Typography.Body className=\"text-red-600 text-center mb-4\">\n        {error?.message || 'An unexpected error occurred'}\n      </Typography.Body>\n      <button\n        onClick={resetError}\n        className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\n      >\n        Try again\n      </button>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeO,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;IACzD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACjD,qBAAO,8OAAC;gBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;QAChF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAA6C;IAC5F,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,aAAU,CAAC,KAAK;gBAAC,WAAU;0BAAoB;;;;;;0BAGhD,8OAAC,sIAAA,CAAA,aAAU,CAAC,IAAI;gBAAC,WAAU;0BACxB,OAAO,WAAW;;;;;;0BAErB,8OAAC;gBACC,SAAS;gBACT,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Gabrr/Polistory/src/components/screens/StoryReaderScreen.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useCallback } from 'react'\nimport { StoryHeader } from '../story/StoryHeader'\nimport { ReadingArea } from '../story/ReadingArea'\nimport { AudioPlayer } from '../story/AudioPlayer'\nimport { PageNavigation } from '../story/PageNavigation'\nimport { ErrorBoundary } from '../ui/ErrorBoundary'\nimport { useAudioPlayer } from '@/lib/hooks/useAudioPlayer'\nimport { Story, StoryReaderState, TranslationResult } from '@/lib/types'\n\ninterface StoryReaderScreenProps {\n  story: Story\n}\n\n// Mock translation function for now\nconst mockTranslate = async (word: string): Promise<TranslationResult> => {\n  // Simulate API delay\n  await new Promise(resolve => setTimeout(resolve, 500))\n  \n  // Mock translations for demo\n  const mockTranslations: Record<string, TranslationResult> = {\n    'plane': {\n      translation: 'avion',\n      pronunciation: 'a-vi-ɔ̃',\n      provider: 'mock',\n      example: {\n        original: 'I like riding on planes.',\n        translation: 'J\\'aime voyager en avion.'\n      }\n    },\n    'aircraft': {\n      translation: 'aéronef',\n      pronunciation: 'a-e-ʁo-nɛf',\n      provider: 'mock'\n    }\n  }\n  \n  return mockTranslations[word.toLowerCase().replace(/[.,!?]/, '')] || {\n    translation: `[${word}]`,\n    provider: 'mock'\n  }\n}\n\nexport function StoryReaderScreen({ story }: StoryReaderScreenProps) {\n  const [state, setState] = useState<StoryReaderState>({\n    currentWordIndex: -1,\n    selectedWord: undefined,\n    translationCard: {\n      isVisible: false,\n      word: '',\n      position: { x: 0, y: 0 },\n      translation: undefined,\n      isLoading: false\n    },\n    audioPlayer: {\n      isPlaying: false,\n      currentTime: 2.14, // Mock current time to match the image\n      duration: 7.35, // Mock duration to match the image\n      volume: 1.0\n    }\n  })\n\n  const handleWordClick = useCallback(async (word: string, index: number, event: React.MouseEvent) => {\n    // Close existing translation card if clicking the same word\n    if (state.translationCard.isVisible && state.translationCard.word === word) {\n      setState(prev => ({\n        ...prev,\n        translationCard: { ...prev.translationCard, isVisible: false }\n      }))\n      return\n    }\n\n    // Show loading state\n    setState(prev => ({\n      ...prev,\n      selectedWord: word,\n      translationCard: {\n        isVisible: true,\n        word,\n        position: { x: event.clientX, y: event.clientY },\n        translation: undefined,\n        isLoading: true\n      }\n    }))\n\n    try {\n      const translation = await mockTranslate(word)\n      setState(prev => ({\n        ...prev,\n        translationCard: {\n          ...prev.translationCard,\n          translation,\n          isLoading: false\n        }\n      }))\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        translationCard: {\n          ...prev.translationCard,\n          isLoading: false\n        }\n      }))\n    }\n  }, [state.translationCard.isVisible, state.translationCard.word])\n\n  const handleTranslationClose = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      translationCard: { ...prev.translationCard, isVisible: false }\n    }))\n  }, [])\n\n  const handlePlayPause = useCallback(() => {\n    setState(prev => ({\n      ...prev,\n      audioPlayer: {\n        ...prev.audioPlayer,\n        isPlaying: !prev.audioPlayer.isPlaying\n      }\n    }))\n  }, [])\n\n  const handleSeek = useCallback((time: number) => {\n    setState(prev => ({\n      ...prev,\n      audioPlayer: {\n        ...prev.audioPlayer,\n        currentTime: time\n      }\n    }))\n  }, [])\n\n  const handleVolumeChange = useCallback((volume: number) => {\n    setState(prev => ({\n      ...prev,\n      audioPlayer: {\n        ...prev.audioPlayer,\n        volume\n      }\n    }))\n  }, [])\n\n  return (\n    <ErrorBoundary>\n      <div className=\"flex flex-col h-screen bg-white\">\n        <StoryHeader\n          title={story.title}\n          chapter={story.chapter}\n          onBookmarkClick={() => console.log('Bookmark clicked')}\n          onMenuClick={() => console.log('Menu clicked')}\n        />\n        \n        <ReadingArea\n          content={story.content}\n          currentWordIndex={state.currentWordIndex}\n          translationCard={state.translationCard}\n          onWordClick={handleWordClick}\n          onTranslationClose={handleTranslationClose}\n          onTranslationPlayAudio={() => console.log('Play translation audio')}\n        />\n        \n        <PageNavigation\n          currentPage={24}\n          totalPages={120}\n          onPreviousPage={() => console.log('Previous page')}\n          onNextPage={() => console.log('Next page')}\n        />\n        \n        <AudioPlayer\n          state={state.audioPlayer}\n          onPlayPause={handlePlayPause}\n          onSeek={handleSeek}\n          onVolumeChange={handleVolumeChange}\n        />\n      </div>\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAeA,oCAAoC;AACpC,MAAM,gBAAgB,OAAO;IAC3B,qBAAqB;IACrB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;IAEjD,6BAA6B;IAC7B,MAAM,mBAAsD;QAC1D,SAAS;YACP,aAAa;YACb,eAAe;YACf,UAAU;YACV,SAAS;gBACP,UAAU;gBACV,aAAa;YACf;QACF;QACA,YAAY;YACV,aAAa;YACb,eAAe;YACf,UAAU;QACZ;IACF;IAEA,OAAO,gBAAgB,CAAC,KAAK,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI;QACnE,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACxB,UAAU;IACZ;AACF;AAEO,SAAS,kBAAkB,EAAE,KAAK,EAA0B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD,kBAAkB,CAAC;QACnB,cAAc;QACd,iBAAiB;YACf,WAAW;YACX,MAAM;YACN,UAAU;gBAAE,GAAG;gBAAG,GAAG;YAAE;YACvB,aAAa;YACb,WAAW;QACb;QACA,aAAa;YACX,WAAW;YACX,aAAa;YACb,UAAU;YACV,QAAQ;QACV;IACF;IAEA,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAc,OAAe;QACtE,4DAA4D;QAC5D,IAAI,MAAM,eAAe,CAAC,SAAS,IAAI,MAAM,eAAe,CAAC,IAAI,KAAK,MAAM;YAC1E,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;wBAAE,GAAG,KAAK,eAAe;wBAAE,WAAW;oBAAM;gBAC/D,CAAC;YACD;QACF;QAEA,qBAAqB;QACrB,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,cAAc;gBACd,iBAAiB;oBACf,WAAW;oBACX;oBACA,UAAU;wBAAE,GAAG,MAAM,OAAO;wBAAE,GAAG,MAAM,OAAO;oBAAC;oBAC/C,aAAa;oBACb,WAAW;gBACb;YACF,CAAC;QAED,IAAI;YACF,MAAM,cAAc,MAAM,cAAc;YACxC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;wBACf,GAAG,KAAK,eAAe;wBACvB;wBACA,WAAW;oBACb;gBACF,CAAC;QACH,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,iBAAiB;wBACf,GAAG,KAAK,eAAe;wBACvB,WAAW;oBACb;gBACF,CAAC;QACH;IACF,GAAG;QAAC,MAAM,eAAe,CAAC,SAAS;QAAE,MAAM,eAAe,CAAC,IAAI;KAAC;IAEhE,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,iBAAiB;oBAAE,GAAG,KAAK,eAAe;oBAAE,WAAW;gBAAM;YAC/D,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAClC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;oBACX,GAAG,KAAK,WAAW;oBACnB,WAAW,CAAC,KAAK,WAAW,CAAC,SAAS;gBACxC;YACF,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;oBACX,GAAG,KAAK,WAAW;oBACnB,aAAa;gBACf;YACF,CAAC;IACH,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,aAAa;oBACX,GAAG,KAAK,WAAW;oBACnB;gBACF;YACF,CAAC;IACH,GAAG,EAAE;IAEL,qBACE,8OAAC,yIAAA,CAAA,gBAAa;kBACZ,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,KAAK;oBAClB,SAAS,MAAM,OAAO;oBACtB,iBAAiB,IAAM,QAAQ,GAAG,CAAC;oBACnC,aAAa,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGjC,8OAAC,0IAAA,CAAA,cAAW;oBACV,SAAS,MAAM,OAAO;oBACtB,kBAAkB,MAAM,gBAAgB;oBACxC,iBAAiB,MAAM,eAAe;oBACtC,aAAa;oBACb,oBAAoB;oBACpB,wBAAwB,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAG5C,8OAAC,6IAAA,CAAA,iBAAc;oBACb,aAAa;oBACb,YAAY;oBACZ,gBAAgB,IAAM,QAAQ,GAAG,CAAC;oBAClC,YAAY,IAAM,QAAQ,GAAG,CAAC;;;;;;8BAGhC,8OAAC,0IAAA,CAAA,cAAW;oBACV,OAAO,MAAM,WAAW;oBACxB,aAAa;oBACb,QAAQ;oBACR,gBAAgB;;;;;;;;;;;;;;;;;AAK1B", "debugId": null}}]}