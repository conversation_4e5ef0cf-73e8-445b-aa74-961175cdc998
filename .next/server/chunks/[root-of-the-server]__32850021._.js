module.exports = {

"[project]/.next-internal/server/app/api/stories/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/types.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "StorySchema": ()=>StorySchema,
    "TranslationResultSchema": ()=>TranslationResultSchema
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-route] (ecmascript) <export * as z>");
;
const StorySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    title: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    chapter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    language: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    difficulty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'beginner',
        'intermediate',
        'advanced'
    ]),
    audioUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    wordTimings: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        word: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        startTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        endTime: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        confidence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
    })).optional()
});
const TranslationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    translation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    pronunciation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    confidence: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    provider: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    example: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        original: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        translation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
    }).optional()
});
}),
"[project]/src/lib/services/tts-service.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Text-to-Speech Service
// This service handles audio generation and word alignment
__turbopack_context__.s({
    "ttsService": ()=>ttsService
});
class TTSService {
    baseUrl = '/api/tts';
    async generateAudio(text, options = {}) {
        const response = await fetch(`${this.baseUrl}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text,
                ...options
            })
        });
        if (!response.ok) {
            throw new Error(`TTS generation failed: ${response.statusText}`);
        }
        return response.json();
    }
    async getAvailableVoices(language) {
        const params = language ? `?language=${language}` : '';
        const response = await fetch(`${this.baseUrl}/voices${params}`);
        if (!response.ok) {
            throw new Error(`Failed to fetch voices: ${response.statusText}`);
        }
        return response.json();
    }
    // For development: create a mock audio file with estimated timings
    async generateMockAudio(text) {
        // Simulate API delay
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        // Create estimated word timings (roughly 0.5 seconds per word)
        const words = text.split(/\s+/);
        let currentTime = 0;
        const wordTimings = words.map((word)=>{
            const duration = Math.max(0.3, word.length * 0.1) // Minimum 0.3s, scale with word length
            ;
            const timing = {
                word,
                startTime: currentTime,
                endTime: currentTime + duration,
                confidence: 0.8
            };
            currentTime += duration + 0.1; // Small pause between words
            return timing;
        });
        // For now, return a mock result with Web Speech API synthesis
        return {
            audioUrl: await this.createWebSpeechAudio(text),
            duration: currentTime,
            wordTimings
        };
    }
    async createWebSpeechAudio(text) {
        return new Promise((resolve, reject)=>{
            if (!('speechSynthesis' in window)) {
                reject(new Error('Speech synthesis not supported'));
                return;
            }
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.rate = 0.8;
            utterance.pitch = 1.0;
            utterance.volume = 1.0;
            // Try to use a French voice if available
            const voices = speechSynthesis.getVoices();
            const frenchVoice = voices.find((voice)=>voice.lang.startsWith('fr') || voice.name.toLowerCase().includes('french'));
            if (frenchVoice) {
                utterance.voice = frenchVoice;
                utterance.lang = 'fr-FR';
            }
            // Create a data URL for the audio (this is a simplified approach)
            // In a real implementation, you'd record the audio and create a blob
            utterance.onend = ()=>{
                // For now, return a placeholder URL
                // In production, this would be a real audio file URL
                resolve('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
            };
            utterance.onerror = (event)=>{
                reject(new Error(`Speech synthesis failed: ${event.error}`));
            };
            speechSynthesis.speak(utterance);
        });
    }
}
const ttsService = new TTSService();
}),
"[project]/src/lib/services/story-processor.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "storyProcessor": ()=>storyProcessor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$tts$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/tts-service.ts [app-route] (ecmascript)");
;
class StoryProcessor {
    async processStory(story, options = {}) {
        const { voice, speed = 1.0, pitch = 1.0, language = 'fr-FR', generateAudio = true } = options;
        console.log(`Processing story: ${story.title}`);
        let audioUrl = '';
        let wordTimings = [];
        let duration = 0;
        if (generateAudio) {
            try {
                console.log('Generating audio...');
                const audioResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$tts$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ttsService"].generateMockAudio(story.content);
                audioUrl = audioResult.audioUrl;
                wordTimings = audioResult.wordTimings || [];
                duration = audioResult.duration;
                console.log(`Audio generated: ${duration}s, ${wordTimings.length} word timings`);
            } catch (error) {
                console.error('Audio generation failed:', error);
                // Continue without audio
                audioUrl = '';
                wordTimings = this.createFallbackTimings(story.content);
                duration = this.estimateDuration(story.content);
            }
        } else {
            // Create estimated timings without audio
            wordTimings = this.createFallbackTimings(story.content);
            duration = this.estimateDuration(story.content);
        }
        return {
            ...story,
            audioUrl,
            wordTimings,
            processingMetadata: {
                processedAt: new Date().toISOString(),
                audioGenerated: generateAudio && audioUrl !== '',
                voice,
                duration
            }
        };
    }
    createFallbackTimings(text) {
        const words = text.split(/\s+/);
        let currentTime = 0;
        return words.map((word)=>{
            const cleanWord = word.replace(/[.,!?;:]/, '');
            const duration = Math.max(0.3, cleanWord.length * 0.08);
            const timing = {
                word: cleanWord,
                startTime: parseFloat(currentTime.toFixed(2)),
                endTime: parseFloat((currentTime + duration).toFixed(2)),
                confidence: 0.7 // Lower confidence for estimated timings
            };
            currentTime += duration + 0.1;
            return timing;
        });
    }
    estimateDuration(text) {
        // Rough estimation: average reading speed is about 200 words per minute
        // For language learning, we slow it down to about 120 words per minute
        const words = text.split(/\s+/).length;
        const wordsPerMinute = 120;
        return words / wordsPerMinute * 60;
    }
    async reprocessStoryAudio(story, options = {}) {
        console.log(`Reprocessing audio for story: ${story.title}`);
        try {
            const audioResult = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$tts$2d$service$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ttsService"].generateMockAudio(story.content);
            return {
                ...story,
                audioUrl: audioResult.audioUrl,
                wordTimings: audioResult.wordTimings || story.wordTimings || [],
                processingMetadata: {
                    processedAt: new Date().toISOString(),
                    audioGenerated: true,
                    voice: options.voice,
                    duration: audioResult.duration
                }
            };
        } catch (error) {
            console.error('Audio reprocessing failed:', error);
            throw new Error(`Failed to reprocess audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    validateStory(story) {
        const errors = [];
        if (!story.title || story.title.trim().length === 0) {
            errors.push('Title is required');
        }
        if (!story.content || story.content.trim().length === 0) {
            errors.push('Content is required');
        }
        if (story.content && story.content.length > 10000) {
            errors.push('Content is too long (maximum 10,000 characters)');
        }
        if (!story.language || story.language.trim().length === 0) {
            errors.push('Language is required');
        }
        if (!story.difficulty || ![
            'beginner',
            'intermediate',
            'advanced'
        ].includes(story.difficulty)) {
            errors.push('Valid difficulty level is required (beginner, intermediate, or advanced)');
        }
        return errors;
    }
}
const storyProcessor = new StoryProcessor();
}),
"[externals]/fs/promises [external] (fs/promises, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs/promises", () => require("fs/promises"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/app/api/stories/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-route] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/types.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$story$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/services/story-processor.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs/promises [external] (fs/promises, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
;
;
;
;
const CreateStorySchema = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$types$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["StorySchema"].omit({
    id: true,
    audioUrl: true,
    wordTimings: true
}).extend({
    generateAudio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().default(true),
    voice: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    speed: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0.1).max(2.0).optional(),
    pitch: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0.1).max(2.0).optional()
});
async function GET() {
    try {
        const storiesPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'src/data/stories.json');
        const storiesData = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].readFile(storiesPath, 'utf-8');
        const stories = JSON.parse(storiesData);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(stories);
    } catch (error) {
        console.error('Error reading stories:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to load stories'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const storyData = CreateStorySchema.parse(body);
        // Generate unique ID
        const id = `story-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        // Validate story content
        const validationErrors = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$story$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["storyProcessor"].validateStory(storyData);
        if (validationErrors.length > 0) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Validation failed',
                details: validationErrors
            }, {
                status: 400
            });
        }
        // Process the story (generate audio, word timings, etc.)
        const processedStory = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$services$2f$story$2d$processor$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["storyProcessor"].processStory({
            ...storyData,
            id
        }, {
            generateAudio: storyData.generateAudio,
            voice: storyData.voice,
            speed: storyData.speed,
            pitch: storyData.pitch,
            language: storyData.language
        });
        // Load existing stories
        const storiesPath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'src/data/stories.json');
        let stories = [];
        try {
            const storiesData = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].readFile(storiesPath, 'utf-8');
            stories = JSON.parse(storiesData);
        } catch (error) {
            // File doesn't exist or is empty, start with empty array
            console.log('Creating new stories file');
        }
        // Add new story
        stories.push(processedStory);
        // Save back to file
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs$2f$promises__$5b$external$5d$__$28$fs$2f$promises$2c$__cjs$29$__["default"].writeFile(storiesPath, JSON.stringify(stories, null, 2));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(processedStory, {
            status: 201
        });
    } catch (error) {
        console.error('Story creation error:', error);
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid story data',
                details: error.errors
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to create story'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__32850021._.js.map